import Configs from "../MoveScript/Configs";
import App from "./Script/common/App";
import Http from "../MoveScript/Http";
import { EmailSecureForm } from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Email";
import { SmsSecureForm } from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm.Sms";
import { Global } from "../../Loading/src/Global";
// import { SecurityFlag } from "./Lobby.PopupSecurity";

const { ccclass, property } = cc._decorator;

@ccclass
export class TabSecurityInfoSecureForm extends cc.Component {
    @property(cc.Node)
    getOtpSecureView: cc.Node = null;

    @property(cc.Label)
    lblAccount: cc.Label = null;

    @property(cc.Label)
    lblEmail: cc.Label = null;

    @property(cc.Label)
    lblMobile: cc.Label = null;

    @property(cc.Label)
    lblNickname: cc.Label = null;

    @property(cc.Toggle)
    changeEmailToggle: cc.Toggle = null;

    @property(cc.Toggle)
    changeSmsToggle: cc.Toggle = null;

    @property(cc.Node)
    editEmail: cc.Node = null;

    @property(cc.Node)
    buttonEmail: cc.Node = null;

    @property(cc.Node)
    buttonActiveEmail: cc.Node = null;

    @property(cc.Node)
    buttonSaveEmail: cc.Node = null;

    @property(cc.Node)
    buttonChangeEmail: cc.Node = null;

    @property(cc.Node)
    verifiedEmail: cc.Node = null;

    @property(cc.Node)
    editMobile: cc.Node = null;

    @property(cc.Node)
    buttonMobile: cc.Node = null;

    @property(cc.Node)
    buttonActiveMobile: cc.Node = null;

    @property(cc.Node)
    buttonSaveMobile: cc.Node = null;

    @property(cc.Node)
    buttonChangeMobile: cc.Node = null;

    @property(cc.Node)
    verifiedMobile: cc.Node = null;

    @property(cc.EditBox)
    edbTeleSafe: cc.EditBox = null;

    @property(EmailSecureForm)
    emailSecureForm: EmailSecureForm = null;

    @property(SmsSecureForm)
    smsSecureForm: SmsSecureForm = null;

    @property(cc.Node)
    btnCapNhat: cc.Node = null;

    private _openChangeEmail: boolean = false;
    private _openChangeMobile: boolean = false;


    protected onLoad() {
        this.buttonChangeEmail.on('toggle', () => {
            this._openChangeEmail = !this._openChangeEmail;
            this.updateUI();
        })

        this.buttonChangeMobile.on('toggle', () => {
            this._openChangeMobile = !this._openChangeMobile;
            this.updateUI();
        })

        this.updateUI();
        this.updateVerifiedUI();
        this.emailSecureForm.lblMail.string = Configs.Login.Mail;
        this.smsSecureForm.lblMobile.string = Configs.Login.MobilePhone;
    }

    protected start() {
        this.lblAccount.string = Configs.Login.Username;
        this.lblEmail.string = Configs.Login.Mail;
        this.lblMobile.string = Configs.Login.MobilePhone;
        this.lblNickname.string = Configs.Login.Nickname;

        this.getOtpSecureView.getChildByName("account").getComponentInChildren(cc.Label).string =
            Configs.Login.Username;
        this.getOtpSecureView.getChildByName("nickname").getComponentInChildren(cc.Label).string =
            Configs.Login.Nickname;

        const isEmptyMail = Configs.Login.Mail === "";
        const isEmptyMobile = Configs.Login.MobilePhone === "";

        this.editEmail.active = isEmptyMail;
        this.buttonEmail.active = !isEmptyMail;

        this.editMobile.active = isEmptyMobile;
        this.buttonMobile.active = !isEmptyMobile;
    }


    updateVerifiedUI() {
        const status = Configs.Login.SecurityStatus;
        let verifiedEmail = [2,4,6,7].includes(status);
        let verifiedMobile = [1,4,5,7].includes(status);

        this.verifiedEmail.active = verifiedEmail;
        this.verifiedMobile.active = verifiedMobile;

        // in active button save and changed if verified
        this.buttonActiveEmail.active = !verifiedEmail;
        this.buttonActiveMobile.active = !verifiedMobile;

        this.buttonChangeEmail.active = !verifiedEmail;
        this.buttonChangeMobile.active = !verifiedMobile;

    }


    showOTPEmail() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["SendMailConfirm"];
        Http.post(url, {}, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                this.node.active = false;
                this.getOtpSecureView.active = true;
                this.smsSecureForm.node.active = false;
                this.emailSecureForm.node.active = true;
            }
        })
    }

    showOTPSMS() {
        this.node.active = false;
        this.getOtpSecureView.active = true;
        this.smsSecureForm.node.active = true;
        this.emailSecureForm.node.active = false;
    }

    private saveEmailButton() {
        let mobileEdb = this.editEmail.getComponent(cc.EditBox).string;
        this.updateAccountInfo({
            Email: mobileEdb.trim(),
        })
    }

    private saveSmsButton() {
        let newPhone = this.editMobile.getComponent(cc.EditBox).string
        this.updateAccountInfo({
            Mobile: newPhone.trim()
        })
    }

    private backToSecureForm() {
        this.node.active = true;
        this.getOtpSecureView.active = false;
    }

    private updateAccountInfo(payload: {}) {
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['UpdateAccountUrl'], payload, (status, res) => {
            App.instance.showLoading(false);
            if (res.c === -1023) {
                this.scheduleOnce(() => {
                    Global.LobbyController.logOut();
                }, 1);
            }
        })
    }

    updateSecurityInfo() {
        let emailEdb = this.editEmail.getComponent(cc.EditBox).string;
        let mobileEdb = this.editMobile.getComponent(cc.EditBox).string;
        let teleSafeEdb = this.edbTeleSafe.string;
        this.updateAccountInfo({
            Email: emailEdb.trim(),
            Mobile: mobileEdb.trim(),
            TeleSafe: teleSafeEdb.trim(),
        })
    }

    updateButtonUpdate() {
        this.btnCapNhat.active = !(this._openChangeEmail || this._openChangeMobile);
    }

    updateUI() {
        const cancel: string = App.instance.getTextLang("txt_cancel");
        const change: string = App.instance.getTextLang("se17");

        this.buttonActiveEmail.active = !this._openChangeEmail;
        this.buttonSaveEmail.active = this._openChangeEmail;
        this.buttonChangeEmail.getComponentInChildren(cc.Label).string = this._openChangeEmail ? cancel : change;

        this.buttonActiveMobile.active = !this._openChangeMobile;
        this.buttonSaveMobile.active = this._openChangeMobile;
        this.buttonChangeMobile.getComponentInChildren(cc.Label).string = this._openChangeMobile ? cancel : change;

        this.editEmail.getComponent(cc.EditBox).string = "";
        this.editMobile.getComponent(cc.EditBox).string = "";

        this.updateButtonUpdate();
    }


}