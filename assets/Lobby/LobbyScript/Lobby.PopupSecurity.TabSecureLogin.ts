import Configs from "../MoveScript/Configs";
import App from "./Script/common/App";
import Http from "../MoveScript/Http";
import { TabSecurityInfoSecureForm } from "./Lobby.PopupSecurity.TabSecurityInfo.SecureForm";
import BroadcastReceiver from "./Script/common/BroadcastReceiver";
// import { SecurityFlag } from "./Lobby.PopupSecurity";

const { ccclass, property } = cc._decorator;

@ccclass
export class TabSecureLogin extends cc.Component {
    @property(cc.Node)
    enableSecure: cc.Node = null;

    @property(cc.Node)
    disableSecure: cc.Node = null;

    @property(cc.Node)
    requireAuthen: cc.Node = null;

    private _isSecure: boolean;
    public get isSecure(): boolean {
        return this._isSecure;
    }
    public set isSecure(value: boolean) {
        this._isSecure = value;
        this.disableSecure.active = !value;
        this.enableSecure.active = value;
    }

    protected onLoad() {
        this.requireAuthen.active = false;
        this.disableSecure.getChildByName("button").on('click', () => {
            if (!this.isSecure) {
                this.requireAuthen.active = true;
            }
        })


    }

    /// 0: Chưa confirm gì
    /// 1: Confirm Mobile
    /// 2: Confirm Email
    /// 3: Confirm Telesafe
    /// 4: Confirm Email và Mobile
    /// 5: confirm Telesafe và Mobile
    /// 6: confirm Telesafe và Email
    /// 7: confirm Telesafe Email và Mobile
    protected start() {
        const status = Configs.Login.SecurityStatus;
        let hasSecure = status > 0;
        this.enableSecure.active = !!hasSecure;
        this.disableSecure.active = !hasSecure;
    }

    private registerSecureService() {
        App.instance.showLoading(true);
        Http.post(Configs.App.DOMAIN_CONFIG['SMSPlusCreateService'], {}, (status, res) => {
            App.instance.showLoading(false);
            if (status === 200) {
                App.instance.showLoading(false);
                this.disableSecure.active = false;
                this.enableSecure.active = true;
            }
        })
    }

    private getOtp() {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG["GetSmsOtp"];
        const payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "ServiceID": 14,
            "Username": Configs.Login.Username
        }
        Http.post(url, payload, (status, res) => {
            App.instance.showLoading(false);
            if (status == 200) {
                App.instance.alertDialog.showMsg(App.instance.getTextLang('se76'));
            }
        })
    }
}