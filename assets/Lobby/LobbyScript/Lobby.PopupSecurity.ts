import {Global} from "../../Loading/src/Global";
import Dialog from "./Script/common/Dialog";
import Configs from "../MoveScript/Configs";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PopupSecurity extends Dialog {
    @property(cc.ToggleContainer)
    tabs: cc.ToggleContainer = null;

    @property(cc.Node)
    tabContents: cc.Node = null;

    private tabSelectedIdx = 0;

    protected onLoad() {
        // log domain
        cc.log("domain config: ", Configs.App.DOMAIN_CONFIG);
        Global.PopupSecurity = this;
        this.tabs.toggleItems.forEach((tab, index) => {
            tab.node.on("toggle", () => {
                this.tabSelectedIdx = index;
                this.onTabChanged();
            })
        })
        this.onTabChanged();
    }

    private onTabChanged() {
        this.tabContents.children.forEach(tab => tab.active = false);
        switch (this.tabSelectedIdx) {
            case 0:
                this.tabContents.children[0].active = true;
                break;
            case 1:
                const status = Configs.Login.SecurityStatus;
                const verifiedMobile = [1,4,5,7].includes(status);

                this.tabContents.children[1].active = verifiedMobile;
                break;
            case 2:
                this.tabContents.children[2].active = true;
                break;
            case 3:
                this.tabContents.children[3].active = true;
                break;
            case 4:
                this.tabContents.children[4].active = true;
                break;
        }
    }
}
