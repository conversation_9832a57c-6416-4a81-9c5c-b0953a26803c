import Http from "../MoveScript/Http";
import Configs from "../MoveScript/Configs";

import PopupGiftCode from "./Lobby.PopupGiftCode";
import cmd from "./Lobby.Cmd";
import TabsListGame from "./Lobby.TabsListGame";
import PopupUpdateNickname from "./PopupUpdateNickname";
import PopupTransaction from "./Lobby.PopupTransaction";
import GameLiveController from "./GameLive/GameLiveController";
import PopupSecurity from "./Lobby.PopupSecurity";
import PopupDiemDanh from "./UIPopupDiemDanh";
import PopupMail from "./UIPopupMail";

import VersionConfig from "../MoveScript/VersionConfig";
import PopupDaiLy from "./Lobby.PopupDaiLy";
import Popupnaprut from "./Lobby.Popupnaprut";
import { Tophudata } from "./Lobby.ItemTopHu";
import TopHu from "./Lobby.TopHu";
import BundleControl from "../../Loading/src/BundleControl";
import LogEvent from "../../Loading/src/LogEvent/LogEvent";
import { Global } from "../../Loading/src/Global";
import PopupRegister from "./PopupRegister";
import PopupForgetPassword from "./Lobby.PopupForgetPassword";
import PopupTaiApp from "./Lobby.PopupTaiApp";
import PopupProfile from "./Lobby.PopupProfile";
import LobbyShop from "./Payment/LobbyShop";
import PopupCashout from "./Lobby.PopupCashout";
import PopupLogin from "./PopupLogin";
import App from "./Script/common/App";
import BroadcastReceiver from "./Script/common/BroadcastReceiver";
import AudioManager from "./Script/common/Common.AudioManager";
import SPUtils from "./Script/common/SPUtils";
import Tween from "./Script/common/Tween";
import Utils from "./Script/common/Utils";
import MiniGameNetworkClient from "./Script/networks/MiniGameNetworkClient";
import MiniGameNetworkClient2 from "./Script/networks/MiniGameNetworkClient2";
import InPacket from "./Script/networks/Network.InPacket";
import SamNetworkClient from "./Script/networks/SamNetworkClient";
import MauBinhNetworkClient from "../../MauBinh/MauBinhScript/MauBinh.NetworkClient";
import SlotNetworkClient from "./Script/networks/SlotNetworkClient";
import TienLenNetworkClient from "./Script/networks/TienLenNetworkClient";
import facebookSdk from "./Script/Service/FaceBook/Facebook";
import TienLenConstant from "./TienLenScript/TienLen.Room";
import BannerList from "./Lobby.BannerList";
import ShootFishNetworkClient from "./Script/networks/ShootFishNetworkClient";
import NetworkClient from "./Script/networks/Network.NetworkClient";
import TaiXiuSTNetworkClient from "./Script/networks/TaiXiuSieuToc.NetworkClient";
import PopupEvent from "./PopupEvent";
import PopupTopHu from "./Lobby.PopupTopHu";
import { PopupRefund } from "./Lobby.PopupRefund";
import PopupDaily from "./Lobby.PopupDiemDanh";
import PopupDiemDanh1 from "./Lobby.PopupDiemDanh";
import BoxLixi from "./Lobby.BoxLixi";
import PopupKiemTien from "./Lobby.PopupKiemTien";

import * as forge from "../../libs/forge";
import UtilsNative from "../MoveScript/UtilsNative";
import MiniGameSignalRClient from "./Script/networks/MiniGameSignalRClient";
import MiniGameTX1SignalRClient from "./Script/networks/MiniGameTX1SignalRClient";
import MiniGameTX2SignalRClient from "./Script/networks/MiniGameTX2SignalRClient";
import MiniGameTX3SignalRClient from "./Script/networks/MiniGameTX3SignalRClient";
import CardGameSignalRClient from "./Script/networks/CardGameSignalRClient";
import CaiShenSignalRClient from "./Script/networks/CaiShenSignalRClient";
import ForestSignalRClient from "./Script/networks/ForestSignalRClient";
import DragonTigerSignalRClient from "./Script/networks/DragonTigerSignalRClient";
import KingdomSignalRClient from "./Script/networks/KingdomSignalRClient";
import SedieSignalRClient from "./Script/networks/SedieSignalRClient";
import BaccaratSignalRClient from "./Script/networks/BaccaratSignalRClient";
import DQSignalRClient from "./Script/networks/DQSignalRClient";
import BlackjackSignalRClient from "./Script/networks/BlackjackSignalRClient";
import RouletteSignalRClient from "./Script/networks/RouletteSignalRClient";
import SicboSignalRClient from "./Script/networks/SicboSignalRClient";
import SignalRClient from "./Script/networks/Network.SignalRClient";
import MiniGameTXMD5SignalRClient from "./Script/networks/MiniGameTXMD5SignalRClient";
import OceanSignalRClient from "./Script/networks/OceanSignalRClient";
import OracleSignalRClient from "./Script/networks/OracleSignalRClient";

const { ccclass, property } = cc._decorator;
var _this = null;
var TW = cc.tween
@ccclass("Lobby.LobbyController.PanelMenu")
export class PanelMenu {
  @property(cc.Node)
  node: cc.Node = null;
  @property(cc.Node)
  bg: cc.Node = null;

  @property(cc.Toggle)
  toggleMusic: cc.Toggle = null;
  @property(cc.Toggle)
  toggleSound: cc.Toggle = null;

  private animate = false;
  start() {
    App.instance.isShowNotifyJackpot = true;
    this.toggleMusic.node.on("toggle", () => {
      SPUtils.setMusicVolumn(this.toggleMusic.isChecked ? 1 : 0);
      BroadcastReceiver.send(BroadcastReceiver.ON_AUDIO_CHANGED);
    });
    this.toggleSound.node.on("toggle", () => {
      SPUtils.setSoundVolumn(this.toggleSound.isChecked ? 1 : 0);
      BroadcastReceiver.send(BroadcastReceiver.ON_AUDIO_CHANGED);
    });

    this.toggleMusic.isChecked = SPUtils.getMusicVolumn() > 0;
    this.toggleSound.isChecked = SPUtils.getSoundVolumn() > 0;
    this.node.active = false;
    // App.instance.setUpNode();
  }

  show() {
    if (this.animate) return;
    this.animate = true;
    this.node.stopAllActions();
    this.node.active = true;
    cc.tween(this.bg)
      .set({ scale: 0.8, opacity: 150 })
      .to(0.3, { scale: 1.0, opacity: 255 }, { easing: cc.easing.backOut })
      .call(() => {
        this.animate = false;
      })
      .start();
  }

  hide() {
    this.node.stopAllActions();
    cc.tween(this.bg)
      .to(0.3, { scale: 0.8, opacity: 150 }, { easing: cc.easing.backIn })
      .call(() => {
        this.node.parent.active = false;
        this.animate = false;
      })
      .start();
  }
  dismiss() {
    if (this.animate) return;
    this.animate = true;
    cc.tween(this.bg)
      .to(0.3, { scale: 0.8, opacity: 150 }, { easing: cc.easing.backIn })
      .call(() => {
        this.node.parent.active = false;
        this.animate = false;
      })
      .start();
  }

  toggle() {
    if (this.node.active) {
      this.dismiss();
    } else {
      this.show();
    }
  }
}

namespace Lobby {
  @ccclass
  export class LobbyController extends cc.Component {
    @property(cc.EditBox)
    userName: cc.EditBox = null;
    @property(cc.EditBox)
    password: cc.EditBox = null;

    @property(cc.EditBox)
    userNameInBox: cc.EditBox = null;
    @property(cc.EditBox)
    passwordInBox: cc.EditBox = null;

    @property(cc.Node)
    nodeTop: cc.Node = null;
    @property(cc.Node)
    nodeWheel: cc.Node = null;
    @property(cc.Node)
    nodeRoom: cc.Node = null;
    @property(cc.Node)
    nodeBot: cc.Node = null;
    @property(cc.Node)
    nodeCenter: cc.Node = null;
    @property(cc.Label)
    txtMail: cc.Label = null;
    @property(cc.Label)
    txtMailz: cc.Label = null;
    @property(cc.Node)
    panelNotLogin: cc.Node = null;
    @property(cc.Node)
    panelCSKH: cc.Node = null;
    @property(cc.Node)
    bottomBarLeft: cc.Node = null;
    @property(cc.Node)
    bottomBarRight: cc.Node = null;
    @property(cc.Layout)
    layoutBtnLeft: cc.Layout = null;
    @property(cc.Layout)
    layoutLbLeft: cc.Layout = null;
    @property(cc.Layout)
    layoutBtnRight: cc.Layout = null;
    @property(cc.Layout)
    layoutLbRight: cc.Layout = null;

    @property(GameLiveController)
    gameLiveController: GameLiveController = null;
    @property(cc.Node)
    panelLogined: cc.Node = null;
    @property(PanelMenu)
    panelMenu: PanelMenu = null;

    @property(cc.Sprite)
    sprAvatar: cc.Sprite = null;
    @property(cc.Label)
    lblNickname: cc.Label = null;
    @property(cc.Label)
    lblVipPoint: cc.Label = null;
    @property(cc.Slider)
    sliderVipPoint: cc.Slider = null;
    @property(cc.Label)
    lblVipPointName: cc.Label = null;
    @property(cc.Sprite)
    spriteProgressVipPoint: cc.Sprite = null;
    @property(cc.Label)
    lblCoin: cc.Label = null;
    @property(cc.Label)
    lblGoldCoin: cc.Label = null;

    @property(cc.RichText)
    txtNotifyMarquee: cc.RichText = null;
    @property(cc.Node)
    bgNotify: cc.Node = null;

    @property(cc.Node)
    btnLoginFb: cc.Node = null;
    @property(cc.Node)
    buttonjb: cc.Node = null;
    @property(BoxLixi)
    boxLixi: BoxLixi = null;

    @property(TabsListGame)
    tabsListGame: TabsListGame = null;
    @property(BannerList)
    bannerList: BannerList = null;
    popupGiftCode: PopupGiftCode = null;
    popupUpdateNickname: PopupUpdateNickname = null;
    popupTransaction: PopupTransaction = null;
    popupTopHu: PopupTopHu = null;
    popupSecurity: PopupSecurity = null;
    popupKiemTien: PopupKiemTien = null;
    popupDiemDanh1: PopupDiemDanh1 = null;
    popupRefund: PopupRefund = null;
    popupEvent: PopupEvent = null;
    popupMail: PopupMail = null;
    popupDiemDanh: PopupDiemDanh = null;
    popupDaily: PopupDaiLy = null;
    Popupnaprut: Popupnaprut = null;
    popupRegister: PopupRegister = null;
    poupLogin: PopupLogin = null;
    popupForgetPassword: PopupForgetPassword = null;
    popupTaiApp: PopupTaiApp = null;
    popupProfile: PopupProfile = null;
    popupShop: LobbyShop = null;
    popupCashout: PopupCashout = null;



    @property({ type: cc.AudioClip })
    clipBgm: cc.AudioClip = null;
    listData100: Array<Tophudata> = new Array<Tophudata>();
    listData1000: Array<Tophudata> = new Array<Tophudata>();
    listData10000: Array<Tophudata> = new Array<Tophudata>();
    private static notifyMarquee = "";
    dataAlertMini: any = {};
    fakeJPInv = null;

    @property(cc.Node)
    panelMiniGameTipZo: cc.Node = null;
    @property(cc.Node)
    buttonMiniGameTipzo: cc.Node = null;
    @property(cc.Node)
    onMusic: cc.Node = null;
    @property(cc.Node)
    offMusic: cc.Node = null;

    @property(cc.Node)
    LoginBox: cc.Node = null;

    @property(cc.Node)
    bgLoginBox: cc.Node = null;

    @property(cc.Node)
    containerLoginBox: cc.Node = null;

    @property(cc.Node)
    jackpotx6: cc.Node = null;
    @property(cc.Node)
    jackpotX6List: cc.Node = null;
    @property(cc.Node)
    jackpotX6Content: cc.Node = null;

    @property([cc.Node])
    hoverCategory: cc.Node[] = [];


    @property(cc.Node)
    prefabContainer: cc.Node = null;

    @property(cc.SpriteFrame)
    logo_fortune: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    logo_kingdom: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    logo_ocean: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    logo_oracle: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    logo_dancingNight: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    logo_dancing: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    logo_forest: cc.SpriteFrame = null;


    //x6
    @property(cc.SpriteFrame)
    icon_x2: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    icon_x3: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    icon_x4: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    icon_x5: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    icon_x6: cc.SpriteFrame = null;



    private pingpongID: number = null;

    private isVisibleJackPotX6: boolean = false;
    private BILLIONGOLD: number = 1000000000;
    private BILLIONSLIVER: number = 10000000;

    onLoad() {
      Global.LobbyController = this;
      if (Global.DOMAIN_CONFIG) {
        Configs.App.DOMAIN_CONFIG = Global.DOMAIN_CONFIG;
        Configs.App.CONFIG88 = Global.DOMAIN_CONFIG_FULL_88;
      }
      if (Global.RsaPublicKey) {
        SPUtils.setRSAPublicKey(Global.RsaPublicKey);
      }
      this.nodeCenter.active = false;
      this.nodeTop.y = cc.winSize.height / 2 + this.nodeTop.height / 2;
      this.nodeBot.y = -cc.winSize.height / 2 - this.nodeBot.height;
      this.jackpotx6.active = false;

      this.hoverCategory.forEach((node) => {
        node.on(cc.Node.EventType.MOUSE_ENTER, () => this.onHoverEnter(node), this);
        node.on(cc.Node.EventType.MOUSE_LEAVE, () => this.onHoverLeave(node), this);
      });

      this.password.node.on('editing-return', this.onPasswordReturn, this);
      this.userName.node.on('editing-return', this.onPasswordReturn, this);
    }

    onPasswordReturn() {
      const username = this.userName.string.trim();
      const password = this.password.string.trim();

      if (username && password) {
        this.actLogin();
      }
    }

    onHoverEnter(node: cc.Node) {
      cc.tween(node).to(0.1, { scale: 1.1 }).start();
    }

    onHoverLeave(node: cc.Node) {
      cc.tween(node).to(0.1, { scale: 1 }).start();
    }

    start() {
      _this = this;
      let tileScreen = cc.winSize.width / 1280;
      this.bottomBarLeft.width = this.bottomBarLeft.width * tileScreen;
      this.bottomBarRight.width = this.bottomBarRight.width * tileScreen;

      this.panelMenu.start();

      BroadcastReceiver.register(
        BroadcastReceiver.USER_UPDATE_COIN,
        () => {
          Http.get(Configs.App.DOMAIN_CONFIG['GetAllBalance'], {}, (status, json) => {
            if (status === 200) {
              Configs.Login.GoldCoin = json['d'][0]['goldBalance'];
              Configs.Login.Coin = json['d'][0]['coinBalance'];

              const scale = (Configs.Login.Coin >= this.BILLIONSLIVER) ? 0.9 : 1.0;
              this.lblCoin.string = Configs.Login.Coin.toLocaleString("vi-VN");
              this.lblCoin.node.setScale(scale);

              const scaleGold = (Configs.Login.GoldCoin >= this.BILLIONGOLD) ? 0.9 : 1.0;
              this.lblGoldCoin.string = Configs.Login.GoldCoin.toLocaleString("vi-VN");
              this.lblGoldCoin.node.setScale(scaleGold);

            }
          });
        },
        this
      );

      BroadcastReceiver.register(
        BroadcastReceiver.USER_LOGGED_IN,
        () => {
          this.lblNickname.string = Configs.Login.Nickname;
          this.sprAvatar.spriteFrame = App.instance.getAvatarSpriteFrame(
            Configs.Login.Avatar
          );

          this.panelNotLogin.active = false;
          this.panelLogined.active = true;
          const scale = (Configs.Login.Coin >= this.BILLIONSLIVER) ? 0.9 : 1.0;
          this.lblCoin.string = Configs.Login.Coin.toLocaleString("vi-VN");
          this.lblCoin.node.setScale(scale);

          const scaleGold = (Configs.Login.GoldCoin >= this.BILLIONGOLD) ? 0.9 : 1.0;
          this.lblGoldCoin.string = Configs.Login.GoldCoin.toLocaleString("vi-VN");
          this.lblGoldCoin.node.setScale(scaleGold);

          Configs.Login.IsLogin = true;
          cc.systemEvent.emit('LOGIN_SUCCESS');
        },
        this
      );

      BroadcastReceiver.register(
        BroadcastReceiver.USER_INFO_REFRESH,
        (data: any) => {
          if (data) {
            this.refreshUserInfo(data);
          } else {
            Http.get(Configs.App.DOMAIN_CONFIG['GetAccountInfoUrl'], {}, (status, res) => {
              if (status === 200) {
                this.refreshUserInfo(res['d']);
              }
            });
          }
        },
        this
      );

      BroadcastReceiver.register(
        BroadcastReceiver.USER_LOGOUT,
        (data) => {
          this.handleLogout();
        },
        this
      );

      // AudioManager.getInstance().playBackgroundMusic(this.clipBgm);
      this.fetchAndDecryptMessage();

      // if(App.instance.isGotoFromTLMN){
      //   this.actGoToTLMN();
      //   App.instance.isGotoFromTLMN = false;
      // }
      // if (cc.sys.isMobile) {
      //   this.actCheckLogined();
      // }
    }

    refreshUserInfo(data: any) {
      if (!data) return;

      Configs.Login.UserId = data["accountID"];
      Configs.Login.Nickname = data["nickname"];
      Configs.Login.Username = data["username"];
      Configs.Login.Avatar = data["avatar"];
      Configs.Login.Coin = data["coinBalance"];
      Configs.Login.GoldCoin = data["goldBalance"];
      Configs.Login.IpAddress = data["clientIP"];
      Configs.Login.CreateTime = data["activatedTime"];
      Configs.Login.Birthday = data["birthday"];
      Configs.Login.VipPoint = data["vipPoint"];
      Configs.Login.VipPointLevel = data["vipLevel"];
      Configs.Login.ConfirmStatus = data['confirmStatus'] != '0';
      Configs.Login.SecurityStatus = data['securityStatus'];
      Configs.Login.CurrencyID = data['currencyID'];
      Configs.Login.Mail = data["email"];
      Configs.Login.MobilePhone = data["mobile"];
      Configs.Login.TeleSafe = data["teleSafe"];
      Configs.Login.PortalID = data["portalID"];
    }

    toggleMusic() {
      AudioManager.getInstance().toggleMusic();
      this.offMusic.active = this.onMusic.active;
      this.onMusic.active = !this.offMusic.active;
    }

    startEff() {
      this.nodeCenter.active = true;
      this.nodeTop.getComponent(cc.Widget).isAlignTop = true;
      this.setupListener();
      this.layoutBtnLeft.spacingX = 50 * (cc.winSize.width / 1280);
      this.layoutLbLeft.spacingX = 50 * (cc.winSize.width / 1280);

      this.layoutLbRight.spacingX = 50 * (cc.winSize.width / 1280);
      this.layoutBtnRight.spacingX = 50 * (cc.winSize.width / 1280);
      this.getConfigGame();
      cc.tween(this.nodeBot)
        .set({ y: -cc.winSize.height / 2, opacity: 150 })
        .to(
          0.3,
          { y: -cc.winSize.height / 2 + this.nodeBot.height / 2, opacity: 255 },
          { easing: cc.easing.sineIn }
        )
        .call(() => {
          this.nodeBot.getComponent(cc.Widget).isAlignBottom = true;
        })
        .start();
    }
    setupListener() {
      AudioManager.getInstance().playBackgroundMusic(this.clipBgm);
      if (!Configs.Login.IsLogin) {
        this.panelNotLogin.active = true;
        this.panelLogined.active = false;
        // App.instance.buttonMiniGame.hidden();
        App.instance.fakeTopHuData = {
          DUAXE: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          BITCOIN: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          THANTAI: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          CHIMDIEN: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          BIKINI: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          THETHAO: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          CHIEMTINH: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
          THANBAI: {
            j100: Utils.randomRangeInt(5000, 7000) * 100,
            j1000: Utils.randomRangeInt(5000, 7000) * 1000,
            j10000: Utils.randomRangeInt(5000, 7000) * 10000,
          },
        };
        this.initFakeJP();
        setInterval(
          (this.fakeJPInv = () => {
            if (!Configs.Login.IsLogin) {
              this.initFakeJP();
            } else {
              clearInterval(this.fakeJPInv);
            }
          }),
          5000
        );
      } else {
        this.panelNotLogin.active = false;
        this.panelLogined.active = true;
        BroadcastReceiver.send(BroadcastReceiver.USER_LOGGED_IN);
        SlotNetworkClient.getInstance().sendCheck(
          new cmd.ReqSubcribeHallSlot()
        );
        MiniGameNetworkClient.getInstance().sendCheck(new cmd.ReqGetMoneyUse());
      }
      MiniGameNetworkClient.getInstance().addListener((data) => {
        let inPacket = new InPacket(data);
        switch (inPacket.getCmdId()) {
          case cmd.Code.GET_SECURITY_INFO:
            App.instance.showLoading(false);
            let res = new cmd.ResGetSecurityInfo(data);
            // res.usertype = "2";
            Configs.Login.UserType = res.usertype;
            if (
              Configs.Login.UserType == "2" &&
              App.instance.RECONNECT_GAME == false
            ) {
              App.instance.updateConfigGame();
              this.reConnectGame();
            }
            break;
          case cmd.Code.NOTIFY_MARQUEE: {
            let res = new cmd.ResNotifyMarquee(data);
            let resJson = JSON.parse(res.message);
            LobbyController.notifyMarquee = "";
            this.dataAlertMini = resJson;
            this.showAlertMiniGame();
            break;
          }
          case cmd.Code.UPDATE_JACKPOTS: {
            let res = new cmd.ResUpdateJackpots(data);
            break;
          }
          case cmd.Code.GET_MONEY_USE: {
            let res = new cmd.ResGetMoneyUse(data);
            Configs.Login.Coin = res.moneyUse;
            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            break;
          }
          case cmd.Code.LOGOUT: {
            Utils.Log("Login from other places!");
            Global.isLoginFromOtherPlaces = true;
            MiniGameNetworkClient.getInstance().isForceClose = true;
            App.instance.ShowAlertDialog(
              App.instance.getTextLang("txt_login_from_other")
            );
            this.panelMenu.node.parent.active = false;
            this.panelMenu.hide();

            if (cc.sys.isBrowser) {
              window.localStorage.removeItem("u");
              window.localStorage.removeItem("at");
              window.localStorage.removeItem("at_fb");
              window.localStorage.removeItem("un");
              window.localStorage.removeItem("pw");
            }
            SPUtils.setUserName("");
            SPUtils.setUserPass("");
            cc.sys.localStorage.setItem("IsAutoLogin", 0);
            BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
            App.instance.updateConfigGame(App.DOMAIN);
            App.instance.RECONNECT_GAME = false;
            break;
          }
          case cmd.Code.LOGIN: {
            Utils.Log("Login Mini Game Success!");
            let res = new cmd.ResLogin(data);
            break;
          }
        }
      }, this);
      SlotNetworkClient.getInstance().addListener((data) => {
        let inPacket = new InPacket(data);
        switch (inPacket.getCmdId()) {
          case cmd.Code.UPDATE_JACKPOT_SLOTS: {
            let res = new cmd.ResUpdateJackpotSlots(data);
            let resJson = JSON.parse(res.pots);
            App.instance.topHuData = resJson;
            // Utils.Log("JP:", JSON.stringify(resJson));
            this.handleUpdateJP();
            break;
          }
        }
      }, this);
    }
    handleUpdateJP() {
      if (this.popupTopHu != null && this.popupTopHu.node.active) {
        this.popupTopHu.setInfo();
      }
      this.updateJackpot("THANTAI", "spartan");
      this.updateJackpot("DUAXE", "audition");
      this.updateJackpot("CHIEMTINH", "chiemtinh");
      this.updateJackpot("THETHAO", "maybach");
      this.updateJackpot("CHIMDIEN", "tamhung");
      this.updateJackpot("BITCOIN", "benley");
      this.updateJackpot("THANBAI", "rollRoye");
      this.updateJackpot("BIKINI", "bikini");
      this.updateJackpot("PIKACHU", "pokemon");
      this.updateJackpot("MINIPOKER", "minipoker");
    }
    updateJackpot(gameName, jackpotID) {
      let data = App.instance.topHuData[jackpotID];
      this.tabsListGame.updateItemJackpots(
        gameName,
        data["100"]["p"],
        data["100"]["x2"] == 1,
        data["1000"]["p"],
        data["1000"]["x2"] == 1,
        data["10000"]["p"],
        data["10000"]["x2"] == 1
      );
    }
    initFakeJP() {
      for (var key in App.instance.fakeTopHuData) {
        App.instance.fakeTopHuData[key]["j100"] += Utils.randomRangeInt(
          5000,
          20000
        );
        App.instance.fakeTopHuData[key]["j1000"] += Utils.randomRangeInt(
          50000,
          200000
        );
        App.instance.fakeTopHuData[key]["j10000"] += Utils.randomRangeInt(
          50000,
          2000000
        );
        this.tabsListGame.updateItemJackpots(
          key,
          App.instance.fakeTopHuData[key]["j100"],
          false,
          App.instance.fakeTopHuData[key]["j1000"],
          false,
          App.instance.fakeTopHuData[key]["j10000"],
          false
        ); //tay du
      }
    }
    showAlertMiniGame() {
      // let parent = this.txtNotifyMarquee.node.parent;
      let parent = this.txtNotifyMarquee.node.parent;
      parent.active = true;
      //<color=#00ff00>Rich</c><color=#0fffff>Text</color>
      let txtFormat =
        "(<color=#00ff00>%s</c>) " +
        App.instance.getTextLang("txt_congratualtion") +
        "<color=#FF7A00> %s </c>" +
        App.instance.getTextLang("txt_win") +
        "<color=#FFFF00> %s</c>        ";
      for (let i = 0; i < this.dataAlertMini["entries"].length; i++) {
        let e = this.dataAlertMini["entries"][i];
        LobbyController.notifyMarquee += cc.js.formatStr(
          txtFormat,
          Configs.GameId.getGameName(e["g"]),
          e["n"],
          Utils.formatNumber(e["m"])
        );
      }
      // this.txtNotifyMarquee.string = LobbyController.notifyMarquee;
      this.txtNotifyMarquee.string = LobbyController.notifyMarquee;
      this.txtNotifyMarquee.node.x = parent.width / 2;
      this.scheduleOnce(() => {
        this.bgNotify.active = true;
        let acMove = cc.tween().by(1.0, { x: -150 });
        let acCheck = cc.tween().call(() => {
          if (
            this.txtNotifyMarquee.node.x <
            -this.txtNotifyMarquee.node.width / 2 - parent.width / 2
          ) {
            cc.Tween.stopAllByTarget(this.txtNotifyMarquee.node);
            parent.active = false;
            // this.bgNotify.active = false;
          }
        });
        cc.Tween.stopAllByTarget(this.txtNotifyMarquee.node);
        cc.tween(this.txtNotifyMarquee.node)
          .repeatForever(cc.tween().sequence(acMove, acCheck))
          .start();
      }, 0.5);
    }
    reConnectGame() {
      Utils.Log("reconnectLote88");
      Utils.Log("TYPE_LOGIN:" + App.instance.TYPE_LOGIN);
      Utils.Log("USER_NAME:" + App.instance.USER_NAME);
      Utils.Log("PASS_WORD:" + App.instance.PASS_WORD);
      Utils.Log("FB_ID:" + App.instance.FB_ID);
      Utils.Log("AT_FB:" + App.instance.AT_FB);
      MiniGameNetworkClient.getInstance().close();
      SlotNetworkClient.getInstance().close();
      ShootFishNetworkClient.getInstance().close();
      if (App.instance.TYPE_LOGIN == "NORMAL") {
        this.actLogin(App.instance.USER_NAME, App.instance.PASS_WORD);
      }
    }

    initPluginFacebook() {
      // if ("undefined" == typeof sdkbox) {
      //   Utils.Log("sdkbox is undefined");
      //   return;
      // }

      // if ("undefined" == typeof sdkbox.PluginFacebook) {
      //   Utils.Log("sdkbox.PluginFacebook is undefined");
      //   return;
      // }

      // sdkbox.PluginFacebook.setListener({
      //   onLogin: function (isLogin, msg) {
      //     if (isLogin) {
      //       Configs.Login.AccessTokenFB =
      //         sdkbox.PluginFacebook.getAccessToken();
      //       _this.loginFB();
      //     } else {
      //       App.instance.showLoading(false);

      //       App.instance.showErrLoading("Lỗi đăng nhập status: " + msg);

      //       Utils.Log("login failed " + msg);
      //     }
      //   },
      // });
      // Utils.Log("initPluginFacebook success!");
      // sdkbox.PluginFacebook.init();
    }

    onEnable() {
      var self = this;
      // this.updateMail();
    }

    updateMail() {
      if (Configs.Login.IsLogin) {
        Http.get(
          Configs.App.API,
          { c: "406", nn: Configs.Login.Nickname },
          (err, res) => {
            if (res["success"]) {
              if (res["data"] > 0) {
                this.txtMail.node.parent.active = true;
                this.txtMailz.node.parent.active = true;
                this.txtMail.string = res["data"];
                if (!App.instance.checkMailUnread) {
                  App.instance.checkMailUnread = true;
                  App.instance.confirmDialog.show2(
                    App.instance.getTextLang("txt_new_mail"),
                    (isConfirm) => {
                      if (isConfirm) this.actEvent();
                    }
                  );
                }
              } else {
                this.txtMail.node.parent.active = false;
              }
            }
          }
        );
      }
    }

    initPluginFirebase() {
      // if ("undefined" == typeof sdkbox) {
      //   Utils.Log("sdkbox is undefined");
      //   return;
      // }
      // if ("undefined" == typeof sdkbox.firebase) {
      //   Utils.Log("sdkbox.firebase is undefined");
      //   return;
      // }
      // Utils.Log("SDKBOX FIREBASE OK!");
      // // sdkbox.firebase.Analytics.init();
      // sdkbox.firebase.Analytics.init();
    }
    onDestroy() {
      SlotNetworkClient.getInstance().send(new cmd.ReqUnSubcribeHallSlot());
      this.password.node.off('editing-return', this.onPasswordReturn, this);
      this.userName.node.off('editing-return', this.onPasswordReturn, this);

    }
    actLoginToken(data): void {
      Configs.Login.AccessToken = data.at;
      Configs.Login.AccessToken2 = data.at;
      App.instance.showLoading(true);
      Http.get(
        Configs.App.API,
        { c: 17, u: data.u, at: data.at },
        (err, res) => {
          App.instance.showLoading(false);

          if (err != null) {
            App.instance.alertDialog.showMsg(
              App.instance.getTextLang("txt_login_error")
            );
            return;
          }
          switch (parseInt(res["errorCode"])) {
            case 0:
              Configs.Login.AccessToken = res["accessToken"];
              if (cc.sys.isBrowser) {
                window.localStorage.setItem("at", Configs.Login.AccessToken);
              }
              Configs.Login.SessionKey = res["sessionKey"];
              Configs.Login.IsLogin = true;
              cc.log(Configs.Login.SessionKey)
              var userInfo = JSON.parse(
                base64.decode(Configs.Login.SessionKey)
              );
              Configs.Login.Nickname = userInfo["nickname"];
              Configs.Login.Avatar = userInfo["avatar"];
              Configs.Login.Username = userInfo["username"];
              let dataLogin: any = {};
              Configs.Login.Password = dataLogin.password =
                SPUtils.getUserPass();
              Configs.Login.Coin = userInfo["vinTotal"];
              Configs.Login.IpAddress = userInfo["ipAddress"];
              Configs.Login.CreateTime = userInfo["createTime"];
              Configs.Login.Birthday = userInfo["birthday"];
              Configs.Login.VipPoint = userInfo["vippoint"];
              Configs.Login.VipPointSave = userInfo["vippointSave"];
              Configs.Login.Mail = userInfo["email"];
              Configs.Login.MobilePhone = userInfo["mobile"];
              Configs.Login.TeleSafe = userInfo["teleSafe"];
              // khoi tao 3 socket dong thoi gui goi tin len server
              MiniGameNetworkClient.getInstance().sendCheck(
                new cmd.ReqSubcribeJackpots()
              );
              SlotNetworkClient.getInstance().sendCheck(
                new cmd.ReqSubcribeHallSlot()
              );
              if (cc.sys.isNative && cc.sys.os == cc.sys.OS_IOS) {
              } else {
                this.loginMiniGameSockJs();
              }
              //this.actShowBanner();
              // this.checkDiemDanh();
              this.checkListBankRut();
              this.boxLixi.getInfo();

              this.panelNotLogin.active = false;
              this.panelLogined.active = true;
              if (
                Global.PopupRegister != null &&
                Global.PopupRegister.node &&
                Global.PopupRegister.node.active
              ) {
                Global.PopupRegister.dismiss();
              }
              App.instance.buttonMiniGame.show();
              BroadcastReceiver.send(BroadcastReceiver.USER_LOGGED_IN);
              break;
            case 1109:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_account_blocked")
              );
              break;
            case 1114:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_room_err6")
              );
              break;
            case 1014:
            case 1015:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_session_end")
              );
              break;
            case 1002:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_account_err_captcha")
              );
              break;
            case 1007:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_account_name_not_the_same")
              );
              break;
            case 1021:
            case 1008:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_account_incorrect_otp")
              );
              break;
            case 2001:
              App.instance.showLoading(false);
              // App.instance.alertDialog.showMsg("Tên nhân vật không được để trống.");
              break;
            default:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                App.instance.getTextLang("txt_login_error")
              );
              break;
          }
        }
      );
    }
    checkDiemDanh() {
      Http.get(
        Configs.App.API,
        {
          c: "2031",
          nn: Configs.Login.Nickname,
          at: Configs.Login.AccessToken,
          ac: "get",
        },
        (err, res) => {
          if (res["success"] != null && res["success"] == true) {
            this.actDiemDanh1();
          } else {
          }
        }
      );
    }

    loginMiniGameSockJs() {
      let dataLogin: any = {};
      dataLogin.username = SPUtils.getUserName();
      dataLogin.password = SPUtils.getUserPass();
      dataLogin.rememberMe = true;
      Utils.Log("loginMiniGameSockJs:", dataLogin);
      Http.post(
        Configs.App.HOST_SOCKJS + "api/login",
        dataLogin,
        (err, res) => {
          if (err) {
            Utils.Log("err Login Tx:", err);
            return;
          }
          if (res != null && res.token != "") {
            Utils.Log("Login TXST Success:" + JSON.stringify(res));
            cc.sys.localStorage.setItem("token_Sockjs", res.token);
            Configs.Login.AccessTokenSockJs = res.token;
            TaiXiuSTNetworkClient.getInstance().isLogin = true;
            TaiXiuSTNetworkClient.getInstance().connect();
          }
        },
        true
      );
    }
    actRule() {
      App.instance.actRule();
      this.actMenu();
    }
    actComingSoon() {
      App.instance.alertDialog.showMsg(
        App.instance.getTextLang("txt_reparing")
      );
    }

    actLogin(username = null, password = null): void {
      if (username == null || password == null) {
        if (this.LoginBox.active) {
          username = this.userNameInBox.string.trim();
          password = this.passwordInBox.string.trim();
        } else {
          username = this.userName.string.trim();
          password = this.password.string.trim();
        }
      }

      if (username.length == 0) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_login_username_not_blank")
        );
        return;
      }

      if (password.length == 0) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_login_password_not_blank")
        );
        return;
      }

      const data = {
        Username: username,
        Password: password,
        Timestamp: UtilsNative.getTicks(),
        Md5Password: md5(password)
      }

      const json = {
        "Input": Utils.encryptWithRSA(JSON.stringify(data)),
      };

      // App.instance.showLoading(true);
      Http.post(
        Configs.App.DOMAIN_CONFIG['LoginUrl'],
        json,
        (status, res) => {
          if (this.pingpongID) {
            clearInterval(this.pingpongID);
          }
          this.pingpongID = setInterval(() => {
            this.pingpong();
          }, 60000);

          switch (parseInt(res["c"])) {
            case 0:
              // App.instance.showLoading(false);

              Configs.Login.IsLogin = true;
              SPUtils.setUserName(username);
              SPUtils.setUserPass(password);

              Configs.Login.AccessToken = res["m"];
              Configs.Login.SessionKey = res["d"]["sessionToken"];

              BroadcastReceiver.send(BroadcastReceiver.USER_INFO_REFRESH, res['d']);
              BroadcastReceiver.send(BroadcastReceiver.USER_LOGGED_IN);

              this.LoginBox.active = false;

              App.instance.taiXiuDouble = null;
              App.instance.miniPoker = null;
              App.instance.caoThap = null;
              App.instance.bauCua = null;
              App.instance.slot3x3 = null;
              App.instance.phucSinh = null;
              this.actVQMM();
              this.actGameTaiXiu();
              this.buttonMiniGameTipzo.active = true;

              Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], { type: 1 }, (status, res) => {
                try {

                  if (status === 200 && res.c == 0) {
                    if (+res?.d > 0) {
                      this.showVipWheel();
                    }
                  }

                } catch (error) {

                }
              });


              Http.get(Configs.App.DOMAIN_CONFIG["GetBannerEvent"], {}, (status, res) => {
                try {

                  if (status === 200 && res.c == 0) {
                    if (res.d.length > 0) {
                      this.showSlide(res.d);
                    }
                  }

                } catch (error) {

                }
              });
              Http.get(Configs.App.DOMAIN_CONFIG["GetPopupContentUrl"], {}, (status, res) => {
                try {

                  if (status === 200 && res.c == 0) {

                    if (res.d.length > 0) {
                      this.showAnnouncement(res.d);
                    }
                  }

                } catch (error) {

                }
              });


              break;
            case 2:
              App.instance.showLoading(false);
              // OTP
              break;
            default:
              App.instance.showLoading(false);
              App.instance.alertDialog.showMsg(
                res["r"]
              );
              break;
          }
        }
      );
    }

    isUseSDK() {
      if (cc.sys.isNative) {
        if (cc.sys.os == cc.sys.OS_ANDROID || cc.sys.os == cc.sys.OS_IOS) {
          return true;
        }
      }
      // if (cc.sys.os == cc.sys.OS_ANDROID) return true;
      // if (cc.sys.os == cc.sys.OS_IOS) return true;
      return false;
    }

    fbRespone(response) {
      if (response.status != "200") {
        if (response.response != "wait") {
          Utils.Log(JSON.stringify(response));
          App.instance.showLoading(false);
          App.instance.showErrLoading(
            "Lỗi đăng nhập status: " + response.status
          );
        }
      } else {
        Utils.Log("fbRespone:" + JSON.stringify(response));
        Configs.Login.AccessTokenFB =
          response.response.authResponse.accessToken;
        Configs.Login.FacebookID = response.response.authResponse.userID;
        _this.loginFB();
      }
    }

    actLoginFB() {
      // Utils.Log("actLoginFB");
      // App.instance.showLoading(true, -1);
      // if (_this.isUseSDK()) {
      //   if (sdkbox.PluginFacebook.isLoggedIn()) {
      //     Configs.Login.AccessTokenFB = sdkbox.PluginFacebook.getAccessToken();
      //     _this.loginFB();
      //   } else {
      //     Utils.Log("FB to Login");
      //     sdkbox.PluginFacebook.login(["public_profile", "email"]);
      //   }
      // } else {
      //   let Appid = "758971848112749";
      //   let scope = "email,public_profile";
      //   if (_this.sdk != null) {
      //     Utils.Log("Login fb web");
      //     try {
      //       FB.getLoginStatus((data) => {
      //         if (data.status === "connected") {
      //           Configs.Login.AccessTokenFB = data.authResponse.accessToken;
      //           Configs.Login.FacebookID = data.authResponse.userID;
      //           Utils.Log(
      //             "Configs.Login.AccessTokenFB auth:" + JSON.stringify(data)
      //           );
      //           _this.loginFB();
      //         } else if (data.status === "not_authorized") {
      //           App.instance.showLoading(false);
      //           // App.instance.showErrLoading("Lỗi đăng nhập status: " + data.status);
      //         } else {
      //           FB.login(_this.fbRespone, { scope: scope });
      //         }
      //       });
      //     } catch (e) {
      //       App.instance.showLoading(false);
      //       // App.instance.showErrLoading("Lỗi đăng nhập status: " + e.message);
      //     }
      //   } else {
      //     _this.sdk = new facebookSdk(Appid, scope, _this.fbRespone);
      //   }
      // }
    }
    actShareFbLink(link) {
      // // sdkbox.FBShareInfo;
      // // sdkbox.PluginFacebook.share
      // FB.ui(
      //   {
      //     display: "popup",
      //     method: "share",
      //     href: link,
      //     caption: "Làm giàu cùng Vua88",
      //   },
      //   function (response) {
      //     //   console.log("Respone FB:" + JSON.stringify(response));
      //   }
      // );
    }
    loginFB() {
      // Configs.Login.AccessToken = Configs.Login.AccessTokenFB;
      // let accessToken = Configs.Login.AccessTokenFB;
      // App.instance.showLoading(true);
      // Utils.Log("accessTokenFB:" + accessToken);
      // Http.get(
      //   Configs.App.API,
      //   { c: 3, s: "fb", at: accessToken },
      //   (err, res) => {
      //     App.instance.showLoading(false);
      //     Utils.Log(
      //       "loginFB failed:" +
      //         JSON.stringify(err) +
      //         " => " +
      //         JSON.stringify(res)
      //     );
      //     if (err != null) {
      //       App.instance.alertDialog.showMsg(
      //         App.instance.getTextLang("txt_login_error")
      //       );
      //       return;
      //     }
      //     Utils.Log("login Fb result:" + JSON.stringify(res));
      //     switch (parseInt(res["errorCode"])) {
      //       case 0:
      //         LogEvent.getInstance().sendEventLogin("facebook");
      //         Configs.Login.AccessToken = res["accessToken"];
      //         Configs.Login.SessionKey = res["sessionKey"];

      //         Configs.Login.IsLogin = true;
      //         var userInfo = JSON.parse(
      //           base64.decode(Configs.Login.SessionKey)
      //         );
      //         Configs.Login.Nickname = userInfo["nickname"];
      //         Configs.Login.Avatar = userInfo["avatar"];
      //         Configs.Login.Coin = userInfo["vinTotal"];
      //         Configs.Login.IpAddress = userInfo["ipAddress"];
      //         Configs.Login.CreateTime = userInfo["createTime"];
      //         Configs.Login.Birthday = userInfo["birthday"];
      //         Configs.Login.Birthday = userInfo["birthday"];
      //         Configs.Login.VipPoint = userInfo["vippoint"];
      //         Configs.Login.VipPointSave = userInfo["vippointSave"];
      //         Configs.Login.Username = userInfo["username"];
      //         Utils.Log("FacebookID=" + Configs.Login.FacebookID);

      //         // khoi tao 3 socket dong thoi gui goi tin len server
      //         MiniGameNetworkClient.getInstance().sendCheck(
      //           new cmd.ReqSubcribeJackpots()
      //         );
      //         SlotNetworkClient.getInstance().sendCheck(
      //           new cmd.ReqSubcribeHallSlot()
      //         );

      //         this.panelNotLogin.active = false;
      //         this.panelLogined.active = true;

      //         SPUtils.setUserName(Configs.Login.Username);
      //         SPUtils.setUserPass(Configs.Login.Password);

      //         App.instance.buttonMiniGame.show();
      //         BroadcastReceiver.send(BroadcastReceiver.USER_INFO_UPDATED);

      //         break;
      //       case 1109:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(
      //           App.instance.getTextLang("txt_login_account_blocked")
      //         );
      //         break;
      //       case 1114:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(
      //           App.instance.getTextLang("txt_login_account_not_exsist")
      //         );
      //         break;
      //       case 1114:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(
      //           App.instance.getTextLang("txt_login_account_not_get_info")
      //         );
      //         break;
      //       case 1002:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(
      //           App.instance.getTextLang("txt_login_account_incorrect_otp")
      //         );
      //         break;
      //       case 1007:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(App.instance.getTextLang(""));
      //         break;
      //       case 1021:
      //       case 1008:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(
      //           App.instance.getTextLang("txt_login_account_incorrect_otp")
      //         );
      //         break;
      //       case 2001:
      //         App.instance.showLoading(false);
      //         if (!App.instance.popupUpdateNickname) {
      //           let cb = (prefab) => {
      //             let popupDaily = cc
      //               .instantiate(prefab)
      //               .getComponent("PopupUpdateNickname");
      //             App.instance.node.addChild(popupDaily.node);
      //             App.instance.popupUpdateNickname = popupDaily;
      //             App.instance.popupUpdateNickname.showFb(accessToken);
      //           };
      //           BundleControl.loadPrefabPopup(
      //             "PrefabPopup/PopupUpdateNickname",
      //             cb
      //           );
      //         } else {
      //           App.instance.popupUpdateNickname.showFb(accessToken);
      //         }
      //         break;
      //       default:
      //         App.instance.showLoading(false);
      //         App.instance.alertDialog.showMsg(
      //           App.instance.getTextLang("txt_login_error")
      //         );
      //         break;
      //     }
      //   }
      // );
    }

    actCloseLoginForm() {
      this.bgLoginBox.stopAllActions();
      this.bgLoginBox.opacity = 128;
      this.bgLoginBox.runAction(cc.fadeOut(0.2));

      this.containerLoginBox.stopAllActions();
      this.containerLoginBox.opacity = 255;
      this.containerLoginBox.scale = 1;

      TW(this.containerLoginBox).to(0.3, { scale: 0.8, opacity: 150 }, { easing: cc.easing.backIn })
        .call(() => {
          this._onDismissed();
        })
        .start();
    }

    _onDismissed() {
      var edits = this.containerLoginBox.getComponentsInChildren(cc.EditBox);

      for (var i = 0; i < edits.length; i++) {
        edits[i].tabIndex = -1;
      }
      this.bgLoginBox.opacity = 210;
      this.containerLoginBox.opacity = 255;
      this.containerLoginBox.scale = 1;

      let editsOutside = this.node.parent.getComponentsInChildren(cc.EditBox);
      for (let i = 0; i < editsOutside.length; i++) {
        editsOutside[i].tabIndex = i + 1;
      }

      this.userNameInBox.string = "";
      this.passwordInBox.string = "";

      this.LoginBox.active = false;
    }

    actMenu() {
      if (this.panelMenu.node.parent.active == false) {
        this.panelMenu.node.parent.active = true;
      } else {
        // this.panelMenu.node.parent.active = false;
        this.panelMenu.dismiss();
      }
      this.panelMenu.show();
    }
    atcPopupUpdateNickName(username, password) {
      Utils.Log("atcPopupUpdateNickName");
      let cb = (prefab) => {
        let popupDaily = cc
          .instantiate(prefab)
          .getComponent("PopupUpdateNickname");
        App.instance.node.addChild(popupDaily.node);
        this.popupUpdateNickname = popupDaily;
        this.popupUpdateNickname.show2(username, password);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupUpdateNickNameNew", cb);
    }
    actLoginRegister(even, data) {

      let cb = (prefab) => {
        let popupRegister = cc
          .instantiate(prefab)
          .getComponent("PopupRegister");
        App.instance.node.addChild(popupRegister.node);
        this.popupRegister = popupRegister;
        this.popupRegister.show(null, data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupRegisterNew", cb);
      // if (!this.popupRegister) {
      //   let cb = (prefab) => {
      //     let popupRegister = cc
      //       .instantiate(prefab)
      //       .getComponent("PopupRegister");
      //     App.instance.node.addChild(popupRegister.node);
      //     this.popupRegister = popupRegister;
      //     this.popupRegister.show(null, data);
      //   };
      //   BundleControl.loadPrefabPopup("PrefabPopup/PopupRegister", cb);
      // } else {
      //   this.popupRegister.show(null, data);
      // }
    }
    actLoginPopup(even, data) {
      // if (!this.poupLogin) {
      //   let cb = (prefab) => {
      //     let popupLogin = cc.instantiate(prefab).getComponent("PopupLogin");
      //     App.instance.node.addChild(popupLogin.node);
      //     this.popupLogin = popupLogin;
      //     this.popupLogin.show(null, data);
      //   };
      //   BundleControl.loadPrefabPopup("PrefabPopup/PopupLogin", cb);
      // } else {
      //   this.popupLogin.show(null, data);
      // }
    }
    actDaiLy() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupDaily) {
        let cb = (prefab) => {
          let popupDaily = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupDaiLy");
          App.instance.node.addChild(popupDaily.node);
          this.popupDaily = popupDaily;
          this.popupDaily.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupDaiLy", cb);
      } else {
        this.popupDaily.show();
      }
    }
    actTopHu() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupTopHu) {
        let cb = (prefab) => {
          let popupTopHu = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupTopHu");
          App.instance.node.addChild(popupTopHu.node);
          this.popupTopHu = popupTopHu;
          this.popupTopHu.show();
          this.popupTopHu.setInfo();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupTopHu", cb);
      } else {
        this.popupTopHu.show();
        this.popupTopHu.setInfo();
      }
    }
    actTransaction() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupTransaction) {
        let cb = (prefab) => {
          let popupDaily = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupTransaction");
          App.instance.node.addChild(popupDaily.node);
          this.popupTransaction = popupDaily;
          this.popupTransaction.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupTransaction", cb);
      } else {
        this.popupTransaction.show();
      }
    }
    actForgetPassword() {
      let isLoaded = false;
      if (!isLoaded) {
        if (!this.popupForgetPassword) {
          let cb = (prefab) => {
            let popupForgetPassword = cc
              .instantiate(prefab)
              .getComponent("Lobby.PopupForgetPassword");
            App.instance.node.addChild(popupForgetPassword.node);
            this.popupForgetPassword = popupForgetPassword;
            this.popupForgetPassword.show();
            isLoaded = true;
          };
          BundleControl.loadPrefabPopup("PrefabPopup/PopupForgetPassword", cb);
        } else {
          this.popupForgetPassword.show();
        }
      }
    }
    actTaiApp() {
      if (!this.popupTaiApp) {
        let cb = (prefab) => {
          let popupTaiApp = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupTaiApp");
          App.instance.node.addChild(popupTaiApp.node);
          this.popupTaiApp = popupTaiApp;
          this.popupTaiApp.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupTaiApp", cb);
      } else {
        this.popupTaiApp.show();
      }
    }
    actnaprut() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.Popupnaprut) {
        let cb = (prefab) => {
          let popupnaprut = cc
            .instantiate(prefab)
            .getComponent("Lobby.Popupnaprut");
          App.instance.node.addChild(popupnaprut.node);
          this.Popupnaprut = popupnaprut;
          this.Popupnaprut.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/Popupnap-rut-doi", cb);
      } else {
        this.Popupnaprut.show();
      }
    }
    actGiftCode() {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let popupGiftCode = cc
          .instantiate(prefab)
          .getComponent("Lobby.PopupGiftCode");
        popupGiftCode.node.parent = App.instance.node;
        // App.instance.node.addChild(popupGiftCode.node)
        Utils.Log(
          "parent giftcode:" +
          popupGiftCode.node.parent.name +
          ":" +
          App.instance.node.name
        );
        this.popupGiftCode = popupGiftCode;
        this.popupGiftCode.show();
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupGiftCode", cb);
      // if (!Configs.Login.IsLogin) {
      //   App.instance.alertDialog.showMsg(
      //     App.instance.getTextLang("txt_need_login")
      //   );
      //   return;
      // }
      // if (!this.popupGiftCode) {
      //   let cb = (prefab) => {
      //     let popupGiftCode = cc
      //       .instantiate(prefab)
      //       .getComponent("Lobby.PopupGiftCode");
      //     popupGiftCode.node.parent = App.instance.node;
      //     // App.instance.node.addChild(popupGiftCode.node)
      //     Utils.Log(
      //       "parent giftcode:" +
      //         popupGiftCode.node.parent.name +
      //         ":" +
      //         App.instance.node.name
      //     );
      //     this.popupGiftCode = popupGiftCode;
      //     this.popupGiftCode.show();
      //   };
      //   BundleControl.loadPrefabPopup("PrefabPopup/PopupGiftCode", cb);
      // } else {
      //   this.popupGiftCode.show();
      // }
    }
    actPromotion() {
      //cmd=2015&nn=brightc&at=dfasfrfsefs9f9sfsdfdsds
      Http.get(
        Configs.App.API,
        { c: 2015, nn: Configs.Login.Nickname, at: Configs.Login.AccessToken },
        (err, res) => {
          Utils.Log("Xác nhan khuyen mai data:", res);
          if (err) {
            App.instance.ShowAlertDialog(App.instance.getTextLang("txt_error"));
            return;
          } else {
            let msg = res.message;
            App.instance.ShowAlertDialog(msg);
            if (res.success) {
              Configs.Login.Coin = parseInt(res.data);
              BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
            }
          }
        }
      );
    }

    actRefund() {
      if (!this.popupRefund) {
        let cb = (prefab) => {
          let popupRefund = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupRefund");
          App.instance.node.addChild(popupRefund.node);
          this.popupRefund = popupRefund;
          this.popupRefund.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupRefund", cb);
      } else {
        this.popupRefund.show();
      }
    }
    actSecurity() {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let popupSecurity = cc
          .instantiate(prefab)
          .getComponent("Lobby.PopupSecurity");
        App.instance.node.addChild(popupSecurity.node);
        // this.popupSecurity = popupSecurity;
        // this.popupSecurity.show();
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupSecurity", cb);
      // if (!Configs.Login.IsLogin) {
      //   App.instance.alertDialog.showMsg(
      //     App.instance.getTextLang("txt_need_login")
      //   );
      //   return;
      // }
      // if (!this.popupSecurity) {
      //   let cb = (prefab) => {
      //     let popupSecurity = cc
      //       .instantiate(prefab)
      //       .getComponent("Lobby.PopupSecurity");
      //     App.instance.node.addChild(popupSecurity.node);
      //     this.popupSecurity = popupSecurity;
      //     this.popupSecurity.show();
      //   };
      //   BundleControl.loadPrefabPopup("PrefabPopup/PopupSecurity", cb);
      // } else {
      //   this.popupSecurity.show();
      // }
    }

    actDiemDanh1() {
      if (!this.popupDiemDanh1) {
        let cb = (prefab) => {
          let popupDiemDanh = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupDiemDanh");
          App.instance.node.addChild(popupDiemDanh.node);
          this.popupDiemDanh1 = popupDiemDanh;
          this.popupDiemDanh1.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupDiemDanh", cb);
      } else {
        this.popupDiemDanh1.show();
      }
    }
    actShowBanner() {
      let cb = (prefab) => {
        let popupBanner = cc.instantiate(prefab).getComponent("Dialog");
        App.instance.node.addChild(popupBanner.node);
        popupBanner.show();
        popupBanner.node.zIndex = cc.macro.MAX_ZINDEX;
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupBanner", cb);
    }
    actVQMM() {
      Http.get(Configs.App.DOMAIN_CONFIG["VQMM_GetTurnUrl"], { type: 0 }, (status, res) => {
        try {

          if (status === 200 && res.c == 0) {
            if (+res?.d > 0) {
              this.showWheelOfFortune();
            }
          }

        } catch (error) {

        }
      });
      this.actHiddenMiniGameTipZo();
    }

    actInstall() {
      App.instance.alertDialog.showMsg(
        App.instance.getTextLang("txt_function_in_development")
      );
    }

    actEvent() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupMail) {
        Utils.Log("Chua có prefab popup Security");
        let cb = (prefab) => {
          let popupMail = cc.instantiate(prefab).getComponent("UIPopupMail");
          App.instance.node.addChild(popupMail.node);
          this.popupMail = popupMail;
          this.popupMail.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/UIPopupMail", cb);
      } else {
        this.popupMail.show();
      }
    }

    actDownload() {
      cc.sys.openURL(Configs.App.LINK_DOWNLOAD);
    }

    actFanpage() {
      cc.sys.openURL(Configs.App.getLinkFanpage());
    }
    actGroup() {
      cc.sys.openURL(Configs.App.LINK_GROUP);
    }

    actTelegram() {
      App.instance.openTelegram(Configs.App.getLinkTelegramGroup());
    }

    actAppOTP() {
      App.instance.openTelegram();
    }

    private isShowCSKH = false;
    actCSKH() {
      var self = this;
      if (self.isShowCSKH == false) {
        self.panelCSKH.scaleX = 0;
        self.panelCSKH.opacity = 0;
        self.panelCSKH.parent.active = true;
        self.isShowCSKH = true;
        cc.Tween.stopAllByTarget(self.panelCSKH);
        cc.tween(self.panelCSKH)
          .to(0.3, { scaleX: 1, opacity: 255 }, { easing: "backOut" })
          .start();
      } else {
        self.isShowCSKH = false;
        cc.Tween.stopAllByTarget(self.panelCSKH);
        cc.tween(self.panelCSKH)
          .to(0.3, { scaleX: 0, opacity: 0 }, { easing: "backIn" })
          .call(() => {
            self.panelCSKH.parent.active = false;
          })
          .start();
      }
    }

    actKhuyenMai() {
      App.instance.alertDialog.showMsg(
        App.instance.getTextLang("txt_function_in_development")
      );
    }

    actDiemDanh() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupDiemDanh) {
        let cb = (prefab) => {
          let popupDiemDanh = cc
            .instantiate(prefab)
            .getComponent("UIPopupDiemDanh");
          App.instance.node.addChild(popupDiemDanh.node);
          this.popupDiemDanh = popupDiemDanh;
          this.popupDiemDanh.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/UIPopupDiemDanh", cb);
      } else {
        this.popupDiemDanh.show();
      }
    }
    actOpenFB() {
      cc.sys.openURL(
        "https://www.facebook.com/Vua88-Club-Thi%C3%AAn-%C4%90%C6%B0%E1%BB%9Dng-Tr%C3%B2-Ch%C6%A1i-114371267824923/"
      );
      // App.instance.openWebView("https://www.facebook.com/gaming/lote88com");
    }

    actOpenMessager() {
      cc.sys.openURL(
        "https://www.facebook.com/Vua88-Club-Thi%C3%AAn-%C4%90%C6%B0%E1%BB%9Dng-Tr%C3%B2-Ch%C6%A1i-114371267824923/"
      );
      // App.instance.openWebView("https://www.facebook.com/lote88com/inbox/");
    }
    actOpenZalo() {
      cc.sys.openURL("https://t.me/cskhVua88");
    }
    actOpenLive() {
      App.instance.openWebView("https://direct.lc.chat/13507743/");
    }

    actOpenHotLine() {
      if (cc.sys.isNative) {
        cc.sys.openURL("tel:0928121378");
      } else {
        App.instance.alertDialog.showMsg("Hotline : 0928121378");
      }
    }

    actTeleTipZo() {
      cc.sys.openURL("https://t.me/TIPZO_CSKH_BOT");
    }

    actFbTipZo() {
      cc.sys.openURL("http://tiny.cc/ple7001");
    }

    actSupportOnline() {
      // cc.sys.openURL(Configs.App.LINK_SUPPORT);
      if (!cc.sys.isNative) {
        var url = "https://direct.lc.chat/13507743/";
        cc.sys.openURL(url);
        //Tawk_API.toggle();
      } else {
        App.instance.openTelegram();
      }
      //App.instance.openTelegram();
    }

    actBack() {
      App.instance.confirmDialog.show3(
        App.instance.getTextLang("txt_ask_logout"),
        "ĐĂNG XUẤT",
        (isConfirm) => {
          if (isConfirm) {
            this.logOut();
            this.buttonMiniGameTipzo.active = false;
          }
        }
      );
    }

    public logOut() {
      App.instance.checkMailUnread = false;
      this.panelMenu.node.parent.active = false;
      this.panelMenu.hide();

      if (cc.sys.isBrowser) {
        window.localStorage.removeItem("u");
        window.localStorage.removeItem("at");
        window.localStorage.removeItem("at_fb");
        window.localStorage.removeItem("un");
        window.localStorage.removeItem("pw");
      }
      SPUtils.setUserName("");
      SPUtils.setUserPass("");
      cc.sys.localStorage.setItem("IsAutoLogin", 0);
      BroadcastReceiver.send(BroadcastReceiver.USER_LOGOUT);
      App.instance.updateConfigGame(App.DOMAIN);
      App.instance.RECONNECT_GAME = false;
    }

    public actSwitchCoin() {
      if (this.lblCoin.node.parent.active) {
        this.lblCoin.node.parent.active = false;
      } else {
        this.lblCoin.node.parent.active = true;
      }
    }

    actGameTaiXiu() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_need_login"));
        return;
      }

      const showError = () => {
        App.instance.showErrLoading(App.instance.getTextLang("me11"));
      };

      const connectHub = (
        client: { connectHub: (cb: (success: boolean) => void) => void }
      ): Promise<void> => {
        return new Promise((resolve, reject) => {
          client.connectHub((success: boolean) => {
            App.instance.showLoading(false);
            success ? resolve() : reject();
          });
        });
      };

      connectHub(MiniGameTX1SignalRClient.getInstance())
        .then(() => connectHub(MiniGameTX2SignalRClient.getInstance()))
        .then(() => connectHub(MiniGameTX3SignalRClient.getInstance()))
        .then(() => connectHub(MiniGameSignalRClient.getInstance()))
        .then(() => {
          App.instance.openMiniGameTaiXiuDouble("TaiXiuDouble", "TaiXiuDouble");
          this.actHiddenMiniGameTipZo();
        })
        .catch(showError);
    }
    actGameTaiXiuSieuToc() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (cc.sys.isNative && cc.sys.os == cc.sys.OS_IOS) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_coming_soon")
        );
        return;
      }
      if (
        Configs.Login.AccessTokenSockJs == "" ||
        cc.sys.localStorage.getItem("token_Sockjs") == null
      ) {
        TaiXiuSTNetworkClient.getInstance().isOpenGame = true;
        this.loginMiniGameSockJs();
        return;
      }
      TaiXiuSTNetworkClient.getInstance().checkConnect(() => {
        App.instance.openMiniGameTaiXiuSieuToc(
          "TaiXiuSieuToc",
          "TaiXiuSieuToc"
        );
      });
    }
    actGameBauCua() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      MiniGameNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openMiniGameBauCua("BauCua", "BauCua");
      });
    }

    actGameCaoThap() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      MiniGameSignalRClient.getInstance().connectHub((success) => {
        App.instance.showLoading(false);
        if (success) {
          cc.log("successs");
          App.instance.openMiniGameCaoThap("Cao_Thap", "Cao_Thap");
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }

      });
      this.actHiddenMiniGameTipZo();
    }

    actGameSlot3x3() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      MiniGameSignalRClient.getInstance().connectHub((success) => {
        App.instance.showLoading(false);
        if (success) {
          App.instance.openMiniGameSlot3x3("Slot3x3", "Slot3x3");
          this.actHiddenMiniGameTipZo();
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }

      });
    }

    actGamePhucSinh() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      MiniGameSignalRClient.getInstance().connectHub((success) => {
        App.instance.showLoading(false);
        if (success) {
          App.instance.openMiniGamePhucSinh("PhucSinh", "PhucSinh");
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }

      });
      this.actHiddenMiniGameTipZo();
    }

    actGameSlot3x3Gem() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      MiniGameNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openMiniGameSlot3x3Gem("Slot3x3Gem", "Slot3x3Gem");
      });
    }

    actGameMiniPoker() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      MiniGameSignalRClient.getInstance().connectHub((success) => {
        App.instance.showLoading(false);
        if (success) {
          App.instance.openMiniGameMiniPoker("MiniPoker", "MiniPoker");
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }

      });
      this.actHiddenMiniGameTipZo();
      // MiniGameNetworkClient.getInstance().checkConnect(() => {
      //   App.instance.showLoading(false);
      //   App.instance.openMiniGameMiniPoker("MiniPoker", "MiniPoker");
      // });
    }

    actGameTaLa() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.alertDialog.showMsg(
        App.instance.getTextLang("txt_coming_soon")
      );
    }

    actGoToSlot1() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot1", "Slot1");
      });
    }

    actGoToSlot2() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot2", "Slot2");
      });
    }

    actGoToSlot3() {
      Utils.Log("Go to slot3");
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot3", "Slot3");
      });
    }

    actGoToSlot4() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot4", "Slot4");
      });
    }

    actGoToSlot5() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot5", "Slot5");
      });
    }

    actGoToSlot6() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot6", "Slot6");
      });
    }

    actGoToSlot7() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot7", "Slot7");
      });
    }

    actGoToSlot8() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot8", "Slot8");
      });
    }

    actGoToSlot10() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // cc.director.loadScene("TestScene");
      App.instance.showErrLoading(App.instance.getTextLang("txt_loading"));
      SlotNetworkClient.getInstance().checkConnect(() => {
        App.instance.showLoading(false);
        App.instance.openGame("Slot10", "Slot10");
      });
    }

    actDev() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.alertDialog.showMsg(
        App.instance.getTextLang("txt_coming_soon")
      );
      return;
    }
    checkListBankRut() {
      if (Configs.Login.ListBankRut == null) {
        App.instance.showLoading(true);
        var data = {};
        data["c"] = 2008;
        data["nn"] = Configs.Login.Nickname;
        data["pn"] = 1;
        data["l"] = 10;
        Http.get(Configs.App.API, data, (err, res) => {
          App.instance.showLoading(false);
          var list = JSON.parse(res.data).data;
          Configs.Login.ListBankRut = list;
        });
      }
    }

    actGoToShootFish() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.openGame("ShootFish", "ShootFish");
    }
    actGoToOanTuTi() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.openGame("OanTuTi", "OanTuTi");
    }

    actGotoLoto() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.openGame("Loto", "Loto");
      // App.instance.loadSceneInSubpackage("Loto", "Loto");
    }

    actAddCoin() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupShop) {
        let cb = (prefab) => {
          let popupShop = cc.instantiate(prefab).getComponent("LobbyShop");
          App.instance.node.addChild(popupShop.node);
          this.popupShop = popupShop;
          this.popupShop.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupShop", cb);
      } else {
        this.popupShop.show();
      }
    }
    actCashout() {

      let cb = (prefab) => {
        let popupCashout = cc
          .instantiate(prefab)
          .getComponent("Lobby.PopupCashout");
        App.instance.node.addChild(popupCashout.node);
        this.popupCashout = popupCashout;
        this.popupCashout.show();
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupCashout", cb);
      // if (!Configs.Login.IsLogin) {
      //   App.instance.alertDialog.showMsg(
      //     App.instance.getTextLang("txt_need_login")
      //   );
      //   return;
      // }

      // if (Configs.Login.ListBankRut.length == 0) {
      //   if (!this.popupProfile) {
      //     let cb = (prefab) => {
      //       let popupProfile = cc
      //         .instantiate(prefab)
      //         .getComponent("Lobby.PopupProfile");
      //       App.instance.node.addChild(popupProfile.node);
      //       this.popupProfile = popupProfile;
      //       this.popupProfile.showTabBank();
      //     };
      //     BundleControl.loadPrefabPopup("PrefabPopup/PopupProfile", cb);
      //   } else {
      //     this.popupProfile.showTabBank();
      //   }
      // } else {
      //   if (!this.popupCashout) {
      //     let cb = (prefab) => {
      //       let popupCashout = cc
      //         .instantiate(prefab)
      //         .getComponent("Lobby.PopupCashout");
      //       App.instance.node.addChild(popupCashout.node);
      //       this.popupCashout = popupCashout;
      //       this.popupCashout.show();
      //     };
      //     BundleControl.loadPrefabPopup("PrefabPopup/PopupCashout", cb);
      //   } else {
      //     this.popupCashout.show();
      //   }
      // }
    }
    onBtnShowProfile() {
      this.actProfile();
    }
    actProfile(tabIndex = 0) {
      if (!this.popupProfile) {
        let cb = (prefab) => {
          let popupProfile = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupProfile");
          App.instance.node.addChild(popupProfile.node);
          this.popupProfile = popupProfile;

          this.popupProfile.show(tabIndex);
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupProfile", cb);
      } else {
        this.popupProfile.show(tabIndex);
      }
    }
    accExchange() {
      Utils.Log("act Add accExchange");
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      this.actAddCoin();
    }

    actGoToTLMN() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      App.instance.openPrefabGame("TienLen", "TienLen", (bundle, prefab) => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("tlmn", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.TLMN);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });


    }

    actGameBacay() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      App.instance.openPrefabGame("ba_cay", "ba_cay", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("bacay", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.BaCay);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });
    }

    actGameCatte() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      App.instance.openPrefabGame("Catte", "Catte", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("catte", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.Catte);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });
    }

    actGameTLMNSolo() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      App.instance.openPrefabGame("TienLen", "TienLen", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("tlmnsolo", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.TLMNSolo);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });
    }

    actGoToSamSolo() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      App.instance.openPrefabGame("Sam", "Sam", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("samlocsolo", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.SamLocSolo);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });
    }

    actGoToSam() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      App.instance.openPrefabGame("Sam", "Sam", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("samloc", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.SamLoc);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });
    }
    actGoToMauBinh() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);

      App.instance.openPrefabGame("MauBinh", "MauBinh", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("maubinh", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.MauBinh);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });

    }

    actGoToBaCay() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.isShowNotifyJackpot = false;
      App.instance.openGame("BaCay", "BaCay");
    }
    actGoToLieng() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.removeAllWebView();
      App.instance.isShowNotifyJackpot = false;
      App.instance.openGame("Lieng", "Lieng");
    }

    actGoToPoker() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }


      // App.instance.showLoading(true);
      App.instance.openPrefabGame("Poker", "Poker", () => {
        // App.instance.showLoading(true);
        CardGameSignalRClient.getInstance().connectHub("poker", (success) => {
          // App.instance.showLoading(false);
          if (success) {
            this.nodeRoom.removeAllChildren();
            let cb = (prefab) => {
              let popupDaily = cc
                .instantiate(prefab)
                .getComponent("RoomCards");
              this.nodeRoom.addChild(popupDaily.node);
              popupDaily.setDataRoom(Configs.GameId88.Poker);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/RoomCards", cb);
          } else {
            App.instance.showErrLoading(App.instance.getTextLang("me11"));
          }

        });
      });

    }

    actGoToBaiCao() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.alertDialog.showMsg(App.instance.getTextLang('txt_coming_soon'));
      // return;
      App.instance.openGame("BaiCao", "BaiCao");
    }
    actKiemTien() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      if (!this.popupKiemTien) {
        let cb = (prefab) => {
          let popupSecurity = cc
            .instantiate(prefab)
            .getComponent("Lobby.PopupKiemTien");
          App.instance.node.addChild(popupSecurity.node);
          this.popupKiemTien = popupSecurity;
          this.popupKiemTien.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupKiemTien", cb);
      } else {
        this.popupKiemTien.show();
      }
    }

    actGoToGame3Rd() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
    }

    actGoToKeno() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.openGame("Keno", "Keno");
    }

    actGoToCaiShen() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      CaiShenSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          // App.instance.openGame("CaiShen", "CaiShen");
          //this.actHiddenMiniGameTipZo();
          App.instance.openPrefabGameFortune("CaiShen", "CaiShen", (_, prefab: cc.Prefab) => {
          })
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });

    }

    actGotoForest() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      // App.instance.showLoading(true);
      ForestSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          App.instance.openPrefabGameRungVang("Forest", "Forest", (_, prefab: cc.Prefab) => {
          })
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGotoKingdom() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      KingdomSignalRClient.getInstance().connectHub((success: Function) => {
        // App.instance.showLoading(false);
        if (success) {
          App.instance.openPrefabGameVuongQuoc("Kingdom", "Kingdom", (_, prefab: cc.Prefab) => {
          })
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGotoOcean() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      OceanSignalRClient.getInstance().connectHub((success: Function) => {
        // App.instance.showLoading(false);
        if (success) {
          App.instance.openPrefabGameThuyCung("Ocean", "Ocean", (_, prefab: cc.Prefab) => {
          })
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGotoOracle() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      OracleSignalRClient.getInstance().connectHub((success: Function) => {
        // App.instance.showLoading(false);
        if (success) {
          App.instance.openPrefabGame("Oracle", "Oracle", (_, prefab: cc.Prefab) => {
            App.instance.showLoading(false);
            let popupGame = cc.instantiate(prefab);
            App.instance.bigGameNode.addChild(popupGame);
          })
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }


    actGoToDancing() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      // App.instance.showLoading(true);
      DQSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          App.instance.openPrefabGameGaiNhay("DancingQueen", "DancingQueen", (_, prefab: cc.Prefab) => {
          })
        } else {
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGoToMegaMillions() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      BundleControl.loadBundle("Keno", () => {
        App.instance.openGame("MegaMillions", "MegaMillions");
      });
    }

    actGoToUSPowerBall() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      BundleControl.loadBundle("Keno", () => {
        BundleControl.loadBundle("MegaMillions", () => {
          App.instance.openGame("USPowerBall", "USPowerBall");
        });
      });
    }

    actGoToXuatKich() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.openPrefabGameXuatKich("XuatKich", "XuatKich", (_, prefab: cc.Prefab) => {
        let popupGame = cc.instantiate(prefab);
        App.instance.bigGameNode.addChild(popupGame);
      });
    }

    actGoToLongVuong(even, data) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
          App.instance.openPrefabGameLongVuong("LongVuong", "LongVuong", (_, prefab: cc.Prefab) => {
            let popupGame = cc.instantiate(prefab);
            App.instance.bigGameNode.addChild(popupGame);
            // cc.director.getScene().getChildByName("Canvas").addChild(popupGame);
      });
    }

    actGoToRongHo() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.showLoadingProcess(true);
      App.instance.lblStatus.string = "0%";
      App.instance.spriteProgress.fillRange = 0;

      DragonTigerSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          App.instance.lblStatus.string = "100%";
          App.instance.spriteProgress.fillRange = 1;

          setTimeout(() => {
            App.instance.showLoadingProcess(false);
            this.actGoToCasinoGame(Configs.GameId88.DragonTiger);
          }, 500);
        } else {
          App.instance.showLoadingProcess(false);
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGoToBaccarat() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.showLoadingProcess(true);
      App.instance.lblStatus.string = "0%";
      App.instance.spriteProgress.fillRange = 0;

      BaccaratSignalRClient.getInstance().connectHub((success: Function) => {
        App.instance.showLoading(false);
        if (success) {
          App.instance.lblStatus.string = "100%";
          App.instance.spriteProgress.fillRange = 1;

          setTimeout(() => {
            App.instance.showLoadingProcess(false);
            this.actGoToCasinoGame(Configs.GameId88.Baccarat);
          }, 500);
        } else {
          App.instance.showLoadingProcess(false);
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGoToXocDia88() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
            App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.showLoadingProcess(true);
      App.instance.lblStatus.string = "0%";
      App.instance.spriteProgress.fillRange = 0;

      SedieSignalRClient.getInstance().connectHub((success: boolean) => {
        if (success) {
          App.instance.lblStatus.string = "100%";
          App.instance.spriteProgress.fillRange = 1;

          setTimeout(() => {
            App.instance.showLoadingProcess(false);
            this.actGoToCasinoGame(Configs.GameId88.Sedie);
          }, 500);
        } else {
          App.instance.showLoadingProcess(false);
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGoToBlackJack() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.showLoadingProcess(true);
      App.instance.lblStatus.string = "0%";
      App.instance.spriteProgress.fillRange = 0;

      BlackjackSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          App.instance.lblStatus.string = "100%";
          App.instance.spriteProgress.fillRange = 1;

          setTimeout(() => {
            App.instance.showLoadingProcess(false);
            this.actGoToCasinoGame(Configs.GameId88.Blackjack);
          }, 500);
        } else {
          App.instance.showLoadingProcess(false);
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGoToRoulette() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.showLoadingProcess(true);
      App.instance.lblStatus.string = "0%";
      App.instance.spriteProgress.fillRange = 0;

      RouletteSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          App.instance.lblStatus.string = "100%";
          App.instance.spriteProgress.fillRange = 1;

          setTimeout(() => {
            App.instance.showLoadingProcess(false);
            this.actGoToCasinoGame(Configs.GameId88.NewRoulette);
          }, 500);
        } else {
          App.instance.showLoadingProcess(false);
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    private actGoToCasinoGame(gameID: number) {
      this.nodeRoom.removeAllChildren();
      BundleControl.loadPrefabPopup("PrefabPopup/Casino/Lobby", (prefab: any) => {
        const popupCasino = cc.instantiate(prefab).getComponent("Casino.Lobby");
        this.nodeRoom.addChild(popupCasino.node);
        popupCasino.setDataRoom(gameID);
      });
    }

    actGoToSport() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }
      App.instance.showLoading(true);
      App.instance.openPrefabGame("Sport", "Sport", (bundle, prefab) => {
        App.instance.showLoading(false);
        App.instance.addGameToNode(prefab);

      });
    }

    actGoToSicbo() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(
          App.instance.getTextLang("txt_need_login")
        );
        return;
      }

      App.instance.showLoadingProcess(true);
      App.instance.lblStatus.string = "0%";
      App.instance.spriteProgress.fillRange = 0;

      SicboSignalRClient.getInstance().connectHub((success: Function) => {
        if (success) {
          App.instance.lblStatus.string = "100%";
          App.instance.spriteProgress.fillRange = 1;

          setTimeout(() => {
            App.instance.showLoadingProcess(false);
            this.actGoToCasinoGame(Configs.GameId88.NewSicbo);
          }, 500);
        } else {
          App.instance.showLoadingProcess(false);
          App.instance.showErrLoading(App.instance.getTextLang("me11"));
        }
      });
    }

    actGoToTaiXiuMD5Live() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_need_login"));
        return;
      }

      const showError = () => {
        App.instance.showErrLoading(App.instance.getTextLang("me11"));
      };

      const connectHub = (
          client: { connectHub: (cb: (success: boolean) => void) => void }
      ): Promise<void> => {
        return new Promise((resolve, reject) => {
          client.connectHub((success: boolean) => {
            App.instance.showLoading(false);
            success ? resolve() : reject();
          });
        });
      };

      connectHub(MiniGameTXMD5SignalRClient.getInstance())
          .then(() => {
            App.instance.openPrefabGame("TaiXiuLive", "TaiXiuLive", (_, prefab: cc.Prefab) => {
              App.instance.bigGameNode.removeAllChildren();
              let popupGame = cc.instantiate(prefab);
              popupGame.removeComponent("TaiXiuLiveJP.Controller");
              // this.dismiss();
              App.instance.bigGameNode.addChild(popupGame);
            })
          })
          .catch(showError);
    }

    actGoToTaiXiuJPLive() {
      if (!Configs.Login.IsLogin) {
        App.instance.alertDialog.showMsg(App.instance.getTextLang("txt_need_login"));
        return;
      }

      const showError = () => {
        App.instance.showErrLoading(App.instance.getTextLang("me11"));
      };

      const connectHub = (
          client: { connectHub: (cb: (success: boolean) => void) => void }
      ): Promise<void> => {
        return new Promise((resolve, reject) => {
          client.connectHub((success: boolean) => {
            App.instance.showLoading(false);
            success ? resolve() : reject();
          });
        });
      };

      connectHub(MiniGameTX1SignalRClient.getInstance())
          .then(() => connectHub(MiniGameTX2SignalRClient.getInstance()))
          .then(() => connectHub(MiniGameTX3SignalRClient.getInstance()))
          .then(() => {
            App.instance.openPrefabGame("TaiXiuLive", "TaiXiuLive", (_, prefab: cc.Prefab) => {
              App.instance.bigGameNode.removeAllChildren();
              let popupGame = cc.instantiate(prefab);
              popupGame.removeComponent("TaiXiuLiveMD5.Controller");
              // this.dismiss();
              App.instance.bigGameNode.addChild(popupGame);
            })
          })
          .catch(showError);
    }

    actLoginCMD() {
      App.instance.actLoginCMD();
    }

    actLoginIBC() {
      App.instance.actLoginIBC();
    }
    actLoginSBO() {
      App.instance.actLoginSBO();
    }

    actLoginWM() {
      App.instance.actLoginWM();
    }

    actLoginAG() {
      App.instance.actLoginAG();
    }
    actLoginEbet() {
      App.instance.actLoginEbet();
    }
    actLoginShootFish() {
      App.instance.actLoginShootFish();
    }

    showGameLive() {
      if (!this.gameLiveController) {
        let cb = (prefab) => {
          let gameLiveController = cc
            .instantiate(prefab)
            .getComponent("GameLiveController");
          App.instance.node.addChild(gameLiveController.node);
          this.gameLiveController = gameLiveController;
          this.gameLiveController.show();
        };
        BundleControl.loadPrefabPopup("PrefabPopup/GameLive", cb);
      } else {
        this.gameLiveController.show();
      }
    }
    updateSizeListGame(isHaveBanner) {
      this.bannerList.node.active = isHaveBanner;
      this.tabsListGame.updateSize(isHaveBanner);
    }
    getConfigGame() {
      Http.get(
        Configs.App.API,
        { c: "2037", nn: Configs.Login.Nickname, pl: "web" },
        (err, res) => {
          if (res != null) {
            //    cc.log(res);
            if (res["success"]) {
              this.tabsListGame.initListGameConfig(res);
              App.instance.VERSION_CONFIG = res["version"];
            } else {
              this.tabsListGame.loadListGame();
            }
            // this.checkAppVersion();
          }
        }
      );
    }
    checkAppVersion() {
      if (typeof Configs.App.VERSION_APP != "undefined") {
        let versionApp = parseInt(Configs.App.VERSION_APP.replace(/[.]/g, ""));
        let versionConfig = parseInt(
          App.instance.VERSION_CONFIG.replace(/[.]/g, "")
        );
        if (versionApp < versionConfig) {
          let url = "https://lote79.com/";
          if (cc.sys.os == cc.sys.OS_ANDROID || cc.sys.os == cc.sys.OS_IOS) {
            App.instance.showConfirmDialog(
              "Đã có phiển bản mới.\nVui lòng cập nhật ứng dụng để có trải nghiệm tốt nhất!",
              () => {
                cc.sys.openURL(url);
              },
              false
            );
          }
        }
      }
    }

    actButtonMiniGameTipZo() {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      this.panelMiniGameTipZo.active = true;
      this.buttonMiniGameTipzo.active = false;
      cc.tween(this.panelMiniGameTipZo.getChildByName("Container")).set({ angle: -180, scale: 0 }).to(0.3, { scale: 1.0, angle: 0 }, { easing: cc.easing.sineOut }).start();
    }

    actHiddenMiniGameTipZo() {
      if (!this.panelMiniGameTipZo) {
        return;
      }

      if (!this.panelMiniGameTipZo.activeInHierarchy) {
        return;
      }

      let container = this.panelMiniGameTipZo.getChildByName("Container");
      if (!container) {
        return;
      }

      cc.tween(container)
        .to(0.3, { angle: -180, scale: 0 }, { easing: cc.easing.sineIn })
        .call(() => {
          this.panelMiniGameTipZo.active = false;
          this.buttonMiniGameTipzo.active = true;
        })
        .start();
    }

    actMissonGameTipZo(even, data) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let popupRegister = cc
          .instantiate(prefab)
          .getComponent("PopupEvent");
        App.instance.node.addChild(popupRegister.node);
        // this.popupRegister = popupRegister;
        // this.popupRegister.show(null, data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupEvent", cb);
    }
    actInboxGameTipZo(even, data) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let actInboxGameTipZo = cc
          .instantiate(prefab)
          .getComponent("PopupInbox");
        App.instance.node.addChild(actInboxGameTipZo.node);
        actInboxGameTipZo.setTabDefault(parseInt(data));
        // this.popupRegister = popupRegister;
        // this.popupRegister.show(null, data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupInbox", cb);
    }

    actDownloadGameTipZo(even, data) {
      let cb = (prefab) => {
        let actInboxGameTipZo = cc
          .instantiate(prefab)
          .getComponent("PopupDownload");
        App.instance.node.addChild(actInboxGameTipZo.node);
        // this.popupRegister = popupRegister;
        // this.popupRegister.show(null, data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupDownload", cb);
    }

    actTournamentGameTipZo(even, data) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let actTournamentGameTipZo = cc
          .instantiate(prefab)
          .getComponent("PopupTournament");
        App.instance.node.addChild(actTournamentGameTipZo.node);
        // this.popupRegister = popupRegister;
        // this.popupRegister.show(null, data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupTournament", cb);
    }

    actSpGameTipZo(even, data) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let actSpGameTipZo = cc
          .instantiate(prefab)
          .getComponent("PopupSupport");


        App.instance.node.addChild(actSpGameTipZo.node);
        actSpGameTipZo.show();
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupSupport", cb);
    }


    actCashGameTipZo(even, data) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let actCashGameTipZo = cc
          .instantiate(prefab)
          .getComponent("PopupCashout");

        this.prefabContainer.addChild(actCashGameTipZo.node);

        if (data === 'withdraw_card') {
          actCashGameTipZo.showWithdrawCard();
        } else {
          actCashGameTipZo.showTab(parseInt(data));
        }
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupCashout", cb);
    }

    actEventX2(even, tabIndex) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let actEventX2 = cc
          .instantiate(prefab)
          .getComponent("PopupEventX2");


        if (!actEventX2) {
          cc.error("Không tìm thấy component PopupCashout trong prefab!");
        } else {
          App.instance.node.addChild(actEventX2.node);
          actEventX2.showTab(parseInt(tabIndex));
        }
        // App.instance.node.addChild(actTournamentGameTipZo.node);
        // this.popupRegister = popupRegister;
        // this.popupRegister.show(null, data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupX2", cb);
    }

    actToggleTargetX6() {
      this.isVisibleJackPotX6 = !this.isVisibleJackPotX6;
      this.jackpotx6.active = this.isVisibleJackPotX6;

      const logoMap = {
        213: this.logo_fortune,
        201: this.logo_kingdom,
        207: this.logo_ocean,
        203: this.logo_oracle,
        215: this.logo_dancingNight,
        205: this.logo_dancing,
        211: this.logo_forest,
      };

      if (this.isVisibleJackPotX6) {
        this.jackpotX6List.removeAllChildren();
        Http.get(Configs.App.DOMAIN_CONFIG['GetListJackpot'], { CurrencyID: Configs.Login.CurrencyID }, (status, res) => {
          if (status == 200) {
            res.d.forEach((item, index) => {
              if (item.multiplier > 0) {
                let itemJackpot = cc.instantiate(this.jackpotX6Content);
                let nameLabel = itemJackpot.getChildByName("center").getChildByName("header").getComponent(cc.Label);
                switch (item.gameID) {
                  case 213:
                    nameLabel.string = App.instance.getTextLang("tx_than_tai");
                    break;
                  case 201:
                    nameLabel.string = App.instance.getTextLang("tx_vuong_quoc");
                    break;
                  case 207:
                    nameLabel.string = App.instance.getTextLang("tx_thuy_cung");
                    break;
                  case 203:
                    nameLabel.string = App.instance.getTextLang("tx_sam_truyen");
                    break;
                  case 215:
                    nameLabel.string = App.instance.getTextLang("tx_vu_truong");
                    break;
                  case 205:
                    nameLabel.string = App.instance.getTextLang("tx_gai_nhay");
                    break;
                  case 211:
                    nameLabel.string = App.instance.getTextLang("tx_rung_vang");
                    break;
                }
                itemJackpot.getChildByName("center").getChildByName("room").getChildByName("value").getComponent(cc.Label).string = item.roomID == 1 ? "100" : (item.roomID == 2 ? "1.000" : "10.000");
                itemJackpot.getChildByName("center").getChildByName("content").getChildByName("X").getComponent(cc.Label).string = "X" + item.multiplier;
                itemJackpot.getChildByName("center").getChildByName("content").getChildByName("nextJackpot").getComponent(cc.Label).string = item.nextJackpot;
                const logoNode = itemJackpot.getChildByName("logo");
                const iconNode = itemJackpot.getChildByName("icon");
                const spriteIcon = iconNode.getComponent(cc.Sprite);

                switch (item.multiplier) {
                  case 2:
                    spriteIcon.spriteFrame = this.icon_x2;
                    break;
                  case 3:
                    spriteIcon.spriteFrame = this.icon_x3;
                    break;
                  case 4:
                    spriteIcon.spriteFrame = this.icon_x4;
                    break;
                  case 5:
                    spriteIcon.spriteFrame = this.icon_x5;
                    break;
                  case 6:
                    spriteIcon.spriteFrame = this.icon_x6;
                    break;
                }


                const sprite = logoNode.getComponent(cc.Sprite);
                if (sprite && logoMap[item.gameID]) {
                  sprite.spriteFrame = logoMap[item.gameID];
                }

                itemJackpot.on(cc.Node.EventType.TOUCH_END, (event) => {
                  event.stopPropagation();
                  if (!Configs.Login.IsLogin) {
                    this.actShowLoginForm();
                  }
                });

                this.jackpotX6List.addChild(itemJackpot);
              }
            });
          }
        });
      }
    }

    actMiniGameXoSo(even, tabIndex) {
      if (!Configs.Login.IsLogin) {
        this.actShowLoginForm();
        return;
      }
      let cb = (prefab) => {
        let actMiniGameXoSo = cc
          .instantiate(prefab)
          .getComponent("PopupMiniGameXoSo");
        if (!actMiniGameXoSo) {
          cc.error("nothing mini game xo so");
        } else {
          this.actHiddenMiniGameTipZo();
          App.instance.node.addChild(actMiniGameXoSo.node);
          actMiniGameXoSo.showTab(parseInt(tabIndex));
        }

      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupXoSo", cb);
    }


    actCheckLogined() {
      if (!Configs.Login.IsLogin) {
        this.LoginBox.active = true;
        return;
      }
    }

    actShowLoginForm() {
      this.LoginBox.active = true;

      let editsOutside = this.node.parent.getComponentsInChildren(cc.EditBox);
      for (let edit of editsOutside) {
        edit.tabIndex = -1;
      }

      let editsInside = this.containerLoginBox.getComponentsInChildren(cc.EditBox);
      for (let i = 0; i < editsInside.length; i++) {
        editsInside[i].tabIndex = i + 1; // Đảm bảo thứ tự focus đúng
      }

      if (editsInside.length > 0) {
        editsInside[0].focus();
      }
    }

    fetchAndDecryptMessage() {
      if (Configs.App.DOMAIN_CONFIG && Configs.App.DOMAIN_CONFIG.LoginUrl) {

        if (SPUtils.getUserName() != "" && SPUtils.getUserPass() != "") {
          this.actLogin(SPUtils.getUserName(), SPUtils.getUserPass());
        }
        return;
      }

      try {
        let url: string;
        let key: string;
        const nonSecretPayloadLength = 0;

        if (cc.sys.isBrowser) {
          url = 'https://gameapi-alpha.bavenoth.com/api/v1/global/cfinfo';
          key = Configs.App.G88_KEY_DECRYPT_CONFIG;
        } else {
          url = 'https://config-alpha.bavenoth.com/api/v1/app/info';
          key = Configs.App.G88_KEY_DECRYPT_CONFIG_APP;
        }

        Http.get(url, {}, (status, res) => {
          if (status != 200) {
            this.fetchAndDecryptMessage();

            return;
          }

          const encryptedMessage = res.replace(/[^A-Za-z0-9+/=]/g, ''); // Cleaning up base64 encoded string

          const decodedKey = forge.util.decode64(key);
          const cipherText = forge.util.decode64(encryptedMessage);

          let offset = 0;

          offset += nonSecretPayloadLength;

          const nonce = cipherText.slice(offset, offset + 16);
          offset += 16;

          const authTag = cipherText.slice(cipherText.length - 16);

          const encryptedMessagePart = cipherText.slice(offset, cipherText.length - 16);

          const decipher = forge.cipher.createDecipher('AES-GCM', decodedKey);

          decipher.start({
            iv: nonce,
            tagLength: 128,
            tag: forge.util.createBuffer(authTag)
          });

          decipher.update(forge.util.createBuffer(encryptedMessagePart));

          const success = decipher.finish();

          if (!success) {
            throw new Error('Decryption failed');
          }

          const data = JSON.parse(decipher.output.toString())

          Configs.App.DOMAIN_CONFIG = data.DomainConfig;
          Configs.App.CONFIG88 = data;

          SPUtils.setRSAPublicKey(data.RsaPublicKey);

          if (cc.sys.isBrowser) {
            window.localStorage.setItem('client-token', data.Token);
          } else {
            cc.sys.localStorage.setItem('client-token', data.Token);
          }

          if (SPUtils.getUserName() != "" && SPUtils.getUserPass() != "") {
            this.actLogin(SPUtils.getUserName(), SPUtils.getUserPass());
          }

          return res;
        }, false);

      } catch (error) {
        if (error instanceof Error) {
          cc.log(`Error: ${error}`);
        } else {
          cc.log('An unknown error occurred');
        }
      }
    }

    pingpong() {
      const accessToken = Configs.Login.AccessToken;
      if (accessToken == "") {
        return;
      }

      Http.post(Configs.App.DOMAIN_CONFIG['CheckAuthenUrl'], {}, (status, res) => {
        switch (res['c']) {
          case 0:
            if (res['m'] != null) {
              Configs.Login.AccessToken = res['m']
            }
            break;
          case 2:
            // OTP
            break;
          case 3:
            let cb = (prefab) => {
              let popupDaily = cc.instantiate(prefab).getComponent("PopupUpdateNickname");
              App.instance.canvas.addChild(popupDaily.node)
              App.instance.popupUpdateNickname = popupDaily;
              App.instance.popupUpdateNickname.show2(SPUtils.getUserName(), SPUtils.getUserPass());
            }
            BundleControl.loadPrefabPopup("PrefabPopup/PopupUpdateNickname", cb);
            break;
          default:
            this.handleLogout();
        }
      })
    }

    handleLogout() {
      if (this.pingpongID) {
        clearInterval(this.pingpongID);
      }

      SignalRClient.closeAll();
      if (cc.director.getScene().name !== "Lobby") {
        App.instance.loadScene("Lobby");
      } else {
        this.panelNotLogin.active = true;
        this.panelLogined.active = false;
        this.userName.string = "";
        this.password.string = "";
        this.userNameInBox.string = "";
        this.passwordInBox.string = "";
      }
      SPUtils.setUserName("");
      SPUtils.setUserPass("");
      cc.systemEvent.emit('LOGOUT');
      Configs.Login.clear();
      App.instance.buttonMiniGame?.actHidden();
      App.instance.bigGameNode?.removeAllChildren();
      App.instance.miniGame?.removeAllChildren();
      this.nodeRoom?.removeAllChildren();
    }

    //Vòng quay may mắn
    showWheelOfFortune() {

      let cb = (prefab: any) => {
        let popupSpinWheel = cc.instantiate(prefab).getComponent("PopupSpinWheel");
        App.instance.node.addChild(popupSpinWheel.node);
        popupSpinWheel.show();
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupSpinWheel", cb);

    }

    showVipWheel() {

      let cb = (prefab) => {
        let popupDaily = cc
          .instantiate(prefab)
          .getComponent("PopupVipWheel");
        this.nodeWheel.addChild(popupDaily.node);

      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupVipWheel", cb);
    }
    showAnnouncement(data) {
      data.forEach(element => {

        let cb = (prefab) => {
          let actSpGameTipZo = cc
            .instantiate(prefab)
            .getComponent("PopupAnnouncement");


          App.instance.node.addChild(actSpGameTipZo.node);
          actSpGameTipZo.show2(element['content']);
        };
        BundleControl.loadPrefabPopup("PrefabPopup/PopupAnnouncement", cb);
      });
    }
    showSlide(data) {

      let cb = (prefab) => {
        let popupDaily = cc
          .instantiate(prefab)
          .getComponent("PopupSlide");
        App.instance.node.addChild(popupDaily.node);
        popupDaily.show2(data);
      };
      BundleControl.loadPrefabPopup("PrefabPopup/PopupSlide", cb);
    }
  }
}
export default Lobby.LobbyController;
