//import SPUtils from "../../Lobby/LobbyScript/Script/common/SPUtils";
import LanguageMananger from "../LobbyScript/Script/common/Language.LanguageManager";
import VersionConfig from "./VersionConfig";

namespace Configs {

  export class Login {
    static UserId: number = 0;
    static CurrencyID: number = 1;
    static Username: string = "";
    static Password: string = "";
    static Nickname: string = "";
    static Avatar: string = "";
    static Coin: number = 0;
    static GoldCoin: number = 0;
    static IsLogin: boolean = false;
    static ConfirmStatus: boolean = false;
    /** 
     * 0: Chưa confirm gì <br/>
     * 1: Confirm Mobile <br/>
     * 2: Confirm Email <br/>
     * 3: Confirm Telesafe <br/>
     * 4: Confirm Email và Mobile <br/>
     * 5: confirm Telesafe và Mobile <br/>
     * 6: confirm Telesafe và Email <br/>
     * 7: confirm Telesafe Email và Mobile <br/>
     */
    static SecurityStatus: number = 0;
    static AccessToken: string = "";
    static AccessToken2: string = "";
    static AccessTokenSockJs: string = "";
    static AccessTokenFB: string = "";
    static FacebookID: string = "";
    static SessionKey: string = "";
    static LuckyWheel: number = 0;
    static CreateTime: string = "";
    static Birthday: string = "";
    static IpAddress: string = "";
    static VipPoint: number = 0;
    static VipPointLevel: number = 0;
    static Address: string = "";
    static VipPointSave: number = 0;
    static Mail: string = "";
    static MobilePhone: string = "";
    static TeleSafe: string = "";
    static Gender: boolean = true;
    static RefferalCode: string = "";
    static PortalID: number = 0;

    static CoinFish: number = 0;
    static UserIdFish: number = 0;
    static UsernameFish: string = "";
    static PasswordFish: string = "";
    static FishConfigs: any = null;
    static BitcoinToken: string = "";
    static Currency: "vin";
    static UserType: string = "0";

    static ListBankRut = null;
    static ListPayment = null;
    static ClickPayPayment = null;

    static CACHE_AG = false;
    static CACHE_IBC = false;
    static CACHE_WM = false;

    static ListMail = null;
    static clear() {
      this.UserId = 0;
      this.Username = "";
      this.Password = "";
      this.Nickname = "";
      this.Avatar = "";
      this.Mail = "";
      this.MobilePhone = "";
      this.TeleSafe = "";
      this.Coin = 0;
      this.IsLogin = false;
      this.ConfirmStatus = false;
      this.SecurityStatus = 0;
      this.AccessToken = "";
      this.AccessToken2 = "";
      this.AccessTokenSockJs = "";
      this.SessionKey = "";
      this.CreateTime = "";
      this.Birthday = "";
      this.IpAddress = "";
      this.VipPoint = 0;
      this.VipPointLevel = 0;
      this.VipPointSave = 0;
      this.CoinFish = 0;
      this.UserIdFish = 0;
      this.UsernameFish = "";
      this.PasswordFish = "";
      this.BitcoinToken = "";
      this.MobilePhone = "";
      this.TeleSafe = "";
      this.PortalID = 0;
      //    SPUtils.setUserPass("");
    }

    static readonly VipPoints = [
      80, 800, 4500, 8600, 12000, 50000, 1000000, 2000000, 5000000,
    ];
    static readonly VipPointsName = [
      "Đá",
      "Đồng",
      "Bạc",
      "Vàng",
      "BK1",
      "BK2",
      "KC1",
      "KC2",
      "KC3",
    ];
    static getVipPointName(): string {
      for (let i = this.VipPoints.length - 1; i >= 0; i--) {
        if (Configs.Login.VipPoint > this.VipPoints[i]) {
          return this.VipPointsName[i + 1];
        }
      }
      return this.VipPointsName[0];
    }

    static GetListMailNew() {
      if (this.ListMail == null || this.ListMail.length == 0) {
        return 0;
      }
      var number = 0;
      for (var i = 0; i < this.ListMail.length; i++) {
        if (this.ListMail[i].status == 0) {
          number++;
        }
      }
      return number;
    }
    static getVipPointNextLevel(): number {
      for (let i = this.VipPoints.length - 1; i >= 0; i--) {
        if (Configs.Login.VipPoint > this.VipPoints[i]) {
          if (i == this.VipPoints.length - 1) {
            return this.VipPoints[i];
          }
          return this.VipPoints[i + 1];
        }
      }
      return this.VipPoints[0];
    }
    static getVipPointIndex(): number {
      for (let i = this.VipPoints.length - 1; i >= 0; i--) {
        if (Configs.Login.VipPoint > this.VipPoints[i]) {
          return i;
        }
      }
      return 0;
    }
  }

  export class App {
    // ************
    public static IS_LOCAL = true;
    public static IS_PRO = true;
    static G88_KEY_DECRYPT_CONFIG: string = "YeJqQu2yr1E4kWY1G1QpZyj0ZkPYJJSgNI5nUouA4Pw=";
    static G88_KEY_DECRYPT_CONFIG_APP: string = "StA/zuCaB9aqqrUQd85rtw8/3933wQa28zhSxCUMWTg=";
    static DOMAIN_CARD_G88 = "https://cards-alpha.bavenoth.com"
    static G88_BUNDLE_ID: string = "test.hot.cc";
    static API: string = "https://iportal.vua88.vin/api";
    static APIROY: string = "https://portal.vua88.vin/api";
    static API_PAYIN_PAYWELL_BANKS: string =
      "https://iportal.eloras.icu/api/payin/paywell/banks";
    static MONEY_TYPE = 1;
    static LINK_DOWNLOAD = "https://eloras.icu/download";
    static LINK_EVENT = "https://eloras.icu/event";
    static LINK_SUPPORT = "https://eloras.icu";
    static USE_WSS = false;
    static LINK_GROUP = "https://www.facebook.com/groups/bao99.vip";
    static FB_APPID = "***************";
    static AGENCY_CODE = "";
    static options = {
      rememberUpgrade: true,
      transports: ["websocket"],
      secure: true,
      rejectUnauthorized: false,
      reconnection: true,
      autoConnect: true,
      auth: {
        token: "WERTWER34534FGHFGBFVBCF345234XCVASD",
      },
    };
    static VERSION_APP = "1.0.3";

    static DOMAIN_CONFIG: any = {};
    static CONFIG88: any = {};
    static GameName = {
      110: "Đua Xe",
      170: "Crypto",
      2: "Tài Xỉu",
      5: "Xèng",
      11: "Tiến Lên",
      160: "Chim Điên",
      120: "Thần Tài",
      150: "Thể Thao",
      1: "MiniPoker",
      3: "Bầu Cua",
      9: "Ba Cây",
      4: "Cao Thấp",
      191: "Chiêm Tinh",
      190: "Tài Xỉu Siêu Tốc",
      12: "Xóc Đĩa",
      180: "Thần Bài",
      197: "Bikini",
    };
    static readonly HOST_MINIGAME = {
      host: "",
      port: 443,
    };
    static readonly HOST_MINIGAME2 = {
      host: "",
      port: 443,
    };
    static readonly HOST_TAI_XIU_MINI2 = {
      host: "",
      port: 443,
    };
    static readonly HOST_SLOT = {
      host: "",
      port: 443,
    };
    static readonly HOST_TLMN = {
      host: "",
      port: 443,
    };
    static readonly HOST_SHOOT_FISH = {
      host: "",
      port: 443,
    };
    static readonly HOST_SAM = {
      host: "",

      port: 443,
    };
    static readonly HOST_XOCDIA = {
      host: "",
      port: 443,
    };
    static readonly HOST_BACAY = {
      host: "",
      port: 443,
    };
    static readonly HOST_BAICAO = {
      host: "",
      port: 443,
    };
    static readonly HOST_POKER = {
      host: "",
      port: 443,
    };
    static readonly HOST_XIDACH = {
      host: "",
      port: 443,
    };
    static readonly HOST_BINH = {
      host: "",
      port: 443,
    };
    static readonly HOST_LIENG = {
      host: "",
      port: 443,
    };
    static readonly SERVER_CONFIG = {
      ratioNapThe: 1,
      ratioNapMomo: 1.2,
      ratioTransfer: 0.98,
      ratioTransferDL: 1,
      listTenNhaMang: ["Viettel", "Vinaphone", "Mobifone", "Vietnamobile"],
      listIdNhaMang: [0, 1, 2, 3],
      listMenhGiaNapThe: [10000, 20000, 30000, 50000, 100000, 200000, 500000],
      ratioRutThe: 1.2,
    };
    static readonly CASHOUT_CARD_CONFIG = {
      listTenNhaMang: [
        "Viettel",
        "Vinaphone",
        "Mobifone",
        "Vietnamobile",
        "Garena",
        "Vcoin",
        "FPT Gate",
        "Mobi Data",
      ],
      listIdNhaMang: ["VTT", "VNP", "VMS", "VNM", "GAR", "VTC", "FPT", "DBM"],
      listMenhGiaNapThe: [10000, 100000, 200000, 500000],
      listQuantity: ["1", "2", "3"],
    };
    static readonly HOST_SOCKJS = "http://" + VersionConfig.DOMAIN_LOCAL + ":10099/";
    static readonly SOCKJS_LOGIN = "websocket/ws-taixiu";
    // static readonly SOCKJS_LOGIN = "websocket/ws-minigame";
    static readonly TXST_SUB_TOPIC = "/topic/tx";

    static readonly nameKeyBank = { VCB: 0, TCB: 1, VIB: 2, VPB: 3 };
    static BILLING_CONF: any;

    static getServerConfig() {}

    static getPlatformName() {
      if (cc.sys.isNative && cc.sys.os == cc.sys.OS_ANDROID) return "android";
      if (cc.sys.isNative && cc.sys.os == cc.sys.OS_IOS) return "ios";
      return "web";
    }

    static getLinkFanpage() {
      switch (VersionConfig.CPName) {
        default:
          return "https://www.facebook.com/bao99club";
      }
    }

    static getLinkTelegram() {
      switch (VersionConfig.CPName) {
        default:
          return "cskhbao99";
      }
    }

    static getLinkTelegramGroup() {
      switch (VersionConfig.CPName) {
        default:
          return "cskhbao99";
      }
    }

    static init() {
      //  cc.log("init config vao day casi sakdjas");
      switch (VersionConfig.ENV) {
        case VersionConfig.ENV_LOCAL:
          this.USE_WSS = false;
          this.API = "http://" + VersionConfig.DOMAIN_LOCAL + ":8081/api";
          this.MONEY_TYPE = 1;
          this.LINK_DOWNLOAD =
            "http://" + VersionConfig.DOMAIN_LOCAL + "/landing";
          this.LINK_EVENT = "http://" + VersionConfig.DOMAIN_LOCAL + "event";
          this.LINK_SUPPORT = "" + VersionConfig.DOMAIN_LOCAL + "";

          this.HOST_MINIGAME.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_MINIGAME.port = 3644;
          this.HOST_TAI_XIU_MINI2.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_TAI_XIU_MINI2.port = 3344;
          this.HOST_MINIGAME2.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_MINIGAME2.port = 36442;
          this.HOST_SLOT.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_SLOT.port = 3844;
          this.HOST_TLMN.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_TLMN.port = 3144;
          this.HOST_SAM.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_SAM.port = 3944;
          this.HOST_XOCDIA.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_XOCDIA.port = 3344;
          this.HOST_BACAY.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_BACAY.port = 3044;
          this.HOST_BAICAO.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_BAICAO.port = 3144;
          this.HOST_POKER.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_POKER.port = 3744;
          this.HOST_XIDACH.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_XIDACH.port = 3244;
          this.HOST_BINH.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_BINH.port = 3244;
          this.HOST_LIENG.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_LIENG.port = 3544;
          this.HOST_SHOOT_FISH.host = VersionConfig.DOMAIN_LOCAL;
          this.HOST_SHOOT_FISH.port = 4083;
          break;

        case VersionConfig.ENV_DEV:
          this.USE_WSS = true;

          this.API = "https://iportal." + VersionConfig.DOMAIN_DEV + "/api";
          this.MONEY_TYPE = 1;
          this.LINK_DOWNLOAD = "https://" + VersionConfig.DOMAIN_DEV + "";
          this.LINK_EVENT = "https://" + VersionConfig.DOMAIN_DEV + "/event";

          this.HOST_MINIGAME.host = "wmini." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_MINIGAME2.host = "wmini2." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_TAI_XIU_MINI2.host =
            "overunder." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_SLOT.host = "wslot." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_TLMN.host = "wtlmn." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_SHOOT_FISH.host = "wbanca." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_SAM.host = "wsam." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_XOCDIA.host = "wxocdia." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_BACAY.host = "wbacay." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_BAICAO.host = "wbaicao." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_POKER.host = "wpoker." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_XIDACH.host = "wxizach." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_BINH.host = "wbinh." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_LIENG.host = "wlieng." + VersionConfig.DOMAIN_DEV + "";
          //  cc.log(VersionConfig.ENV);
          break;
        case VersionConfig.ENV_PROD:
          this.USE_WSS = true;

          this.API = "https://iportal." + VersionConfig.DOMAIN_PRO + "/api";
          this.MONEY_TYPE = 1;
          this.LINK_DOWNLOAD = "https://" + VersionConfig.DOMAIN_PRO + "";
          this.LINK_EVENT = "https://" + VersionConfig.DOMAIN_PRO + "/event";

          this.HOST_MINIGAME.host = "wmini." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_MINIGAME2.host = "wmini2." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_TAI_XIU_MINI2.host =
            "overunder." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_SLOT.host = "wslot." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_TLMN.host = "wtlmn." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_SHOOT_FISH.host = "wbanca." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_SAM.host = "wsam." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_XOCDIA.host = "wxocdia." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_BACAY.host = "wbacay." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_BAICAO.host = "wbaicao." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_POKER.host = "wpoker." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_XIDACH.host = "wxizach." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_BINH.host = "wbinh." + VersionConfig.DOMAIN_PRO + "";
          this.HOST_LIENG.host = "wlieng." + VersionConfig.DOMAIN_PRO + "";
          //  cc.log(VersionConfig.ENV);
          break;
        default:
          this.USE_WSS = true;

          this.API = "https://iportal." + VersionConfig.DOMAIN_DEV + "/api";
          this.MONEY_TYPE = 1;
          this.LINK_DOWNLOAD = "https://" + VersionConfig.DOMAIN_DEV + "";
          this.LINK_EVENT = "https://" + VersionConfig.DOMAIN_DEV + "/event";

          this.HOST_MINIGAME.host = "wmini." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_MINIGAME2.host = "wmini2." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_TAI_XIU_MINI2.host =
            "overunder." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_SLOT.host = "wslot." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_TLMN.host = "wtlmn." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_SHOOT_FISH.host = "wbanca." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_SAM.host = "wsam." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_XOCDIA.host = "wxocdia." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_BACAY.host = "wbacay." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_BAICAO.host = "wbaicao." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_POKER.host = "wpoker." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_XIDACH.host = "wxizach." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_BINH.host = "wbinh." + VersionConfig.DOMAIN_DEV + "";
          this.HOST_LIENG.host = "wlieng." + VersionConfig.DOMAIN_DEV + "";
          break;
      }
    }
  }
  export class GameId88 {
    static readonly None = 0;
    static readonly BaCay = 1;
    static readonly Poker = 13;
    static readonly TLMN = 7;
    static readonly XiTo = 5;
    static readonly MauBinh = 9;
    static readonly SamLoc = 15;
    static readonly TLMNSolo = 33;
    static readonly SamLocSolo = 35;
    static readonly BaiCao = 37;
    static readonly XocXoc = 25;
    static readonly Lieng = 17;
    static readonly Catte = 57;
    static readonly Kingdom = 201;
    static readonly Forest = 211;
    static readonly Ocean = 207;
    static readonly Olympia = 203;
    static readonly Dancing = 205;
    static readonly GodOfFortune = 213;
    static readonly BanCa = 301;
    static readonly TieuLongNgu = 302;
    static readonly Shark = 605;
    static readonly SoDo = 401;
    static readonly Sicbo = 403;
    static readonly Roulette = 405;
    static readonly Baccarat = 43;
    static readonly Blackjack = 45;
    static readonly Sedie = 47;
    static readonly NewRoulette = 49;
    static readonly NewSicbo = 51;
    static readonly DragonTiger = 53;
    static readonly CrabFish = 55;
    static readonly Tala = 3;
    static readonly TalaSolo = 39;
    static readonly SlotTournament = 230;
    static readonly PokerTournament = 501;
    static readonly TournamentGoOn = 503;
    static readonly TournamentSam = 505;
    static readonly TournamentOTT = 407;
    static readonly MutilSlot = 990;
    static readonly BanCaCooming = 991;
    static readonly Sortie = 607;
    static readonly PowerBall = 601;
    static readonly MegaMillions = 603;
    static readonly SpaceWar = 609;
    static readonly SportLive = 701;
    static readonly SportVirtual = 703;
    static readonly ESport = 705;
    static readonly CockFighting = 707;
    static readonly FantasySport = 709;
    static readonly FantasySportSkill = 7090;
    static readonly Disco = 215;
    static readonly Keno = 611;
    static readonly TradingPro = 119;
    static readonly LuckyDiceMd5 = 123;
    static readonly CommingSoon = 999;

    static readonly MiniPoker = 101;
    static readonly TaiXiu = 103;
    static readonly HiLo = 105;
    static readonly BauCua = 107;
    static readonly PhucSinh = 109;
    static readonly LuckyWild = 121;
    static readonly Lottery = 113;
    static readonly VQMM = 111;
    static readonly OTT = 115;

    static getGameName(gameId: number): string {
      const idToLangKey: Record<number, string> = {
        [GameId88.BaCay]: 'na1',
        [GameId88.Poker]: 'na2',
        [GameId88.TLMN]: 'na3',
        [GameId88.SamLoc]: 'na4',
        [GameId88.TLMNSolo]: 'na5',
        [GameId88.SamLocSolo]: 'na6',
        [GameId88.MauBinh]: 'na7',
        [GameId88.Kingdom]: 'na8',
        [GameId88.Olympia]: 'na9',
        [GameId88.Ocean]: 'na10',
        [GameId88.Dancing]: 'na11',
        [GameId88.MutilSlot]: 'na12',
        [GameId88.Sedie]: 'na13',
        [GameId88.Blackjack]: 'na14',
        [GameId88.Baccarat]: 'na15',
        [GameId88.NewRoulette]: 'na16',
        [GameId88.NewSicbo]: 'na17',
        [GameId88.DragonTiger]: 'na18',
        [GameId88.TieuLongNgu]: 'na20',
        [GameId88.MiniPoker]: 'na22',
        [GameId88.TaiXiu]: 'na21',
        [GameId88.PhucSinh]: 'na23',
        [GameId88.BauCua]: 'na24',
        [GameId88.HiLo]: 'na25',
        [GameId88.OTT]: 'na26',
        [GameId88.LuckyWild]: 'na27',
        [GameId88.Lottery]: 'na28',
        [GameId88.SoDo]: 'na29',
        [GameId88.Catte]: 'na30',
        [GameId88.Shark]: 'na32',
        [GameId88.Forest]: 'na33',
        [GameId88.GodOfFortune]: 'na34',
        [GameId88.Sortie]: 'na36',
        [GameId88.PokerTournament]: 'na37',
        [GameId88.TournamentGoOn]: 'na38',
        [GameId88.TournamentSam]: 'na39',
        [GameId88.TournamentOTT]: 'na40',
        [GameId88.FantasySport]: 'na41',
        [GameId88.Disco]: 'na44'
      };

      const langKey = idToLangKey[gameId];
      if (langKey) {
        return LanguageMananger.instance.getString(langKey);
      }

      return "Unknown";
    }

  }
  export class GameId {
    static readonly MiniPoker = 1;
    static readonly TaiXiu = 2;
    static readonly BauCua = 3;
    static readonly CaoThap = 4;
    static readonly Slot3x3 = 5;
    static readonly VQMM = 7;
    static readonly Sam = 8;
    static readonly BaCay = 9;
    static readonly MauBinh = 10;
    static readonly TLMN = 11;
    static readonly TaLa = 12;
    static readonly Lieng = 13;
    static readonly XiTo = 14;
    static readonly XocXoc = 15;
    static readonly BaiCao = 16;
    static readonly Poker = 17;
    static readonly Bentley = 19;
    static readonly RangeRover = 20;
    static readonly MayBach = 21;
    static readonly RollsRoyce = 22;

    static getGameName(gameId: number): string {
      switch (gameId) {
        case this.MiniPoker:
          return "MiniPoker";
        case this.TaiXiu:
          return "Tài xỉu";
        case this.BauCua:
          return "Bầu cua";
        case this.CaoThap:
          return "Cao thấp";
        case this.Slot3x3:
          return "Slot3x3";
        case this.VQMM:
          return "VQMM";
        case this.Sam:
          return "Sâm";
        case this.MauBinh:
          return "Mậu binh";
        case this.TLMN:
          return "TLMN";
        case this.TaLa:
          return "Tá lả";
        case this.Lieng:
          return "Liêng";
        case this.XiTo:
          return "Xì tố";
        case this.XocXoc:
          return "Xóc xóc";
        case this.BaiCao:
          return "Bài cào";
        case this.Poker:
          return "Poker";
        case this.Bentley:
          return "Bentley";
        case this.RangeRover:
          return "Range Rover";
        case this.MayBach:
          return "May Bach";
        case this.RollsRoyce:
          return "Rolls Royce";
      }
      return "Unknow";
    }

    static getChatChannelName(gameId: number): string {
      switch (gameId) {
        case GameId88.MauBinh:
          return "maubinh_lobby";
        case GameId88.SamLoc:
          return "loc_lobby";
        case GameId88.TLMN:
          return "tlmn_dl_lobby";
        case GameId88.Poker:
          return "poker_lobby";
        case GameId88.TLMNSolo:
          return "tlmn_dl_solo_lobby";
        case GameId88.SamLocSolo:
          return "loc_solo_lobby";
        case GameId88.Catte:
          return "catte_lobby";
        case GameId88.DragonTiger:
          return "dragontiger_lobby";
        case Configs.GameId88.Sedie:
          return "sedie_lobby";
        case Configs.GameId88.Blackjack:
          return "blackjack_lobby";
        case Configs.GameId88.Baccarat:
          return "baccarat_lobby";
        case Configs.GameId88.NewSicbo:
          return "casinosicbo_lobby";
        case Configs.GameId88.NewRoulette:
          return "roulette_lobby";
      }
    }
  }

  export class Payment {
    static Deposit = null;
  }
  export class EventFacebook {
    static readonly EVENT_NAME_ACTIVATED_APP = "fb_mobile_activate_app"; //mo app
    static readonly EVENT_NAME_DEACTIVATED_APP = "fb_mobile_deactivate_app";
    static readonly EVENT_NAME_SESSION_INTERRUPTIONS =
      "fb_mobile_app_interruptions";
    static readonly EVENT_NAME_TIME_BETWEEN_SESSIONS =
      "fb_mobile_time_between_sessions";

    static readonly EVENT_NAME_COMPLETED_REGISTRATION =
      "fb_mobile_complete_registration"; //dang ky
    static readonly EVENT_NAME_COMPLETED_LOGIN = "fb_mobile_complete_login"; //todo dang nhap

    static readonly EVENT_NAME_VIEWED_CONTENT = "fb_mobile_content_view";
    static readonly EVENT_NAME_SEARCHED = "fb_mobile_search";
    static readonly EVENT_NAME_RATED = "fb_mobile_rate";
    static readonly EVENT_NAME_COMPLETED_TUTORIAL =
      "fb_mobile_tutorial_completion";
    static readonly EVENT_NAME_PUSH_TOKEN_OBTAINED =
      "fb_mobile_obtain_push_token";
    static readonly EVENT_NAME_ADDED_TO_CART = "fb_mobile_add_to_cart";
    static readonly EVENT_NAME_ADDED_TO_WISHLIST = "fb_mobile_add_to_wishlist";
    static readonly EVENT_NAME_INITIATED_CHECKOUT =
      "fb_mobile_initiated_checkout";
    static readonly EVENT_NAME_ADDED_PAYMENT_INFO =
      "fb_mobile_add_payment_info"; //sdt

    static readonly EVENT_NAME_PURCHASED = "fb_mobile_purchase"; //nap tien
    static readonly EVENT_NAME_EARN_VIRTUAL_CURRENCY =
      "fb_mobile_earn_virtual_currency"; //todo doi thuong

    static readonly EVENT_NAME_ACHIEVED_LEVEL = "fb_mobile_level_achieved";
    static readonly EVENT_NAME_UNLOCKED_ACHIEVEMENT =
      "fb_mobile_achievement_unlocked";
    static readonly EVENT_NAME_SPENT_CREDITS = "fb_mobile_spent_credits"; //tieu tien
  }
  export class ParamsFacebook {
    static readonly EVENT_PARAM_CURRENCY = "fb_currency";
    static readonly EVENT_PARAM_REGISTRATION_METHOD = "fb_registration_method";
    static readonly EVENT_PARAM_LOGIN_METHOD = "fb_login_method"; //todo

    static readonly EVENT_PARAM_CONTENT_TYPE = "fb_content_type";
    static readonly EVENT_PARAM_CONTENT = "fb_content";
    static readonly EVENT_PARAM_CONTENT_ID = "fb_content_id";
    static readonly EVENT_PARAM_SEARCH_STRING = "fb_search_string";
    static readonly EVENT_PARAM_SUCCESS = "fb_success";
    static readonly EVENT_PARAM_MAX_RATING_VALUE = "fb_max_rating_value";
    static readonly EVENT_PARAM_PAYMENT_INFO_AVAILABLE =
      "fb_payment_info_available";
    static readonly EVENT_PARAM_NUM_ITEMS = "fb_num_items";
    static readonly EVENT_PARAM_LEVEL = "fb_level";
    static readonly EVENT_PARAM_DESCRIPTION = "fb_description";
    static readonly EVENT_PARAM_SOURCE_APPLICATION = "fb_mobile_launch_source";
    static readonly EVENT_PARAM_VALUE_YES = "1";
    static readonly EVENT_PARAM_VALUE_NO = "0";
  }
  export class EventFirebase {
    static readonly ADD_PAYMENT_INFO = "add_payment_info";
    static readonly ADD_TO_CART = "add_to_cart";
    static readonly ADD_TO_WISHLIST = "add_to_wishlist";
    static readonly APP_OPEN = "app_open";
    static readonly BEGIN_CHECKOUT = "begin_checkout";
    static readonly CAMPAIGN_DETAILS = "campaign_details";
    static readonly ECOMMERCE_PURCHASE = "ecommerce_purchase";
    static readonly GENERATE_LEAD = "generate_lead";
    static readonly JOIN_GROUP = "join_group";
    static readonly LEVEL_UP = "level_up";
    static readonly LOGIN = "login";
    static readonly POST_SCORE = "post_score";
    static readonly PRESENT_OFFER = "present_offer";
    static readonly PURCHASE_REFUND = "purchase_refund";
    static readonly SEARCH = "search";
    static readonly SELECT_CONTENT = "select_content";
    static readonly SHARE = "share";
    static readonly SIGN_UP = "sign_up";
    static readonly SPEND_VIRTUAL_CURRENCY = "spend_virtual_currency";
    static readonly TUTORIAL_BEGIN = "tutorial_begin";
    static readonly TUTORIAL_COMPLETE = "tutorial_complete";
    static readonly UNLOCK_ACHIEVEMENT = "unlock_achievement";
    static readonly VIEW_ITEM = "view_item";
    static readonly VIEW_ITEM_LIST = "view_item_list";
    static readonly VIEW_SEARCH_RESULTS = "view_search_results";
    static readonly EARN_VIRTUAL_CURRENCY = "earn_virtual_currency";
    static readonly REMOVE_FROM_CART = "remove_from_cart";
    static readonly CHECKOUT_PROGRESS = "checkout_progress";
    static readonly SET_CHECKOUT_OPTION = "set_checkout_option";
  }
  export class ParamsFireBase {
    static readonly ACHIEVEMENT_ID = "achievement_id";
    static readonly CHARACTER = "character";
    static readonly TRAVEL_CLASS = "travel_class";
    static readonly CONTENT_TYPE = "content_type";
    static readonly CURRENCY = "currency";
    static readonly COUPON = "coupon";
    static readonly START_DATE = "start_date";
    static readonly END_DATE = "end_date";
    static readonly FLIGHT_NUMBER = "flight_number";
    static readonly GROUP_ID = "group_id";
    static readonly ITEM_CATEGORY = "item_category";
    static readonly ITEM_ID = "item_id";
    static readonly ITEM_LOCATION_ID = "item_location_id";
    static readonly ITEM_NAME = "item_name";
    static readonly LOCATION = "location";
    static readonly LEVEL = "level";
    static readonly SIGN_UP_METHOD = "sign_up_method";
    static readonly NUMBER_OF_NIGHTS = "number_of_nights";
    static readonly NUMBER_OF_PASSENGERS = "number_of_passengers";
    static readonly NUMBER_OF_ROOMS = "number_of_rooms";
    static readonly DESTINATION = "destination";
    static readonly ORIGIN = "origin";
    static readonly PRICE = "price";
    static readonly QUANTITY = "quantity";
    static readonly SCORE = "score";
    static readonly SHIPPING = "shipping";
    static readonly TRANSACTION_ID = "transaction_id";
    static readonly SEARCH_TERM = "search_term";
    static readonly TAX = "tax";
    static readonly VALUE = "value";
    static readonly VIRTUAL_CURRENCY_NAME = "virtual_currency_name";
    static readonly CAMPAIGN = "campaign";
    static readonly SOURCE = "source";
    static readonly MEDIUM = "medium";
    static readonly TERM = "term";
    static readonly CONTENT = "content";
    static readonly ACLID = "aclid";
    static readonly CP1 = "cp1";
    static readonly ITEM_BRAND = "item_brand";
    static readonly ITEM_VARIANT = "item_variant";
    static readonly ITEM_LIST = "item_list";
    static readonly CHECKOUT_STEP = "checkout_step";
    static readonly CHECKOUT_OPTION = "checkout_option";
    static readonly CREATIVE_NAME = "creative_name";
    static readonly CREATIVE_SLOT = "creative_slot";
    static readonly AFFILIATION = "affiliation";
    static readonly INDEX = "index";
    static readonly METHOD = "method";
  }
  export class ParamType {
    static readonly STRING = "String";
    static readonly DOUBLE = "Double";
  }
}
export default Configs;
Configs.App.init();

//acc test: Bright111/admin123 bright/123456
