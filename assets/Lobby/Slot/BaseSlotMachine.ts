import { BaseSlotSymbol, SymbolState } from "./BaseSlotSymbol";
import { SpinConfig } from "./SlotConfig";

const { ccclass, property } = cc._decorator;
export interface SlotMachineListener {
    onReelCompleted?: () => void;
}

@ccclass("BaseSlotMachine")
export abstract class BaseSlotMachine extends cc.Component {
    @property([cc.Node])
    protected reels: cc.Node[] = [];

    @property(cc.Node)
    protected symbolTemplate: cc.Node = null;

    @property
    protected itemHeight: number = 0;

    protected spinConfig: SpinConfig;
    protected itemPosition: cc.Node[] = [];
    protected listeners: SlotMachineListener;

    /**
    * Sets the configuration for the slot machine
    * @param config - The spin configuration
    */
    public setConfig(config: SpinConfig): void {
        this.spinConfig = config;
    }

    /**
     * Initializes the slot reels with symbols
     */
    public initializeReels(): void {
        this.reels.forEach((reel: cc.Node) => reel.removeAllChildren());

        for (let i = 0; i < this.reels.length; i++) {
            const reel = this.reels[i];
            for (let j = 0; j < this.spinConfig.rowCount * 2 + this.spinConfig.symbolOffset; j++) {
                const item = cc.instantiate(this.symbolTemplate);
                item.active = true;
                reel.addChild(item);
                item.setPosition(0, this.itemHeight / 2 + this.itemHeight * j);

                // This is just a placeholder that will be overridden by child classes
                this.initializeSymbol(item, j > 2);
            }
        }

        // Remove the template after we're done with it
        this.symbolTemplate.removeFromParent();
        this.symbolTemplate = null;

        // Setup item positions for easy access
        const rowCount = this.spinConfig.rowCount;
        const colCount = this.reels.length;

        for (let row = rowCount - 1; row >= 0; row--) {
            for (let col = 0; col < colCount; col++) {
                this.itemPosition.push(this.reels[col].children[row]);
            }
        }
    }

    /**
     * Method to be overridden by child classes to initialize a symbol
     * @param symbolNode - The symbol node to initialize
     * @param isBlur - Whether the symbol is blur
     */
    protected abstract initializeSymbol(symbolNode: cc.Node, isBlur: boolean): void;

    /**
     * Starts the spinning animation with a given result
     * @param resultSpin - The result of the spin as a comma-separated string
     */
    public async startSpin(resultSpin: string): Promise<void> {
        let matrix = resultSpin.split(",");
        const { spinDuration, symbolOffset, delayReel, rowCount, elasticPercent } = this.spinConfig;
        let distance = this.itemHeight * (rowCount + symbolOffset + elasticPercent / 100);
        let reelCount = this.reels.length;
        let spinPromises: Promise<void>[] = [];

        for (let i = 0; i < reelCount; i++) {
            let column = this.reels[i];
            let items = column.children;

            // Set the result symbols at the bottom of the reels
            for (let j = 0; j < rowCount; j++) {
                let id = parseInt(matrix[i + j * reelCount]);
                let symbol = items[column.childrenCount - 1 - j];
                symbol.getComponent(BaseSlotSymbol).setIsBlur(false).setId(id.toString()).show();
                //this.setSymbolId(items[column.childrenCount - 1 - j], id, false);
            }
            // Create a promise for each reel spin
            let spinPromise = new Promise<void>((resolve) => {
                cc.tween(column)
                    .delay(i * delayReel)
                    .to(spinDuration, { y: -distance }, { easing: "quadOut" })
                    .by(0.2, { y: this.itemHeight * elasticPercent / 100 }, { easing: "quadInOut" })
                    .call(() => {
                        for (let j = 0; j < rowCount; j++) {
                            let id = parseInt(matrix[i + j * reelCount]);
                            let symbol = items[this.spinConfig.rowCount - 1 - j];
                            symbol.getComponent(BaseSlotSymbol).setIsBlur(false).setId(id.toString()).show();
                        }
                        this.listeners?.onReelCompleted?.();

                        column.y = 0;
                        resolve();
                    })
                    .start();
            });
            spinPromises.push(spinPromise);
        }

        await Promise.all(spinPromises);
    }

    /**
     * Gets the item at a specific position
     * @param index - The index of the item (1-based)
     * @returns The node at the specified position
     */
    public getItemAtPosition(index: number): cc.Node {
        return this.itemPosition[index - 1];
    }

    /**
     * Highlights specific items with winning positions
     * @param indexes - Array of indexes to highlight
     * @param prizeLines - Optional array of prize lines to show
     */
    public async highlightItems(indexes: number[], prizeLines?: string[]): Promise<void> {
        // Base implementation - to be overridden by child classes
        await Promise.all(
            indexes.map(async (i) => {
                let item = this.getItemAtPosition(i);
                await item.getComponent(BaseSlotSymbol).setState(SymbolState.HIGHLIGHT);
            })
        );

        this.resetAllItems();
    }

    /**
    * Resets all items to their initial state
    */
    public resetAllItems(): void {
        for (let item of this.itemPosition) {
            item.getComponent(BaseSlotSymbol).setState(SymbolState.INIT).then();
        }
    }
}