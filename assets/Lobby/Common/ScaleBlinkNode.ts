const { ccclass, property } = cc._decorator;

@ccclass
export default class ScaleBlinkNode extends cc.Component {

    @property({ tooltip: "Time Delay" })
    delayStart: number = 0;

    @property({ tooltip: "Time Done" })
    blinkDuration: number = 0.6;

    @property({ tooltip: "Opacity lowest" })
    minScale: number = 0.8;

    start() {
        this.scheduleOnce(() => {
            this.startBlinking();
        }, this.delayStart);
    }

    startBlinking() {
        const fadeOut = cc.scaleTo(this.blinkDuration / 2, this.minScale);
        const fadeIn = cc.scaleTo(this.blinkDuration / 2, 1);
        const blinkAction = cc.repeatForever(cc.sequence(fadeOut, fadeIn));
        this.node.runAction(blinkAction);
    }

    onDestroy() {
        this.node.stopAllActions();
    }
}
