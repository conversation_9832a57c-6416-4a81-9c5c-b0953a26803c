// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html


import BundleControl from "../../Loading/src/BundleControl";
import ChatInGame from "../../Lobby/ChatInGame/ChatInGame";
import CardOnTable from "../../Lobby/Common/CardOnTable";
import Card from "../../Lobby/Common/MCard";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import CardGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/CardGameSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import PlayviewSam from "./PlayviewSam";


const {ccclass, property} = cc._decorator;

@ccclass
export default class Sam extends cc.Component {

    // @property(cc.SpriteFrame)
    // cardFrameSP: cc.SpriteFrame[] = [];

    @property(cc.Node)
    deskPoient: cc.Node = null;
    @property(cc.SpriteFrame)
    frameCardBack: cc.SpriteFrame = null;
    @property(cc.Node)
    cardFrames: cc.Node;
    @property(cc.Node)
    cardClone: cc.Node = null;

    @property(cc.Node)
    effectToitrang: cc.Node = null;
  @property(cc.Node)
nodeEffectCardSpecial: cc.Node = null;
       

    @property(cc.Node)
    cardOnTableTemp: cc.Node;
    @property(cc.Node)
    cardOnTableParent: cc.Node

    listCardOnTable: cc.Node[] = [];
    isNewTurn: boolean = false;
  
    @property(PlayviewSam)
    listPlayerView: PlayviewSam[] = [];
    playerViewMe: PlayviewSam;
    cardCombo = [];

    listPlayer: any[] = [];
    maxPlayer:number = 4;
    playerMe: any;

    @property(cc.Node)
    btnDanh: cc.Node;

    @property(cc.Node)
    btnXep: cc.Node;


    @property(cc.Node)
    btnBo: cc.Node;


    @property(cc.Node)
    btnUnselect: cc.Node;

    @property(cc.Node)
    btnBaoSam:cc.Node;

    @property(cc.Node)
    btnHuySam:cc.Node;



    @property(cc.Label)
    lblThongBao: cc.Label = null;
    @property(cc.Label)
    lblClone: cc.Label = null;

    @property(cc.Sprite)
    dealer: cc.Sprite;


    @property(cc.AudioSource)
    sound: cc.AudioSource;

    @property(cc.Node)
    nodeButton: cc.Node;

     // var bundle = cc.assetManager.getBundle("TienLen");
        // bundle.loadDir("res/win", cc.Texture2D, (err, textures) => {
        //     if (err) {
        //         console.error("Lỗi khi tải ảnh:", err);
        //         return;
        //     }
    
        //     // Chuyển texture thành SpriteFrame
        //     let spriteFrames: cc.SpriteFrame[] = textures.map((texture: cc.Texture2D) => {
        //         return new cc.SpriteFrame(texture);
        //     });
    
        //     // Gọi hàm phát animation
        //     this.playAnimation(spriteFrames);
        // });
    

     INACTIVITY_TIMEOUT = 60;
     onLoad() {
        App.instance.inactivityTimer = 0;

        this.schedule(this.checkInactivity, 1);
    }

   

    checkInactivity() {
        App.instance.inactivityTimer++;
        var thiz = this;
        if (App.instance.inactivityTimer == this.INACTIVITY_TIMEOUT&& !thiz.isRegLeave) {
      
            App.instance.ShowAlertDialog(App.instance.getTextLang("ca247"));
            this.scheduleOnce(() => {
                if(App.instance.inactivityTimer >= thiz.INACTIVITY_TIMEOUT ){
                    App.instance.alertDialog.dismiss();
                    CardGameSignalRClient.getInstance().send('LeaveGame', [], (data) => {
                           cc.log(data);
                           if(data && !this.isGotolobby){
                               App.instance.showToast(App.instance.getTextLang("me8"));
                           }
                                  });
                }
                
              }, 3);
          
        }
    }
    playSound(nameSound,isloop){

        var chidd = this.sound.node.getChildByName(nameSound);
        if(chidd){
            this.sound.clip =   chidd.getComponent(cc.AudioSource).clip;
            this.sound.loop = isloop;
            this.sound.play();
        }
 
    }

    start () {
        
        var thiz = this;
        this.lblThongBao.node.parent.active = false;
  
        cc.game.on(cc.game.EVENT_HIDE, this.onGamePause, this);

        cc.game.on(cc.game.EVENT_SHOW, this.onGameResume, this);
        this.btnBo.getComponent(cc.Button).interactable = false;
        this.btnDanh.getComponent(cc.Button).interactable = false;
        this.btnXep.getComponent(cc.Button).interactable = false;
        this.btnUnselect.getComponent(cc.Button).interactable = false;
        this.btnBaoSam.active = false;
        this.btnHuySam.active = false;
      

        this.listPlayerView.forEach(element => {
            element.cardList.init(this.deskPoient,this.cardClone,this.frameCardBack,this.cardFrames);
        });


        this.playerViewMe = this.listPlayerView[0];

        this.playerViewMe.cardList.onSelected = (card, isSelected) => {
            if (!isSelected)
            {
                  if (thiz.playerViewMe.cardList.getCardSelected().length == 0 ){
                         thiz.btnUnselect.getComponent(cc.Button).interactable = false;
                  }
                return;
            }
          

            if (thiz.playerViewMe.cardList.getCardSelected().length > 2)
                return;
            thiz.btnUnselect.getComponent(cc.Button).interactable = true;
        };
        // setTimeout(() => {
        //     this.cardListMe.dealCards([1,2,3,4,5,6,7,8,9,10,11,12,13],true);
        //     this.cardListMe.onFinishDealCard = ()=>{
        // };
        // }, 100);
        CardGameSignalRClient.getInstance().receive('betOfAccountCF', (data: any) => {
      
     });
   
     setTimeout(() => {
        if(App.instance.DataPass.length>0)
            {
                this.onHandleJoin(App.instance.DataPass[0],App.instance.DataPass[1]);
            }    
     }, 10);
         
        // CardGameSignalRClient.getInstance().receiveArray('joinGame', (data1: any,data2: any) => {
        //      this.onHandleJoin(data1,data2);
        // });
        CardGameSignalRClient.getInstance().receive('playerJoin', (data: any) => {
            this.playerJoin(data);
        });
        CardGameSignalRClient.getInstance().receiveArray('playerLeave', (data: any,data2: any) => {
            this.playerLeave(data,data2);
        });

        CardGameSignalRClient.getInstance().receive('askBaoSam', (data: any) => {
            this.askBaoSam(data);
        });
        CardGameSignalRClient.getInstance().receive('baoSam', (data: any) => {
            this.baoSam(data);
        });
    CardGameSignalRClient.getInstance().receiveArray('message',  (data1: any,data2: any)=> {
            if(data1.code){
                App.instance.showToast(App.instance.getTextLang("me"+data1.code));
            }
        });
        CardGameSignalRClient.getInstance().receive('playerBaoSam', (data: any) => {
            this.baoSam(data);
        });
        CardGameSignalRClient.getInstance().receiveArray('updateAccount', (data: any,data2: any) => {
            this.updateAccount(data,data2);
        });
        
        CardGameSignalRClient.getInstance().receiveArray('startActionTimer', (data1: any,data2: any,data3:any) => {
            this.startActionTimer(data1,data2,data3);
        });
        CardGameSignalRClient.getInstance().receive('startGame', (data: any) => {
            this.startGame(data,true);
        });
        CardGameSignalRClient.getInstance().receiveArray('danhBai',  (data1: any,data2: any,data3:any)=> {
            this.danhBai([data1,data2,data3]);
        });
       CardGameSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
                   if (accountId == `${Configs.Login.UserId}:${Configs.Login.PortalID}`) {
                      
                       thiz.playerViewMe.showChatMsg(content);
                   } else {
                       var playerView = thiz.getPlayviewWithID(accountId);
                       if (playerView == null) return;;
                       playerView.showChatMsg(content);
                   }
               });

        CardGameSignalRClient.getInstance().receive('endRound',  (data1: any)=> {
            this.endRound(data1);
        });

        //hieu ung tien bay len
        // end game
        //3 nguoi
        CardGameSignalRClient.getInstance().receiveArray('updateConnectionStatus',  (data1: any,data2: any)=> {
            this.updateConnectionStatus(data1,data2);
        });
        CardGameSignalRClient.getInstance().receiveArray('showResult',  (data1: any,data2: any)=> {
            setTimeout(() => {
                 thiz.showResult(data1);
            }, 250);
        });
        
        CardGameSignalRClient.getInstance().receive('boLuot',  (data1)=> {
            this.boLuot(data1);
        });
        this.intervalId = setInterval(this.PingPong, 30000);

         var bundle = cc.assetManager.getBundle("Sam");
        bundle.loadDir("res/normal", cc.Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }
    
            // Chuyển texture thành SpriteFrame
            thiz.spriteFramesNo = textures.map((texture: cc.Texture2D) => {
                return new cc.SpriteFrame(texture);
            });
    
            // Gọi hàm phát animation
            this.playAnimationnormal();
        });

        bundle.loadDir("res/chiabai", cc.Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }
    
            // Chuyển texture thành SpriteFrame
            thiz.spriteFramesDe = textures.map((texture: cc.Texture2D) => {
                return new cc.SpriteFrame(texture);
            });
    
            // Gọi hàm phát animation
            // this.playAnimationDealer(spriteFrames);
        });
        thiz.playerViewMe.cardList.onFinishDealCard = () => {
                thiz.btnXep.getComponent(cc.Button).interactable = true;
        };

    }
    spriteFramesDe: cc.SpriteFrame[] = [];
    spriteFramesNo: cc.SpriteFrame[]= [];
    playAnimationDealer() {
        var thiz = this;
        let frameIndex = 0;
        if(thiz.spriteFramesDe.length<=0) return;
        const finishDealer = () => {
            console.log("Animation completed!");
            thiz.playAnimationnormal();
        };
        this.dealer.scheduleOnce(() => {
            this.dealer.spriteFrame = thiz.spriteFramesDe[frameIndex];
            frameIndex = (frameIndex + 1) % thiz.spriteFramesDe.length; 
        
            // Gọi callback khi hoàn thành
            finishDealer();
        }, 1/30);

    }
    

    playAnimationnormal() {
        var thiz = this;
        let index = 0;
        let forward = true; // Biến kiểm soát hướng
        const frameRate =1/30; // 100ms mỗi frame
        if(thiz.spriteFramesNo.length<=0) return;
        // Hủy mọi schedule trước khi đặt mới
        this.dealer.unscheduleAllCallbacks();
        
        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesNo[index];
        
            // Nếu đến cuối mảng, đảo chiều
            if (index === thiz.spriteFramesNo.length - 1) {
                forward = false;
            } 
            // Nếu đến đầu mảng, đổi hướng đi tiếp
            else if (index === 0) {
                forward = true;
            }
        
            // Cập nhật index theo hướng
            index += forward ? 1 : -1;
        }, frameRate, cc.macro.REPEAT_FOREVER);
        // 
        // let index = 0;
        // const frameRate = 0.3; // 100ms mỗi frame
        // this.dealer.unscheduleAllCallbacks(); 
        // // Lặp lại animation
        // this.dealer.schedule(() => {
        //     this.dealer.spriteFrame = thiz.spriteFramesNo[index];
        //     index = (index + 1) % thiz.spriteFramesNo.length; // Vòng lặp animation
        // },frameRate, cc.macro.REPEAT_FOREVER);
    }
    private intervalId: number = 0;
    PingPong() {
        CardGameSignalRClient.getInstance().send('PingPong', [], (data) => {
            cc.log(data);
                 });
        // Thực hiện các hành động khác trong hàm này
    }
    onDestroy() {
        // Dừng interval khi component bị hủy
        if (this.intervalId !== 0) {
            clearInterval(this.intervalId);
        }
        this.unschedule(this.checkInactivity);
        CardGameSignalRClient.getInstance().dontReceive();
    }

    actUnselect(){
        App.instance.inactivityTimer = 0;
              this.btnUnselect.getComponent(cc.Button).interactable = false;
              this.playerViewMe.cardList.node.children.forEach(element => {
                  element.getComponent(Card).setSelected(false,true);
              });
        this.playSound("s_bochon",false);
        
    }

    actBoluot(){
        App.instance.inactivityTimer = 0;
        CardGameSignalRClient.getInstance().send('boLuot',[],  (data1: any)=> {
           cc.log(data1);
        });
        this.playSound("s_boluot",false);
    }
    actBaossam(){
        App.instance.inactivityTimer = 0;
        CardGameSignalRClient.getInstance().send('BaoSam',[true],  (data1: any)=> {
           cc.log(data1);
           this.btnBaoSam.active = false;
           this.btnHuySam.active = false;
        });
        // this.playSound("s_boluot",false);
    }

    actBoSam(){
        App.instance.inactivityTimer = 0;
        CardGameSignalRClient.getInstance().send('BaoSam',[false],  (data1: any)=> {
           cc.log(data1);
           this.btnBaoSam.active = false;
           this.btnHuySam.active = false;
        });
        // this.playSound("s_boluot",false);
    }
    isbackground = false;
    onGamePause() {
        this.isbackground = true;
        // Xử lý khi game bị đưa xuống nền
    }
    
    onGameResume() {
        this.isbackground = false;
        // Xử lý khi game quay lại từ nền
    }
    dealCardForOther(playerViewTmpe){
            
     
        var thiz = this;
        var cardClone =  this.cardClone;
        let worldPos = thiz.deskPoient.convertToWorldSpaceAR(cc.Vec2.ZERO);
        var posDealer = this.node.convertToNodeSpaceAR(worldPos);
     

   
        for (var i = 0; i < 13; i++) {

            (()=>{
                var inew  = i;
                
               
                if (!thiz.isbackground) {
                    cc.log("dealCardsFake");
                    var cardNode = cc.instantiate(cardClone);
                
                cardNode.parent =  thiz.node;
                cardNode.setPosition(posDealer);
                var cardTemp = playerViewTmpe.lblCardRemain.node.parent;
             
             
                var posOrigin =  thiz.node.convertToNodeSpaceAR(cardTemp.convertToWorldSpaceAR(cc.Vec2.ZERO)); 
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
    
                    // cardNode.setScale(cc.v3(-1, 1, 1));
                    let t1 = cc.tween(cardNode).delay(0.02*inew).call(() => {
                        cc.log("vaa" + inew);
                        cardNode.active = true;
                        this.playSound("chiabai",false);
                    });
                    let t2 = cc.tween(cardNode)
                        .to(0.25, { position: cc.v3(posOrigin.x,posOrigin.y,0) });
                    let t3 = cc.tween(cardNode).to(0.1, {
                        // Bind position
                        scaleX: 0,
                    }
                    ).call(() => {
                        setTimeout(() => {
                            cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
                        }, 0); 
                    });
    
                    let t4 = cc.tween(cardNode).delay(0.1).to(0.1, {
                        // Bind position
                        // scaleX: 1,
                    }
                    ).call(()=>{
                     
                        cardNode.removeFromParent();

                    });
    
                    cc.tween(cardNode).sequence(t1, t2, t3, t4).start();
    
    
                }
                
            })();
           


        }
    }
    updateConnectionStatus(plaerID,status){


        if(this.playerMe.AccountID == plaerID){
        this.playerMe.isRegExit = false;
        if( status == 2)
                    this.playerMe.isRegExit = true;
                }

        var playerView = this.getPlayviewWithID(plaerID);
        if (playerView == null) return;
        
        playerView.updateStatus(status);
    }
    upbaiAll(){
        this.listCardOnTable.forEach(element => {
            element.children.forEach(element2 => {
                element2.getComponent(cc.Sprite).spriteFrame = this.frameCardBack;
                element2.setContentSize(cc.size(84.7,120));
                element2.color = cc.color(127, 127, 127,255);
            });
        });
    }
    endRound(data1){
        console.log("Endround========");
        this.upbaiAll();
        var thiz = this;
        setTimeout(() => {
             thiz.listPlayerView.forEach(element => {
               console.log("Endround========1");
             element.nodeResuft.children[10].active = false;
        });
        }, 250);
       
        
        
   
    }
    boLuot(data1){
        var playerView = this.getPlayviewWithID(data1);
        if (playerView == null) return;
        playerView.showEffectBoluot();

        // {"type":1,"target":"boLuot","arguments":[100085]}
    }

    danhBai(data){
       this.cardCombo = [];
        var thiz = this;
        var discardedUser = data[0];
        var cardsD = data[1];
        // SlotUtil.PlaySound(this.sounds, "danh_bai");
        var playerView = this.getPlayviewWithID(discardedUser);
        if (playerView == null) return;

        if (discardedUser == this.playerMe.AccountID) {
              this.btnUnselect.getComponent(cc.Button).interactable = false;
            this.btnBo.getComponent(cc.Button).interactable = false;
            var cardRemoves = playerView.cardList.removeCardWithID(cardsD);
             this.addCardNodeOnTable(cardRemoves, false);
        } else {
            this.cardCombo = cardsD;
   
            this.isNewTurn = false;
            this.addCardOnTableFormPlayeView(cardsD, false, playerView.node);
        }
      

        const playerIDs = Object.keys(data[2].Players);
        for (let i = 0; i < playerIDs.length; i++) {
            const playerID = playerIDs[i];
            const player = data[2].Players[playerID];
            if (player.AccountID != thiz.playerMe.AccountID) {
                var playerView = thiz.getPlayviewWithID(player.AccountID);
                if (playerView != null) {
                    playerView.updateRemainCard(player.HandCards.length);
                }
            }
           
        }
 var CurrTurnCards = data[2].GameLoop.CurrTurnCards;
   if(CurrTurnCards.length>0){
            var typeBobai =    CurrTurnCards[0].Value.Type ;
            if(typeBobai == 6 || typeBobai == 9 || typeBobai == 10){
               setTimeout(() => {
                   thiz.nodeEffectCardSpecial.children[typeBobai].active = true;
                }, 250);
                
            }
          } ;
       
    }
    showResult(data){
        this.nodeButton.active = false;
        const playerIDs = Object.keys(data.Players);
        var thiz = this;

       var listResulftDetail =  data.GameLoop.SessionResult.ResultList

        for (let i = 0; i < playerIDs.length; i++) {

            
            var inew = i;
            const playerID = playerIDs[inew];
            const player = data.Players[playerID];
           
            const result =   listResulftDetail.find(item => item.AccountId === player.AccountID);
            if(result == null){
                continue;
            }
            var isMeToiTrang = false;
            if(player.AccountID!=thiz.playerMe.AccountID){
                //mo bai
                var playerView = thiz.getPlayviewWithID(player.AccountID);
                if (playerView != null) {
                    var mycard =  player.HandCards.map(card => card.OrdinalValue); 
                    playerView.updateRemainCard(0);
                    playerView.cardList.addCards(mycard, true, playerView.node);
                    playerView.cardList.deactiveCard();
                }   
                

            }else{
               //chat,hang
                if(result && result.ResultFamily == 5 && result.WinType >-1){
                    this.playSound("coinFall",false);
                    isMeToiTrang = true;
                    thiz.showEffectToiTrang(result.WinType);
                }

                if(result.ResultFamily == 5 || result.ResultFamily == 4){
                    this.playSound("music_win",false);
                }else{
                    this.playSound("music_lose",false);
                }
               
            }

            var playerView = thiz.getPlayviewWithID(player.AccountID);

            if(result.ResultFamily == 5 && result.WinType ==-1){
                result.ResultFamily = 4;
            }
          
            if (playerView != null && result && player.Status == 1 ) {

if(result.WinType > 0 && result.Money<0){
                    playerView.showToiTrang(result.WinType);
                }

                if(result.Money!=0){ 

                // toi trang roi thì set la thang thoi
                if(isMeToiTrang){
                    playerView.showEffect(4);
                }else{
                    playerView.showEffect(result.ResultFamily);
                }
             
                // thiz.createEffectMoney(playerView, result.Money);
                }else{
                        //set hoa chua co
                           playerView.showEffect(13);
                }
                
             }   

           
        }
    }

    chatIngame: ChatInGame = null;
       actChat(){
    
           App.instance.inactivityTimer = 0;
           this.playSound("click",false);
           var thiz = this;
           if(this.chatIngame == null){
               let cb = (prefab) => {
                   this.chatIngame = cc.instantiate(prefab).getComponent("ChatInGame");
                   this.node.addChild(this.chatIngame.node);
                   this.chatIngame.show(Configs.GameId88.SamLoc);
                  
                 };
                 BundleControl.loadPrefabPopup("PrefabPopup/ChatInGame", cb);
           }else{
               this.chatIngame.show(Configs.GameId88.SamLoc);
           }
          
       }
       
    arrTypeToitrang = ["","","","","","ca69","ca65","ca63","ca64","ca66"];

    showEffectToiTrang(WinType){
        this.effectToitrang.active = true;
        this.effectToitrang.children[1].getComponent(cc.Label).string = App.instance.getTextLang(this.arrTypeToitrang[WinType]);
    }

    startGame(data,isAni){
                //   this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + data.CurrentGameLoopId; 
                   this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + (data.CurrentGameLoopId==-1?"":data.CurrentGameLoopId.toString()); 
      
        this.clearAllCardTable();
        this.nodeButton.active = true;
        this.btnBo.getComponent(cc.Button).interactable = false;
        this.btnDanh.getComponent(cc.Button).interactable = false;
        this.btnXep.getComponent(cc.Button).interactable = false;
        this.btnUnselect.getComponent(cc.Button).interactable = false;
        this.isNewTurn = true;
        this.effectToitrang.active = false;
        this.lblThongBao.node.parent.active = false;
        this.listPlayerView.forEach(element => {
            element.resetResulft();
        });
        this.playSound("s_chiabai",false);
        
        const playerIDs = Object.keys(data.Players);
        var thiz = this;

        for (let i = 0; i < playerIDs.length; i++) {

            
            var inew = i;
            const playerID = playerIDs[inew];
            const player = data.Players[playerID];
           
           
            if(player.AccountID!=thiz.playerMe.AccountID){
                //chia card ao
                var playerView = thiz.getPlayviewWithID(player.AccountID);
                if (playerView != null) {
                    var mycard =  player.HandCards.map(card => card.OrdinalValue); 
                    playerView.updateRemainCard(mycard.length);
                    if(isAni){

                        thiz.dealCardForOther(playerView);
                    }
                    playerView.activeAvatar(player.Status);
                }   
            }else{
                var mycard =  player.HandCards.map(card => card.OrdinalValue); 
                cc.log("thiz.playerMe"+ thiz.playerMe.AccountID);
                cc.log("thiz.mycard"+ mycard);
                thiz.playerViewMe.activeAvatar(player.Status);
                (function(mycard) {
                    setTimeout(() => {
                        thiz.playAnimationDealer();
                        thiz.playerViewMe.cardList.dealCards(mycard, isAni);
                      
                    }, 100);
                })(mycard);
               
            }
           
        }
       

        
    }

    createEffectMoney(playview, element) {
        var txtMoney = cc.instantiate(this.lblClone.node);
        txtMoney.parent = playview.node;
        txtMoney.active = true;
        txtMoney.position = cc.v3(0, 0, 0);
        if (element > 0) {
            txtMoney.getComponent(cc.Label).string = "+" + Utils.formatMoney(Math.abs(element));
              cc.tween(txtMoney).to(1.5, { position: cc.v3(0, 100, 0) }).delay(1.5).call(() => {
            txtMoney.removeFromParent();
        }).start();
        } else  if (element < 0) {
            // txtMoney.color = cc.color(255, 0, 0, 255);
            txtMoney.getComponent(cc.Label).string = "-" + Utils.formatMoney(Math.abs(element));
              cc.tween(txtMoney).to(1.5, { position: cc.v3(0, 100, 0) }).delay(1.5).call(() => {
            txtMoney.removeFromParent();
        }).start();
        }
      
    }
   

    startActionTimer(idplaer,time,allowAcction){
        this.lblThongBao.node.parent.active = false;
        this.btnDanh.getComponent(cc.Button).interactable = false;
        this.btnBo.getComponent(cc.Button).interactable = false;

        this.btnBaoSam.active = false;
        this.btnHuySam.active = false;
        
//         Thao tác người chơi:
// Bắt đầu: 1000
// Chờ: -1
// Đánh bài: 1
// Bỏ lượt: 2
// Chặt bài: 3
// Thắng-Thua: 4
        // {"type":1,"target":"startActionTimer","arguments":["100053:15",3,[1000]]} //bat dau
        // {"type":1,"target":"startActionTimer","arguments":["100085:15",15,[1]]} // nextturn
     
        for (let index = 0; index < allowAcction.length; index++) {
        const element = allowAcction[index];
        if(idplaer == this.playerMe.AccountID){
           
            if(element == 1){
                this.btnDanh.getComponent(cc.Button).interactable = true;
                this.playSound("s_denluot",false);
            }
            if(element == 2){
                this.btnBo.getComponent(cc.Button).interactable = true;
            }
            
            if(element == 3){
                this.playSound("s_chat",false);
            }
//             Chặt bài: 3
// Thắng-Thua: 4

        }
            if(element == -1){ //startgame
                // this.btnBo.active = true;
                this.lblThongBao.node.parent.active = true;
                this.lblThongBao.string = App.instance.getTextLang("me7");
                this.clearAllCardTable();
                this.nodeButton.active = false;
                this.effectToitrang.active = false;
                 this.listPlayerView.forEach(element => {
                    element.resetResulft();
                });
               
            }else if(element == 1000) {
                this.playSound("start",false);
                this.lblThongBao.node.parent.active = true;
                this.lblThongBao.string = App.instance.getTextLang("me26");
                this.clearAllCardTable();
                this.nodeButton.active = false;
            
                this.isNewTurn = true;
                this.effectToitrang.active = false;
                this.listPlayerView.forEach(element => {
                    element.resetResulft();
                });
                
            }
        }
       
   
  
   
    var playerView = this.getPlayviewWithID(idplaer );
    if (playerView == null) return;
    this.listPlayerView.forEach(element => {
        element.startCountDown(0);
    });
    playerView.startCountDown(time );
    }


    askBaoSam(time){
                this.lblThongBao.node.parent.active = true;
                this.lblThongBao.string = App.instance.getTextLang("ca62");
        this.btnBaoSam.active = true;
        this.btnHuySam.active = true;
        this.listPlayerView.forEach(element => {
            element.startCountDownSam(time);
        });

    }
    baoSam(idLeave){
        var playerView = this.getPlayviewWithID(idLeave);
                if (playerView != null) {
                    playerView.showEffect(11);
                }
    }
    updateAccount(idLeave,data){

        var playerView = this.getPlayviewWithID(idLeave.AccountID);
                if (playerView != null) {
                    var curr = this.MoneyType == 1? idLeave.GoldBalance:idLeave.CoinBalance;
                      this.createEffectMoney(playerView, curr - playerView.getGold());
                    playerView.updateGold(curr);
                          
                }
    }
    isGotolobby = false;

     
    playerLeave(idLeave,data){

        this.playSound("leave_room",false);
        if(idLeave == this.playerMe.AccountID){
            this.dataRoom = null;
            this.isGotolobby = true;
            if(data && data.code == 11007){
                var stringaler = App.instance.getTextLang("me11007");
                App.instance.ShowAlertDialog(Utils.formatString(stringaler, Utils.formatNumber(data.prms[0]),data.prms[0]==1? App.instance.getTextLang("hi25"):"Tipzo"));
            }
            this.node.removeFromParent();
               App.instance.gotoLobby();
            return;
        }
        for (var i = 0; i < this.listPlayer.length; i++) {
            if (this.listPlayer[i]['AccountID'] == idLeave) {
                this.listPlayer.splice(i, 1);
                break;
            }
        }
        for (var i = 0; i < this.listPlayerView.length; i++) {
            if (this.listPlayerView[i].player != null && this.listPlayerView[i].player.AccountID == idLeave) {
                this.listPlayerView[i].updateInfor(null);
                break;
            }
        }
        // this.updateDataRoom(data);

    }
    actExitLobby(){
    
    }

  
 isRegLeave = false;
    actLeaveGame(){
           this.playSound("click",false);
            if( this.playerMe.isRegExit){
                       CardGameSignalRClient.getInstance().send('UnregisterLeaveRoom', [], (data) => {
                           cc.log(data);
                             this.isRegLeave = false;
                           if(data && !this.isGotolobby){
                               App.instance.showToast(App.instance.getTextLang("me9"));
                           }
                                  }); 
           
                   }else{
                       CardGameSignalRClient.getInstance().send('LeaveGame', [], (data) => {
                           cc.log(data);
                             this.isRegLeave = true;
                           if(data && !this.isGotolobby){
                               App.instance.showToast(App.instance.getTextLang("me8"));
                           }
                                  });
                   }
          
       }

    playerJoin(data){
      //  {"type":1,"target":"playerJoin","arguments":[{"HandCards":[],"OrderInGame":1,"EmptyHand":
      // true,"AccountID":"100085:15","Account":{"AvatarID":0,"Avatar":"0","AccountID":"100085:
      // 15","NickName":"[X]tommyt1","OriginNickname":"tommyt1","GoldBalance":********,"CoinBalance"
      // :0,"MerchantID":0,"SourceID":1,"CurrencyID":1,"PortalID":15},"Status":0,"Position":1,"Regi
      // sterLeaveRoom":false,"ConnectionStatus":1}]}
      this.listPlayer.push(data);
      this.fillPlayerToSlot();
    }

    // responsvive==================
    dataRoom;
    @property(cc.Label)
    lblTable: cc.Label = null;
    @property(cc.Label)
    lblTable2: cc.Label = null;
    @property(cc.Label)
    lblTable3: cc.Label = null;
    @property(cc.Node)
    vipTable: cc.Node = null;

    MoneyType = 1;
    onHandleJoin(data,extra){
        // {"type":1,"target":"joinGame","arguments":[{"Rule":0,"GameLoop":{"TimerPaused":true,"Elapsed":0,"CurrTurnCards":[],
        // "GameState":{"DefaultAccount":null,"ClientStates":[]},"SessionResult":{"ResultList":[]}},
        // "Players":{"100053:15":{"HandCards":[],"OrderInGame":-1,"EmptyHand":true,"AccountID":"100053:15",
        // "Account":{"AvatarID":0,"Avatar":"0","AccountID":"100053:15","NickName":"
        // [X]tommy2","OriginNickname":"tommy2","GoldBalance":********,"CoinBalance":5700000,"M
        // erchantID":0,"SourceID":1,"CurrencyID":1,"PortalID":15},"Status":0,"Position":0,"Regi
        // sterLeaveRoom":false,"ConnectionStatus":1}},"IsPlaying":false,"OwnerId":"100053:15","Positions"
        // :["100053:15",null,null,null],"LeaveGameList":[],"CountActivePlayer":0,"MaxAllow":4,"Name":"1",
        // "GameId":7,"MaxPlayer":4,"MinBet":1000,"MaxBet":0,"RuleDescription":"","IsPrivate":false,"BetStep":0,
        // "MoneyType":1,"CurrentGameLoopId":0,"CurrencyId":1,"CountPlayers":1},{"totalTime":15,"time":0,"state":
        // {"accountId":null,"allowedActions":[]}}]}{"type":3,"invocationId":"1","result":1}

        this.maxPlayer = data.MaxAllow;
        this.listPlayerView.forEach(element => {
            element.MoneyType = data.MoneyType;
        });
             this.lblTable.string = ( data.MoneyType == 1 ? App.instance.getTextLang("tb112") : App.instance.getTextLang("tb113"))+ ": " + data.Name;
              this.lblTable2.string = App.instance.getTextLang("iap38")+ ": " + Utils.formatMoney(data.MinBet) + (data.MoneyType == 1?" Tipzo" :" Coin") ;
            //   this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + data.CurrentGameLoopId; 
                this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + (data.CurrentGameLoopId==-1?"":data.CurrentGameLoopId.toString()); 
              this.MoneyType = data.MoneyType;
              this.vipTable.active = data.MoneyType != 1 ?true:false;
              this.vipTable.parent.children[1].color =new cc.Color().fromHEX(data.MoneyType != 1 ?'#513b21':"#003533"); 
        this.listPlayer = [];

      
      
        if(this.maxPlayer== 2){
            this.listPlayerView[1].node.active = false;
            this.listPlayerView[3].node.active = false;
            this.listPlayerView.splice(3, 1); // Xoá phần tử ở index 2 (giá trị 3)
            this.listPlayerView.splice(1, 1);
            this.vipTable.parent.children[1].active = false;
            this.vipTable.parent.children[2].active = true;
        }else{
            this.vipTable.parent.children[1].active = true;
            this.vipTable.parent.children[2].active = false;
        }
        this.listPlayerView.forEach(element => {
            element.resetAll();
        });

      

        this.btnBo.getComponent(cc.Button).interactable = false;
        this.btnDanh.getComponent(cc.Button).interactable = false;
        this.btnXep.getComponent(cc.Button).interactable = false;
        var dataRoom = data;
        const playerIDs = Object.keys(dataRoom.Players);
        this.playSound("join_room",false);
        // Duyệt danh sách bằng vòng lặp for bình thường
        for (let i = 0; i < playerIDs.length; i++) {
            const playerID = playerIDs[i];
            const player = dataRoom.Players[playerID];
            var isExits = false;
            for (var j = 0; j < this.listPlayer.length; j++) {
                if(playerID == this.listPlayer[j]['AccountID']){
                    isExits = true;
                    break;
                }
               
            }
            if(!isExits){
                this.listPlayer.push(player);
            }
            // console.log(`Player ID: ${playerID}`);
            // console.log(`Nickname: ${player.Account.NickName}`);
            // console.log(`Gold: ${player.Account.GoldBalance}`);
            // console.log(`Status: ${player.Status}`);
            // console.log("-----------------------");
        }
        this.updateDataRoom(dataRoom);
        this.fillPlayerToSlot();
        this.startGame(dataRoom,false);


        if(extra && extra.state.accountId){
            this.startActionTimer(extra.state.accountId, extra.time,extra.state.allowedActions) ;
        }
    }
    updateDataRoom(dataRoom){
        this.dataRoom = dataRoom;
        var thiz = this;
        this.listPlayer.forEach(element => {
            element["isOwner"] = false;
            if(dataRoom.OwnerId == element.AccountID){
                element["isOwner"] = true;
            }
            const firstPart = element.AccountID.substring(0, element.AccountID.indexOf(":"))
            if (firstPart == Configs.Login.UserId) {
                thiz.playerMe = element;

            }
        });
        
    }

    clearAllCardTable() {
        this.listCardOnTable.forEach(element => {
            element.destroy();
        });
        this.listCardOnTable = [];
    }
    addCardNodeOnTable(arr: any, isReconnect: boolean) {

        this.deactiveCardOnTable();
       
        let nodezz = cc.instantiate(this.cardOnTableTemp);
      
        var cardOnTable = nodezz.getComponent(CardOnTable);


        cardOnTable.addCardOntable(arr, isReconnect, true, null);
        this.addRandomChild(nodezz);
        this.listCardOnTable.push(nodezz);


    }
    deactiveCardOnTable() {
        this.listCardOnTable.forEach(element => {
            element.getComponent(CardOnTable).deactiveCard();
        });
    }
   
  
  

    actDanhBai(){
        App.instance.inactivityTimer = 0;
        this.playSound("danhbai",false);
        var arr = this.playerViewMe.cardList.getCardSelected();
        var newNumber = arr.map(function (c) {
            return c.getIDCardSend();
        });
        cc.log(newNumber);
        if (newNumber.length > 0) {
            CardGameSignalRClient.getInstance().send('DanhBai', [newNumber], (data) => {
                cc.log(data);
                if(!data){
                    App.instance.showToast(App.instance.getTextLang("me17"));
                }
                       });
        }else{
            // App.instance.showToast("Bạn chưa chọn bài đánh");
        }




        
    }
    getPlayviewWithID(userId) {
        for (let index = 0; index < this.listPlayerView.length; index++) {
            const element = this.listPlayerView[index];
            if (element.player != null && element.player.AccountID == userId) {
                return element;
            }
        }
        return null;
    }
   
    fillPlayerToSlot() {
        var thiz = this;
       
        this.listPlayer.forEach(element => {
            var slot = this.getSlotNew(element['Position']);

            thiz.listPlayerView[slot].updateInfor(element);
        });

        
    }
    addCardOnTableFormPlayeView(arr: any, isReconnect, formNode) {
        this.deactiveCardOnTable();
        
        let nodezz = cc.instantiate(this.cardOnTableTemp);
        
        var cardOnTable = nodezz.getComponent(CardOnTable);

        cardOnTable.addCardOntable(arr, isReconnect, false, formNode);
        this.listCardOnTable.push(nodezz);

        this.addRandomChild(nodezz);

        this.listCardOnTable.push(nodezz);
    }

    randomYzzz = 0;
    addRandomChild( childNode) {
        

        // Lấy kích thước của node cha
        let parentSize = this.cardOnTableParent.getContentSize();
    
        // Lấy danh sách tất cả child hiện tại của parentNode
        let children = this.cardOnTableParent.children;
    
        // Xác định vị trí Y của child cuối cùng
        let lastChildY = null;
        if (this.randomYzzz > 0 && children.length>0 ) {
            lastChildY = children[children.length - 1].y;
           
        }
        this.randomYzzz++;
       
    
        let randomX = (Math.random() - 0.5) * parentSize.width;
        let randomY = (Math.random() - 0.5) * parentSize.height;
    
        // Nếu đã có child trước đó, đảm bảo khoảng cách tối thiểu là 20
        if (lastChildY !== null) {
            let minY = lastChildY + 20; // Đảm bảo cách ít nhất 20 đơn vị theo trục Y
            randomY = Math.max(randomY, minY);
        }
        if(children.length > 4){
            this.randomYzzz = 0;
            randomY = (Math.random() - 0.5) * parentSize.height;
        }
        // Gán vị trí cho child
        childNode.setPosition(randomX, randomY);
    
        // Random góc xoay từ -80 đến 180 độ
        let randomRotation = Math.random() * 90 - 45;
        childNode.angle = randomRotation;
        
        // Thêm vào node cha
        this.cardOnTableParent.addChild(childNode);
    }
  

    actXepBai() {
        App.instance.inactivityTimer = 0;
        // SlotUtil.PlaySound(this.sounds, "button_click");
        this.playerViewMe.cardList.reArrangeCards(function (a, b) {
            var operatorA = a.rank, operatorB = b.rank;
            if (operatorA < 3) {
                operatorA += 13;
            }
            if (operatorB < 3) {
                operatorB += 13;
            }
            return (operatorA - operatorB) * 100 + a.suit - b.suit;
        });
        this.playSound("xepbai",false);
    }

    // onTurn(data) {
    //     this.onNextTurn(data.nextUser,data.newRound);
    //     if (data.newRound) {
    //         this.clearAllCardTable();
    //         this.suggestCard();
    //     }
    //     if(this.playerMe.userId == data.passedUser){
    //         this.btnBo.active = false;
    //     }
        

    // }
  

    getSlotNew(slot) {
        var slotNew = slot - this.playerMe.Position;
        if(slotNew<0){
            slotNew =this.maxPlayer + slotNew;
        }
        return slotNew;
    }
    getIDCard(card) {
        
        return card.idCard;
    }

   
    // update (dt) {}
}
