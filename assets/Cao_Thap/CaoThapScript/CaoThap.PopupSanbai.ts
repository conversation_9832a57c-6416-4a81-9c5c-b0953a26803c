import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import {DropdownCalendar} from "./DropdownCalendar";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Http from "../../Lobby/MoveScript/Http";
import Configs from "../../Lobby/MoveScript/Configs";
import {DropdownSelector} from "./DropdownSelector";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";

const {ccclass, property} = cc._decorator;

@ccclass
export default class CaoThapPopupSanbai extends Dialog {
    @property(cc.ToggleContainer)
    private tabs: cc.ToggleContainer = null;

    @property([cc.Node])
    private tabsContent: cc.Node[] = [];

    @property(DropdownCalendar)
    dropdownCalendar: DropdownCalendar = null;

    @property(cc.Node)
    rowContainerTab1: cc.Node = null;

    @property(cc.Node)
    rowTemplateTab1: cc.Node = null;

    @property(DropdownSelector)
    dropdownSelector: DropdownSelector = null;

    @property(cc.Node)
    rowContainerTab2: cc.Node = null;

    @property(cc.Node)
    rowTemplateTab2: cc.Node = null;

    private currentDay: Date = null;
    private currentMonth: Date = null;

    onLoad() {
        this.tabs.toggleItems.forEach((tab, index) => {
            tab.node.on("toggle", () => {
                this.onTabChanged(index);
            })
        })

        let self = this;
        this.dropdownCalendar.setListener({
            onChangeDate(date: Date) {
                self.currentDay = date;
                self.showDayRanking();
            }
        })

        this.dropdownSelector.setOptions(
            generateMonthOptions(),
            (date: Date): string => {
                let month = (date.getMonth() + 1).toString().padStart(2, '0');
                let year = date.getFullYear();
                return `${month}/${year}`;
            },
            {
                onChangeOption(option: Date) {
                    self.currentMonth = new Date(option.getFullYear(), option.getMonth(), 1);
                    self.showMonthRanking();
                }
            }
        )
    }

    public show() {
        super.show();
        this.currentDay = new Date();
        this.currentMonth = new Date();
        this.tabs.node.getChildByName('tab1').getComponent(cc.Toggle).isChecked = true;
        this.onTabChanged(0);
    }

    public dismiss() {
        super.dismiss();
        this.dropdownCalendar.calendarPanel.active = false;
        this.dropdownSelector.dropdownList.active = false;
    }

    private onTabChanged(index: number) {
        if (index < 0 || index >= this.tabsContent.length) return;

        this.tabsContent.forEach(tab => tab.active = false);
        this.tabsContent[index].active = true;

        const showContentCallback = {
            [0]: () => {
                this.showDayRanking(50);
                this.dropdownCalendar.calendarPanel.active = false;
            },
            [1]: () => {
                this.showMonthRanking(50);
                this.dropdownSelector.dropdownList.active = false;
            },
            [2]: () => {
            }
        };
        showContentCallback[index]?.();
    }

    private showDayRanking(topCount: number = 10) {
        this.rowContainerTab1.removeAllChildren();
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetTopEventHiLo'], {
            "currencyID": Configs.Login.CurrencyID,
            "eventType": 1,
            "topCount": topCount,
            "eventDate": formatEventDate(this.currentDay)
        }, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200) {
                for (let i = 0; i < response["d"].length; i++) {
                    let result = response["d"][i];
                    let itemRow = cc.instantiate(this.rowTemplateTab1);
                    itemRow.active = true;
                    this.rowContainerTab1.addChild(itemRow);
                    itemRow.children[0].getComponent(cc.Label).string = result.Position.toString();
                    itemRow.children[1].getComponent(cc.Label).string = result.Nickname;
                    itemRow.children[2].getComponent(cc.Label).string = result.CardWinData;
                    itemRow.children[3].getComponent(cc.Label).string = Utils.formatNumber(result.PrizeValue);
                    itemRow.children[4].getComponent(cc.Label).string = Utils.formatDatetime(result.FinishedTime, "HH:mm:ss");
                    let rank = result.Position;
                    if (rank === 1) {
                        itemRow.children[5].getComponent(cc.Label).string = Utils.formatNumber(2_000_000);
                    } else if (rank === 2) {
                        itemRow.children[5].getComponent(cc.Label).string = Utils.formatNumber(1_000_000);
                    } else if (rank === 3) {
                        itemRow.children[5].getComponent(cc.Label).string = Utils.formatNumber(500_000);
                    } else {
                        itemRow.children[5].getComponent(cc.Label).string = "";
                    }
                }
            }
        })
    }

    private showMonthRanking(topCount: number = 10) {
        this.rowContainerTab2.removeAllChildren();
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['GetTopEventHiLo'], {
            "currencyID": Configs.Login.CurrencyID,
            "eventType": 2,
            "topCount": topCount,
            "eventDate": formatEventDate(this.currentMonth)
        }, (status, response) => {
            App.instance.showLoading(false);
            if (status === 200) {
                for (let i = 0; i < response["d"].length; i++) {
                    let result = response["d"][i];
                    let itemRow = cc.instantiate(this.rowTemplateTab2);
                    itemRow.active = true;
                    this.rowContainerTab2.addChild(itemRow);
                    itemRow.children[0].getComponent(cc.Label).string = result.Position.toString();
                    itemRow.children[1].getComponent(cc.Label).string = result.Nickname;
                    itemRow.children[2].getComponent(cc.Label).string = result.CardWinData;
                    itemRow.children[3].getComponent(cc.Label).string = Utils.formatNumber(result.PrizeValue);
                    itemRow.children[4].getComponent(cc.Label).string = Utils.formatDatetime(result.FinishedTime, "dd/MM/yyyy");
                    itemRow.children[5].getComponent(cc.Label).string =
                        result.Position === 1 ? "IPhone 16 Pro Max\n256GB" : "";
                }
            }
        })
    }
}

// Support functions

function formatEventDate(date: Date): string {
    return encodeURIComponent(date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: '2-digit',
        year: 'numeric'
    }).replace(',', ''));
}

function generateMonthOptions(): Date[] {
    const months: Date[] = [];
    const now = new Date();

    for (let i = 0; i < 12; i++) {
        let date = new Date(now);
        date.setMonth(now.getMonth() - i);
        months.push(date);
    }

    return months;
}
