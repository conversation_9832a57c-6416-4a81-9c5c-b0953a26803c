import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Http from "../../Lobby/MoveScript/Http";
import Configs from "../../Lobby/MoveScript/Configs";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Pagination from "../../Lobby/LobbyScript/Pagination";

const {ccclass, property} = cc._decorator;

@ccclass
export class CaoThapPopupLSH extends Dialog {
    @property(cc.ToggleContainer)
    private tabs: cc.ToggleContainer = null;

    @property([cc.Node])
    private tabsContent: cc.Node[] = [];

    @property(cc.Node) private contentTipzo: cc.Node = null;
    @property(cc.Node) private rowTipzo: cc.Node = null;
    @property(Pagination) private paginationTipzo: Pagination = null;


    @property(cc.Node) private contentXu: cc.Node = null;
    @property(cc.Node) private rowXu: cc.Node = null;
    @property(Pagination) private paginationXu: Pagination = null;
    
    @property(cc.SpriteFrame) private rowBackground1: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) private rowBackground2: cc.SpriteFrame = null;

    private _dataTipzoLoaded = null;
    private _dataXuLoaded = null;

    onLoad() {
        this.tabs.toggleItems.forEach((tab, index) => {
            tab.node.on("toggle", () => {
                this.onTabChanged(index);
            })
        })

        this.paginationTipzo.initListener((page: number) => {
            this.showTipzoHistory(page);
        });

        this.paginationXu.initListener((page: number) => {
            this.showXuHistory(page);
        });
    }

    public show() {
        super.show();
        
        this.fetchTipzoHistory();
        this.fetchXuHistory();
    }

    private onTabChanged(index: number) {
        if (index < 0 || index >= this.tabsContent.length) return;

        this.tabsContent.forEach(tab => tab.active = false);
        this.tabsContent[index].active = true;
    }

    private showTipzoHistory(page: number) {
        this.contentTipzo.removeAllChildren();
        this.paginationTipzo.updatePagination(Math.ceil(this._dataTipzoLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataTipzoLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = cc.instantiate(this.rowTipzo);
            itemRow.active = true;
            this.contentTipzo.addChild(itemRow);
            itemRow.children[0].getComponent(cc.Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(cc.Label).string = data.Nickname;
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumberMin(this.getRoomValue(1, data.RoomID));
            itemRow.children[3].getComponent(cc.Label).string = Utils.formatNumber(data.PrizeValue);
            itemRow.children[4].getComponent(cc.Label).string = data.Type == 0 ? App.instance.getTextLang('sl16') : App.instance.getTextLang('sl15');
            itemRow.getComponent(cc.Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    private showXuHistory(page: number) {
        this.contentXu.removeAllChildren();
        this.paginationXu.updatePagination(Math.ceil(this._dataXuLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataXuLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = cc.instantiate(this.rowXu);
            itemRow.active = true;
            this.contentXu.addChild(itemRow);
            itemRow.children[0].getComponent(cc.Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(cc.Label).string = data.Nickname;
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumberMin(this.getRoomValue(2, data.RoomID));
            itemRow.children[3].getComponent(cc.Label).string = Utils.formatNumber(data.PrizeValue);
            itemRow.children[4].getComponent(cc.Label).string = data.Type == 0 ? App.instance.getTextLang('sl16') : App.instance.getTextLang('sl15');
            itemRow.getComponent(cc.Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    dismiss() {
        super.dismiss();
    }

    private fetchTipzoHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopWinnersHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 1,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataTipzoLoaded = json["d"];
                this.showTipzoHistory(1);
            }
        }
        );
    }

    private fetchXuHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopWinnersHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 2,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataXuLoaded = json["d"];
                this.showXuHistory(1);
            }
        }
        );
    }

    getRoomValue(betType: number, roomID: number) {
        if (betType === 1) {
            return [0, 1_000, 10_000, 50_000, 100_000, 500_000][roomID];
        } else if (betType === 2) {
            return [0, 10_000, 100_000, 500_000, 1_000_000, 5_000_000][roomID];
        }
    }
}

