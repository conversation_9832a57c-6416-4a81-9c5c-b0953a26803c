import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import Http from "../../Lobby/MoveScript/Http";
import Configs from "../../Lobby/MoveScript/Configs";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Pagination from "../../Lobby/LobbyScript/Pagination";

const { ccclass, property } = cc._decorator;

//Sửa lại lần lượt: <PERSON><PERSON><PERSON>, Bước, Kết quả, cửa đặt, Nhận

@ccclass
export class CaoThapPopupLSC extends Dialog {

    @property(cc.ToggleContainer)
    private tabs: cc.ToggleContainer = null;

    @property([cc.Node])
    private tabsContent: cc.Node[] = [];

    @property(cc.Node) private contentTipzo: cc.Node = null;
    @property(cc.Node) private rowTipzo: cc.Node = null;
    @property(Pagination) private paginationTipzo: Pagination = null;

    @property(cc.Node) private contentXu: cc.Node = null;
    @property(cc.Node) private rowXu: cc.Node = null;
    @property(Pagination) private paginationXu: Pagination = null;

    @property(cc.SpriteFrame) private rowBackground1: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) private rowBackground2: cc.SpriteFrame = null;

    private _dataTipzoLoaded = null;
    private _dataXuLoaded = null;

    onLoad() {
        this.tabs.toggleItems.forEach((tab, index) => {
            tab.node.on("toggle", () => {
                this.onTabChanged(index);
            })
        })
        this.paginationTipzo.initListener((page: number) => {
            this.showTipzoHistory(page);
        });

        this.paginationXu.initListener((page: number) => {
            this.showXuHistory(page);
        });
    }

    public show() {
        super.show();
        this.fetchTipzoHistory();
        this.fetchXuHistory();
    }

    private onTabChanged(index: number) {
        if (index < 0 || index >= this.tabsContent.length) return;

        this.tabsContent.forEach(tab => tab.active = false);
        this.tabsContent[index].active = true;

        const showContentCallback = {
            [0]: () => this.showTipzoHistory(1),
            [1]: () => this.showXuHistory(1),
        };
        showContentCallback[index]?.();
    }

    private showTipzoHistory(page: number) {
        this.contentTipzo.removeAllChildren();
        this.paginationTipzo.updatePagination(Math.ceil(this._dataTipzoLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataTipzoLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = cc.instantiate(this.rowTipzo);
            itemRow.active = true;
            this.contentTipzo.addChild(itemRow);
            itemRow.children[0].getComponent(cc.Label).string = data.TurnID;
            itemRow.children[1].getComponent(cc.Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumber(data.BetValue);
            itemRow.children[3].getComponent(cc.Label).string = data.Step;
            itemRow.children[4].getComponent(cc.RichText).string = data.CardID >= 0 ? convertCardSymbols(data.CardID.toString()) : "";
            if (data.Step == 1) {
                itemRow.children[5].getComponent(cc.Label).string = "";
                itemRow.children[6].getComponent(cc.Label).string = `-${Utils.formatNumber(data.PrizeValue)}`
            } else {
                itemRow.children[5].getComponent(cc.Label).string = data.LocationID == 0 ? App.instance.getTextLang('hl14') : App.instance.getTextLang('hl13');
                itemRow.children[6].getComponent(cc.Label).string = `${Utils.formatNumber(data.PrizeValue)}`;
            }
            itemRow.getComponent(cc.Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    private showXuHistory(page: number) {
        this.contentXu.removeAllChildren();
        this.paginationXu.updatePagination(Math.ceil(this._dataXuLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataXuLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = cc.instantiate(this.rowXu);
            itemRow.active = true;
            this.contentXu.addChild(itemRow);
            itemRow.children[0].getComponent(cc.Label).string = data.TurnID;
            itemRow.children[1].getComponent(cc.Label).string = Utils.formatDatetime(data.CreatedTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumber(data.BetValue);
            itemRow.children[3].getComponent(cc.Label).string = data.Step;
            itemRow.children[4].getComponent(cc.RichText).string = data.CardID >= 0 ? convertCardSymbols(data.CardID.toString()) : "";
            if (data.Step == 1) {
                itemRow.children[5].getComponent(cc.Label).string = "";
                itemRow.children[6].getComponent(cc.Label).string = `-${Utils.formatNumber(data.PrizeValue)}`
            } else {
                itemRow.children[5].getComponent(cc.Label).string = data.LocationID == 0 ? App.instance.getTextLang('hl14') : App.instance.getTextLang('hl13');
                itemRow.children[6].getComponent(cc.Label).string = `${Utils.formatNumber(data.PrizeValue)}`;
            }
            itemRow.getComponent(cc.Sprite).spriteFrame = ithRow % 2 === 0 ? this.rowBackground1 : this.rowBackground2;
            ithRow++;
        }
    }

    public dismiss() {
        super.dismiss();
    }

    private fetchTipzoHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetAccountHistoryHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 1,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataTipzoLoaded = json["d"];
                this.showTipzoHistory(1);
            }
        }
        );
    }

    private fetchXuHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetAccountHistoryHiLo"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 2,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataXuLoaded = json["d"];
                this.showXuHistory(1);
            }
        }
        );
    }

}


function convertCardSymbols(cardIds: string) {
    if (cardIds === "") return "";

    const ranks = ["2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"];
    const suits = ["♠", "♣", "♦", "♥"]; // Spades, Clubs, Diamonds, Hearts

    return cardIds.replace(/\d+/g, (match) => {
        const cardId = parseInt(match);
        if (isNaN(cardId) || cardId < 0 || cardId > 51) return match; // Return original if invalid
        const suitIndex = Math.floor(cardId / 13);
        const rankIndex = cardId % 13;

        const suit = suits[suitIndex];
        const rank = ranks[rankIndex];

        return `${rank}${suit}`;
    });
}
