import MiniGame from "../../Lobby/LobbyScript/MiniGame";
import App from "../../Lobby/LobbyScript/Script/common/App";
import CaoThapPopupGuide from "./CaoThap.PopupGuide";
import { CaoThapPopupLSC } from "./CaoThap.PopupLSC";
import { CaoThapPopupLSH } from "./CaoThap.PopupLSH";
import CaoThapPopupSanbai from "./CaoThap.PopupSanbai";
import MiniGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/MiniGameSignalRClient";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Configs from "../../Lobby/MoveScript/Configs";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";

const { ccclass, property } = cc._decorator;

@ccclass
export default class CaoThapController extends MiniGame {

    @property(cc.Prefab) private popupGuidePrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupLSCPrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupLSHPrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupSanbaiPrefab: cc.Prefab = null;

    @property(cc.Node) private popupContainer: cc.Node = null;

    @property(cc.Label)
    private lblSession: cc.Label = null;

    @property(cc.Label)
    private lblUp: cc.Label = null;

    @property(cc.Label)
    private lblDown: cc.Label = null;

    @property([cc.Toggle])
    private betButtons: cc.Toggle[] = [];

    @property(cc.Button)
    private btnBetHighGate: cc.Button = null;

    @property(cc.Button)
    private btnBetLowGate: cc.Button = null;

    @property(cc.Button)
    private btnNewTurn: cc.Button = null;

    @property(cc.Button)
    private btnStart: cc.Button = null;

    // @property(cc.Toggle)
    // private toggleChangeBetType: cc.Toggle = null;

    @property(cc.Button)
    private btnChangeBetTypeBig: cc.Button = null;

    @property(cc.Button)
    private btnChangeBetTypeSmall: cc.Button = null;

    @property(cc.Label)
    private lblJackpotValue: cc.Label = null;

    @property(cc.Label)
    private lblSelectedBetValue: cc.Label = null;

    @property([cc.Node])
    private atSlot: cc.Node[] = [];

    @property(cc.Label)
    private lblTime: cc.Label = null;

    @property(cc.Sprite)
    private sprCard: cc.Sprite = null;

    @property(cc.SpriteAtlas)
    private sprCardAtlas: cc.SpriteAtlas = null;

    @property(cc.Node)
    private rewardFloatUp: cc.Node = null;

    @property(cc.Animation)
    private jackpotAnimation: cc.Animation = null;

    @property(cc.RichText)
    private informationTurn: cc.RichText = null;

    @property(cc.SpriteFrame)
    private xuIcon: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private tipzoIcon: cc.SpriteFrame = null;

    private popupGuide: CaoThapPopupGuide = null;
    private popupLSC: CaoThapPopupLSC = null;
    private popupLSH: CaoThapPopupLSH = null;
    private popupSanbai: CaoThapPopupSanbai = null;

    private _betType: number = 1;
    private _roomID: number = 1;
    private _acesRevealedCount: number = 0;
    private _currentTurnID: number = 0;
    private _currentTime: number = 120;
    private _isSpinning = false;
    private latestResultAccountInfo: any = null;

    private countDownIntervalID: number | undefined;

    private set currentTurnID(value: number) {
        this._currentTurnID = value;
        this.lblSession.string = "#" + value.toString();
    }

    private set currentTime(value: number) {
        this._currentTime = value;
        this.lblTime.string = formatTimer(value);
    }

    private get currentTime(): number {
        return this._currentTime;
    }

    // private set betType(value: number) {
    //     this._betType = value;
    //     this.invokeGetJackpotHiLo();

    //     this.toggleChangeBetType.isChecked = value === 2;
    //     this.resetBetValue();
    //     this.lblSelectedBetValue.string = Utils.formatNumber(this.getBetValue(this.roomID));
    // }

    private get betType(): number {
        return this._betType;
    }

    private set roomID(value: number) {
        this._roomID = value;
        this.invokeGetJackpotHiLo();

        this.betButtons.forEach((btn, j) => {
            btn.isChecked = (j + 1) === value;
        });
        this.lblSelectedBetValue.string = Utils.formatNumber(this.getBetValue(this.roomID));
    }

    private get roomID(): number {
        return this._roomID;
    }

    private set acesRevealedCount(value: number) {
        this._acesRevealedCount = value;
        this.atSlot.forEach((slot, i) => {
            slot.active = i < value;
        })
    }

    onLoad() {
        super.onLoad();
        this.initHubs();
        this.initListeners();
    }

    start() {
        this._betType = 1;
        this.roomID = 1;
        this.updateChangeBetType();
    }

    private initHubs() {
        MiniGameSignalRClient.getInstance().receive("jackpotHiLo", (data) => {
            cc.log("jackpotHiLo", data);
            Tween.numberTo(this.lblJackpotValue, data.JackpotFund, 0.25);
        })

        MiniGameSignalRClient.getInstance().receive("resultHiLoAccountInfo", (data) => {

            cc.log("resume data", data);

            if (data.currentTurnId > 0) {
                this.disableBetButton();
                this.informationTurn.string = convertCardSymbols(data.currentCardData);
                this.lblSession.string = '#' + data.currentTurnId;
                this.currentTime = data.remainTime;
                this.acesRevealedCount = data.acesCount;
                this.rewardFloatUp.getComponent(cc.Label).string = '';

                this.lblUp.string = this.getWinGatePrize(data.betRateUp, data.currentBetValue);
                this.lblDown.string = this.getWinGatePrize(data.betRateDown, data.currentBetValue);

                this.btnBetHighGate.interactable = data.betRateUp > 0;
                this.btnBetLowGate.interactable = data.betRateDown > 0;
                this.btnNewTurn.interactable = true;

                this.sprCard.spriteFrame = this.sprCardAtlas.getSpriteFrame(`card_${this.getLastCardID(data.currentCardData)}`);

                this.btnStart.node.active = false;

                this.lblSelectedBetValue.string = Utils.formatNumber(data.currentBetValue);

                this.countDownIntervalID = setInterval(() => {
                    this.updateCountDown()
                }, 1000);

                if (data.currentStep == 1) {
                    this.btnNewTurn.interactable = false;
                }

            } else {
                this.initTable();
                this.acesRevealedCount = 0;
                this.btnStart.node.active = true;
            }
            this.invokeGetJackpotHiLo();
        })

        MiniGameSignalRClient.getInstance().receive("resultHiLoSetBet", async (data) => {

            if (this.countDownIntervalID) {
                clearInterval(this.countDownIntervalID);
            }
            this.currentTime = 120;

            cc.log("resultHiLoSetBet", data);
            this.latestResultAccountInfo = data;
            this.lblSession.string = "#" + data.turnId;

            if (data.cardId === 0 && data.balance === -1) {
                this.initTable();
                this.showReward(data.prizeValue);
                this.scheduleOnce(() => {
                    this.btnStart.node.active = true;
                    this.acesRevealedCount = 0;
                }, 1.5)
            }
            else {
                await this.spinCard_v2(data.cardId);
                this.countDownIntervalID = setInterval(() => {
                    this.updateCountDown()
                }, 1000);
                this.updateBalance(data.balance);

                this.acesRevealedCount = data.cardData.split(",").filter(num => [12, 25, 38, 51].includes(parseInt(num))).length;

                if (data.prizeValue > 0) {  // Choose correct
                    this.lblSelectedBetValue.string = Utils.formatNumber(data.prizeValue);
                    this.lblUp.string = this.getWinGatePrize(data.betRateUp, data.prizeValue);
                    this.lblDown.string = this.getWinGatePrize(data.betRateDown, data.prizeValue);
                    this.btnBetHighGate.interactable = data.betRateUp > 0;
                    this.btnBetLowGate.interactable = data.betRateDown > 0;
                    this.btnNewTurn.interactable = true;
                    this.informationTurn.string = convertCardSymbols(data.cardData);
                    if (data.isJackpot) {
                        this.showJackpotHilo();
                    }
                }
                else {    // Choose fail, end game
                    this.btnBetHighGate.interactable = false;
                    this.btnBetLowGate.interactable = false;
                    this.btnNewTurn.interactable = false;
                    this.showReward(data.prizeValue);
                    this.scheduleOnce(() => {
                        this.initTable();
                        this.btnStart.node.active = true;
                        this.acesRevealedCount = 0;
                    }, 2.3)
                }
            }


            // Step 1 ko cho kết thúc lượt chơi ngay
            if (data.step == 1) {
                this.btnNewTurn.interactable = false;
            }
        })
    }

    private initTable() {
        this.enableBetButton();
        this.informationTurn.string = App.instance.getTextLang('hl2');
        this.rewardFloatUp.opacity = 0;
        this.lblSession.string = '#0';
        this.currentTime = 120;
        // this.acesRevealedCount = 0;

        this.lblUp.string = '';
        this.lblDown.string = '';

        this.btnBetHighGate.interactable = false;
        this.btnBetLowGate.interactable = false;
        this.btnNewTurn.interactable = false;

        //this.sprCard.spriteFrame = this.sprCardAtlas.getSpriteFrame(`card_0`);

        //this.btnStart.node.active = true;

        this.lblSelectedBetValue.string = Utils.formatNumber(this.getBetValue(this.roomID));
        if (this.countDownIntervalID) {
            clearInterval(this.countDownIntervalID);
        }
    }

    updateBalance(newBalance: number) {
        if (this.betType === 1) {
            Configs.Login.GoldCoin = newBalance;
        }
        else if (this.betType === 2) {
            Configs.Login.Coin = newBalance;
        }
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    addBalance(amount: number) {
        if (this.betType === 1) {
            Configs.Login.GoldCoin += amount;
        }
        else if (this.betType === 2) {
            Configs.Login.Coin += amount;
        }
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    enableBetButton() {
        for (let i = 0; i < this.betButtons.length; i++) {
            this.betButtons[i].interactable = true;
        }
        this.btnChangeBetTypeBig.interactable = true;
        this.btnChangeBetTypeSmall.interactable = true;
    }

    disableBetButton() {
        for (let i = 0; i < this.betButtons.length; i++) {
            this.betButtons[i].interactable = false;
        }
        this.btnChangeBetTypeBig.interactable = false;
        this.btnChangeBetTypeSmall.interactable = false;
    }

    getWinGatePrize(ratio: number, betValue: number): string {
        if (ratio === 0) return "";
        return Utils.formatNumber(Math.floor(betValue * ratio));
    }

    getLastCardID(s: string): number | null {
        const numbers = s.split(",").map(num => parseInt(num.trim(), 10));
        return numbers.length ? numbers[numbers.length - 1] : null;
    }

    private initListeners() {
        for (let i = 0; i < this.betButtons.length; i++) {
            this.betButtons[i].node.on("toggle", () => {
                this.roomID = i + 1;
                this.lblSelectedBetValue.string = Utils.formatNumber(this.getBetValue(this.roomID));
                this.betButtons.forEach((btn, j) => {
                    btn.isChecked = (j + 1) === this.roomID;
                });
            })
        }
    }

    show() {
        if (this.node.active) {
            super.reOrder();
            return;
        }
        super.show();
        this.invokeGetAccountInfo();
    }

    dismiss() {
        super.dismiss();
        this.invokeHideHiLo();
        App.instance.hideGameMini("CaoThap")
    }

    private updateCountDown() {
        if (this.currentTime > 0) {
            this.currentTime--;
        } else {
            if (this.countDownIntervalID) {
                clearInterval(this.countDownIntervalID);
            }
            // random actDown or actUp
            if (Math.random() > 0.5) {
                this.actUp();
            } else {
                this.actDown();
            }
        }
    }

    private async showReward(value: number): Promise<void> {
        this.rewardFloatUp.getComponent(cc.Label).string = "+" + Utils.formatNumber(value);
        let originalPosition = this.rewardFloatUp.getPosition();
        let delay = 1.5;
        if (value > 0) {
            this.rewardFloatUp.opacity = 255;
            let step = this.latestResultAccountInfo.step;
            let text = "";
            if (this.betType === 1) {
                text = App.instance.getTextLang('hi54');
            }
            else {
                text = App.instance.getTextLang('hi55');

            }
            text = text.replace('{0}', Utils.formatNumber(value));
            text = text.replace('{1}', step);
            this.informationTurn.string = `<color=#ff0000>${text}</color>`;
        }
        else {
            delay = 2.2;
            this.rewardFloatUp.opacity = 0;
            this.informationTurn.string = `<color=#ff0000>${App.instance.getTextLang('me37')}</color>`;
        }

        return new Promise(resolve => {
            cc.tween(this.rewardFloatUp)
                .by(delay, { y: 100 })
                .call(() => {
                    this.informationTurn.string = App.instance.getTextLang('hl2');
                    this.rewardFloatUp.setPosition(originalPosition);
                    this.rewardFloatUp.opacity = 0;
                    resolve();
                })
                .start();
        })
    }


    private showJackpotHilo() {
        this.jackpotAnimation.node.active = true;
        this.jackpotAnimation.play("jackpot");
        let duration = this.jackpotAnimation.currentClip.duration;
        this.scheduleOnce(() => {
            this.invokeGetAccountInfo();
        }, duration);
        this.scheduleOnce(() => {
            this.jackpotAnimation.node.active = false;
        }, duration + 2);
    }

    private getBetValue(roomID: number) {
        if (this._betType === 1) {
            return [0, 1_000, 10_000, 50_000, 100_000, 500_000][roomID];
        } else if (this._betType === 2) {
            return [0, 10_000, 100_000, 500_000, 1_000_000, 5_000_000][roomID];
        }
    }

    private resetBetValue() {
        this.betButtons.forEach((btn, index) => {
            const bg = btn.node.getChildByName("Background");
            const checkMark = btn.node.getChildByName("checkmark");

            const value = this.getBetValue(index + 1);

            bg.getComponentInChildren(cc.Label).string = Utils.formatNumberMin(value);
            checkMark.getComponentInChildren(cc.Label).string = Utils.formatNumberMin(value);
        })
    }

    private activePopupLSC() {
        if (!this.popupLSC) {
            this.popupLSC = cc.instantiate(this.popupLSCPrefab).getComponent(CaoThapPopupLSC);
            this.popupLSC.node.parent = this.popupContainer;
        }
        this.popupLSC.show();
    }

    private activePopupLSH() {
        if (!this.popupLSH) {
            this.popupLSH = cc.instantiate(this.popupLSHPrefab).getComponent(CaoThapPopupLSH);
            this.popupLSH.node.parent = this.popupContainer;
        }
        this.popupLSH.show();
    }

    private activePopupGuide() {
        if (!this.popupGuide) {
            this.popupGuide = cc.instantiate(this.popupGuidePrefab).getComponent(CaoThapPopupGuide);
            this.popupGuide.node.parent = this.popupContainer;
        }
        this.popupGuide.show();
    }

    private activePopupSanbai() {
        if (!this.popupSanbai) {
            this.popupSanbai = cc.instantiate(this.popupSanbaiPrefab).getComponent(CaoThapPopupSanbai);
            this.popupSanbai.node.parent = this.popupContainer;
        }
        this.popupSanbai.show();
    }

    private invokeGetAccountInfo() {
        MiniGameSignalRClient.getInstance().send('GetAccountInfoHiLo',
            [Configs.Login.CurrencyID],
            (data) => {
            }
        )
    }

    private invokeGetJackpotHiLo() {
        MiniGameSignalRClient.getInstance().send('GetJackpotHiLo',
            [Configs.Login.CurrencyID, this.betType, this.roomID],
            (data) => {
            }
        )
    }

    private invokeHideHiLo() {
        MiniGameSignalRClient.getInstance().send('HideHiLo', [], (data) => {
        })
    }

    actChangeBetType(){
        this._betType = this._betType === 1 ? 2 : 1;
        this.updateChangeBetType();
    }

    updateChangeBetType(){
        this.invokeGetJackpotHiLo();
        let icon : cc.SpriteFrame = null;
        if(this._betType === 1){
            icon = this.tipzoIcon;
        }
        else{
            icon = this.xuIcon;
        }
        this.btnChangeBetTypeBig.node.getChildByName("Background").getComponent(cc.Sprite).spriteFrame = icon;
        this.btnChangeBetTypeSmall.node.getChildByName("Background").getComponent(cc.Sprite).spriteFrame = icon;
        this.resetBetValue();
        this.lblSelectedBetValue.string = Utils.formatNumber(this.getBetValue(this.roomID));
    }

    actStart() {
        MiniGameSignalRClient.getInstance().send('SetBetHiLo',
            [Configs.Login.CurrencyID, this.betType, 0, 1, this.roomID],
            (data) => {
                if (data >= 0) {
                    this.btnStart.node.active = false;
                    this.disableBetButton();
                }
                else {
                    this.showError(data);
                }
            }
        )
    }

    actStop() {
        MiniGameSignalRClient.getInstance().send('SetBetHiLo',
            [Configs.Login.CurrencyID, this.betType, 2, 0, this.roomID],
            (data) => {
                if (data >= 0) {
                    this.btnNewTurn.interactable = false;
                }
                else {
                    this.showError(data);
                }
            }
        )
    }

    actUp() {
        MiniGameSignalRClient.getInstance().send('SetBetHiLo',
            [Configs.Login.CurrencyID, this.betType, 1, 1, this.roomID],
            (data) => {
                if (data >= 0) {
                    this.btnBetHighGate.interactable = false;
                    this.btnBetLowGate.interactable = false;
                    this.btnNewTurn.interactable = false;
                } else {
                    this.showError(data);
                }
            }
        )
    }

    actDown() {
        MiniGameSignalRClient.getInstance().send('SetBetHiLo',
            [Configs.Login.CurrencyID, this.betType, 1, 0, this.roomID],
            (data) => {
                if (data >= 0) {
                    this.btnBetHighGate.interactable = false;
                    this.btnBetLowGate.interactable = false;
                    this.btnNewTurn.interactable = false;
                }
                else {
                    this.showError(data);
                }
            }
        )
    }

    private async spinCard_v2(id: string): Promise<void> {
        const frameDelay = 70; // milliseconds between frames

        const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

        for (let i = 0; i < 15; i++) {
            const randomId = Math.floor(Math.random() * 52); // random from 0 to 51
            const frameName = `card_${randomId}`;
            const spriteFrame = this.sprCardAtlas.getSpriteFrame(frameName);
            if (spriteFrame) {
                this.sprCard.spriteFrame = spriteFrame;
            }
            await wait(frameDelay);
        }

        // Set final sprite
        const finalFrame = this.sprCardAtlas.getSpriteFrame(`card_${id}`);
        if (finalFrame) {
            this.sprCard.spriteFrame = finalFrame;
        }
    }

    private showError(code: number) {
        App.instance.ShowAlertDialog(App.instance.getTextLang("me" + code));
    }

}

function formatTimer(seconds: number) {
    let minutes = Math.floor(seconds / 60);
    let secs = seconds % 60;

    let minStr = minutes.toString().padStart(2, "0");
    let secsStr = secs.toString().padStart(2, "0");
    return `${minStr}:${secsStr}`;
}

function convertCardSymbols(cardIds: string) {
    if (cardIds === "") return "";

    const ranks = ["2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"];
    const suits = ["♠", "♣", "♦", "♥"]; // Spades, Clubs, Diamonds, Hearts

    return cardIds.replace(/\d+/g, (match) => {
        const cardId = parseInt(match);
        if (isNaN(cardId) || cardId < 0 || cardId > 51) return match; // Return original if invalid
        const suitIndex = Math.floor(cardId / 13);
        const rankIndex = cardId % 13;

        const suit = suits[suitIndex];
        const rank = ranks[rankIndex];

        // Apply color formatting for suits
        let color = (suit === "♦" || suit === "♥") ? "#ff0000" : "#000000"; // Red for ♦♥, Black for ♠♣
        return `${rank}<color=${color}>${suit}</color>`;
    });
}


// Choi xu 100K : 1 2 0 1 2
// Low: 1 2 1 0 2
// High: 1 2 1 1 2
// End game: 1 2 2 1 2

// Choi gold 1K: 1 1 0 1 1
// High: 1 1 1 1 1


