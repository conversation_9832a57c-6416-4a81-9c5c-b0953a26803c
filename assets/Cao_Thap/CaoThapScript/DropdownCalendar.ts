const {ccclass, property, menu} = cc._decorator;

export interface Listener {
    onChangeDate?(date: Date): void;
}

@ccclass('DropdownCalendar')
@menu("CustomUI/DropdownCalendar")
export class DropdownCalendar extends cc.Component {
    @property(cc.Node) calendarPanel: cc.Node = null;
    @property(cc.Label) private selectedDateLabel: cc.Label = null;
    @property(cc.Node) private dayContainer: cc.Node = null;
    @property(cc.Label) private headerLabel: cc.Label = null;
    @property(cc.Node) private dayTemplate: cc.Node = null;

    private currentDate: Date = new Date();
    private _listener: Listener = {};

    start() {
        this.calendarPanel.active = false;
        this.updateCalendar();
        this.selectedDateLabel.string = this.formatDate(this.currentDate);

        this.initCurrentDate();
    }

    public setListener(listener: Listener) {
        this._listener = listener;
    }

    private toggleCalendar() {
        this.calendarPanel.active = !this.calendarPanel.active;
    }

    private updateCalendar() {
        let year = this.currentDate.getFullYear();
        let month = this.currentDate.getMonth();
        let firstDay = new Date(year, month, 1).getDay();
        let daysInMonth = new Date(year, month + 1, 0).getDate();

        this.headerLabel.string = this.formatDate(this.currentDate);

        this.dayContainer.removeAllChildren();

        for (let i = 0; i < firstDay; i++) {
            let emptyNode = cc.instantiate(this.dayTemplate);
            emptyNode.active = true;
            emptyNode.opacity = 0;
            this.dayContainer.addChild(emptyNode);
        }

        for (let day = 1; day <= daysInMonth; day++) {
            let dayNode = cc.instantiate(this.dayTemplate);
            dayNode.active = true;
            dayNode.opacity = 255;
            dayNode.on(cc.Node.EventType.TOUCH_START, () => {
                this.selectDate(day);
            });
            let label = dayNode.getComponentInChildren(cc.Label)
            label.string = day.toString().padStart(2, '0');
            this.dayContainer.addChild(dayNode);
        }
    }

    public selectDate(day: number) {
        this.currentDate.setDate(day);
        this.selectedDateLabel.string = this.formatDate(this.currentDate);
        this._listener.onChangeDate && this._listener.onChangeDate(this.currentDate);
    }

    initCurrentDate(){
        let dayOfMonth = this.currentDate.getDate();
        for(let i = 0; i < this.dayContainer.childrenCount; i++){
            if(i == dayOfMonth - 1){
                this.dayContainer.children[i].getComponent(cc.Toggle).check();
                break;
            }
        }
    }

    private changeMonth(delta: number) {
        this.currentDate.setMonth(this.currentDate.getMonth() + delta);
        this.updateCalendar();
    }

    private formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Ensure two digits
        const day = date.getDate().toString().padStart(2, '0'); // Ensure two digits
        return `${day}/${month}/${year}`;
    }

    private onClickNavigate(button: cc.Button, step: string) {
        this.changeMonth(parseInt(step));
    }
}
