import ForestPopupLSC from "./ForestPopupLSC";
import ForestPopupLSH from "./ForestPopupLSH";
import ForestPopupGuide from "./ForestPopupGuide";
import Configs from "../../Lobby/MoveScript/Configs";
import App from "../../Lobby/LobbyScript/Script/common/App";
import ForestPopupSelectLine from "./ForestPopupSelectLine";
import ForestSignalRClient from "../../Lobby/LobbyScript/Script/networks/ForestSignalRClient";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import ForestPopupBonus from "./ForestPopupBonus";
import { EffectCreator, EnumEffect, SlotPlayerResponse } from "../../Lobby/Slot/SlotConfig";
import { SlotMachine_v2 } from "./SlotMachine/SlotMachine_v2";
import Http from "../../Lobby/MoveScript/Http";
import { ForestTrial } from "./ForestTrial";
import { AUDIO_CLIP, ForestAudioManager } from "./ForestAudioManager";

const { ccclass, property, menu } = cc._decorator;


@ccclass
@menu('Forest/Controller')
export default class ForestController extends cc.Component {
    @property(cc.Node) autoFrame: cc.Node = null;

    @property(ForestPopupLSC) popupLSC: ForestPopupLSC = null;
    @property(ForestPopupLSH) popupLSH: ForestPopupLSH = null;
    @property(ForestPopupGuide) popupGuide: ForestPopupGuide = null;

    @property(cc.Label) lblNumberOfLines: cc.Label = null;
    @property(cc.Label) lblBalance: cc.Label = null;
    @property(cc.Label) lblBet: cc.Label = null;
    @property(cc.Label) lblFreeSpin: cc.Label = null;
    @property(cc.Label) lblTotalBet: cc.Label = null;
    @property(cc.Label) lblWinCash: cc.Label = null;
    @property(cc.Label) lblJackpot: cc.Label = null;
    @property(cc.Label) lblSession: cc.Label = null;
    @property(cc.Label) lblPayLine: cc.Label = null;
    @property(cc.Label) lblKeep: cc.Label = null;
    @property(cc.Label) lblHeSoNhan: cc.Label = null;
    @property(cc.Label) lblTrialBalance: cc.Label = null;
    @property(cc.Label) lblFreeTicket: cc.Label = null;

    @property(cc.Label) lblAutoSpin: cc.Label = null;
    @property(cc.Node) textSpin: cc.Node = null;
    @property(cc.Node) textStop: cc.Node = null;
    @property(cc.Node) textAutoSpin: cc.Node = null;

    @property(cc.Button) btnSpin: cc.Button = null;
    @property(cc.Toggle) toggleTrial: cc.Toggle = null;
    @property(cc.Button) btnStopAutoSpin: cc.Button = null;
    @property(cc.Button) btnSelectLines: cc.Button = null;
    @property(cc.Button) btnBetDecrease: cc.Button = null;
    @property(cc.Button) btnBetIncrease: cc.Button = null;

    @property(cc.Toggle) toggleSound: cc.Toggle = null;
    @property(cc.Toggle) toggleMusic: cc.Toggle = null;

    @property(cc.Node) effectBigWin: cc.Node = null;
    @property(cc.Node) effectSuperWin: cc.Node = null;
    @property(cc.Node) effectMegaWin: cc.Node = null;
    @property(cc.Node) effectBonus: cc.Node = null;
    @property(cc.Node) effectFreeSpin: cc.Node = null;
    @property(cc.Node) effectJackpot: cc.Node = null;

    @property(ForestPopupSelectLine)
    popupSelectLine: ForestPopupSelectLine = null;

    @property(ForestPopupBonus)
    popupBonus: ForestPopupBonus = null;

    @property(cc.Node)
    toast: cc.Node = null;

    @property(cc.Animation) floatText: cc.Animation = null;

    @property(SlotMachine_v2)
    slotMachine: SlotMachine_v2 = null;

    @property(cc.Sprite)
    iconMultiplier: cc.Sprite = null;
    @property(cc.SpriteFrame)
    iconX2: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    iconX3: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    iconX4: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    iconX5: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    iconX6: cc.SpriteFrame = null;

    private _roomID: number = 1;

    private _currentAutoSpin: number = 0;
    public get currentAutoSpin(): number {
        return this._currentAutoSpin;
    }
    public set currentAutoSpin(value: number) {
        this._currentAutoSpin = value;
        this.lblAutoSpin.string = value > 0 ? Utils.formatNumber(value) : '';
        this.btnStopAutoSpin.node.active = value > 0;
        // disable interactable of spin button
        this.btnSpin.interactable = value === 0;
    }
    private _isSpinning: boolean = false;
    private _effectCreator: EffectCreator = null;
    private forestPlayerResponse: SlotPlayerResponse = null;

    private holdTimeout: any = null;
    private isHolding: boolean = false;

    private set roomID(roomID: number) {
        this._roomID = roomID;
        ForestSignalRClient.getInstance().send(
            'PlayNow', [{ "CurrencyID": Configs.Login.CurrencyID, "RoomID": roomID }],
            (data) => {
            }
        )
    }

    private get roomID() {
        return this._roomID;
    }

    private updateGoldCoin(coin: number) {
        if (coin < 0) return; // gold < 0, don't update balance
        Configs.Login.GoldCoin = coin;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    public get isSpinning(): boolean {
        return this._isSpinning;
    }
    public set isSpinning(value: boolean) {
        this._isSpinning = value;
        this.updateGoldCoin(this.forestPlayerResponse.Account.GoldBalance);
        this.btnSelectLines.interactable = !value;
        this.btnBetIncrease.interactable = !value;
        this.btnBetDecrease.interactable = !value;
        this.toggleTrial.interactable = !value;
        this.btnSpin.interactable = !value;
    }

    private totalFreeSpinPrize = 0;

    private _totalFreeSpin: number = 0;
    public get totalFreeSpin(): number {
        return this._totalFreeSpin;
    }
    public set totalFreeSpin(value: number) {
        this._totalFreeSpin = value;
        this.lblFreeSpin.string = value.toString();
        this.lblFreeSpin.node.parent.active = value > 0;
    }

    private mIsTrial = false;

    private _trialBalance: number = 0;
    public get trialBalance(): number {
        return this._trialBalance;
    }
    public set trialBalance(value: number) {
        this._trialBalance = value;
        this.lblTrialBalance.string = Utils.formatNumber(value);
    }

    private _freeTicket: number = 0;
    public get freeTicket(): number {
        return this._freeTicket;
    }
    public set freeTicket(value: number) {
        this._freeTicket = value;
        this.lblFreeTicket.string = value.toString();
        this.lblFreeTicket.node.parent.active = value > 0;
    }

    protected onLoad() {
        this.initListeners();
        this.initHubs();
        this.initEffects();
        this.slotMachine.setTurbo(false);
        this.slotMachine.initializeReels();
    }

    protected start() {
        this.currentAutoSpin = 0;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        this.roomID = 1;
    }

    protected onEnable(): void {
        this.toggleMusic.isChecked = !ForestAudioManager.Instance.isMuteMusic;
        this.toggleSound.isChecked = !ForestAudioManager.Instance.isMuteEffect;
    }

    private initListeners() {
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);

        [10, 50, 200, 1000, 5000].forEach((value, index) => {
            this.autoFrame.children[index].on('click', () => {
                this.autoFrame.active = false;
                ForestAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                if (this.mIsTrial) {
                    this.showToast("", App.instance.getTextLang("me35")).then();
                    return;
                }
                this.currentAutoSpin = value;
                this.isSpinning = true;
                this._spin();
            })
        })
        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            this.lblBalance.string = Utils.formatNumber(Configs.Login.GoldCoin);
        }, this);
        this.popupSelectLine.setOnSelectedChanged(selectedLines => {
            this.lblNumberOfLines.string = selectedLines!.length.toString();
            this.lblTotalBet.string = Utils.formatNumber(selectedLines.length * this.forestPlayerResponse.BetValue);
        })
    }

    private onTouchStart() {
        if (this.isSpinning) {
            return;
        }
        this.isHolding = false;

        this.holdTimeout = setTimeout(() => {
            this.isHolding = true;
            this.autoFrame.active = true;
        }, 800);
    }

    private onTouchEnd() {
        if (this.isSpinning) {
            return;
        }
        clearTimeout(this.holdTimeout);

        if (!this.isHolding) {
            this.onSpin(); // gọi nếu nhấn thả trước 2s
        }
    }

    private onTouchCancel() {
        if (this.isSpinning) {
            return;
        }
        clearTimeout(this.holdTimeout);
    }

    private onClickTrial() {
        this.mIsTrial = !this.mIsTrial;
        this.toggleTrial.isChecked = this.mIsTrial;
        if (this.mIsTrial) {
            this.lblBet.string = '100';
            this.lblNumberOfLines.string = '20';
            this.lblTotalBet.string = '2.000';
            this.trialBalance = 10_000_000;
        }
        else {
            const lineCount = this.popupSelectLine.getSelectedLines().length;
            const data = this.forestPlayerResponse;
            this.lblBet.string = Utils.formatNumber(data.BetValue);
            this.lblNumberOfLines.string = lineCount.toString();
            this.lblTotalBet.string = Utils.formatNumber(lineCount * data.BetValue);
        }
        this.lblTrialBalance.node.parent.active = this.mIsTrial;
    }

    private initHubs() {
        ForestSignalRClient.getInstance().receive('UpdateJackPot', (data: number) => {
            Tween.numberTo(this.lblJackpot, data, 0.5);
        })

        ForestSignalRClient.getInstance().receive('MessageError', (data: number) => {
            this.showToast("", App.instance.getTextLang(`me${data}`)).then();
        })

        ForestSignalRClient.getInstance().receive('JoinGame', (data) => {
            cc.log("JoinGame", data);

            this.forestPlayerResponse = data;
            this.lblSession.string = ``;
            const lineCount = this.popupSelectLine.getSelectedLines().length;
            this.lblBet.string = Utils.formatNumber(data.BetValue);
            this.lblTotalBet.string = Utils.formatNumber(lineCount * data.BetValue);
            this.updateMultiplierIcon();
            this.checkFreeTicket();

            if (data.SlotInfo.FreeSpins > 0) {
                this.totalFreeSpin = data.SlotInfo.FreeSpins;
                let z = this.effectFreeSpin.getChildByName("FreeSpinCount");
                z.getComponent(cc.Label).string = `${data.SlotInfo.FreeSpins}`;
                this._effectCreator[EnumEffect.FREESPIN].effect().then(() => {
                    this.isSpinning = true;
                    this._spin();
                })
            }
        })

        ForestSignalRClient.getInstance().receive('ResultSpin', async (data: SlotPlayerResponse) => {
            cc.log("ResultSpin", data);

            this.forestPlayerResponse = data;
            this.lblSession.string = `#${data.SpinData.SpinID}`;
            this.resetSession();
            this.updateGoldCoin(parseInt(data.Account.GoldBalance));
            ForestAudioManager.Instance.playEffect(AUDIO_CLIP.REEL_SPIN);
            await this.slotMachine.startSpin(data.SpinData.SlotsData);
            this.onSpinCompleted();
        })

        ForestSignalRClient.getInstance().receive('resultBonusGame', (data: SlotPlayerResponse) => {
            this.updateGoldCoin(parseInt(data.Account.GoldBalance));
        })
    }

    private onSpin() {
        this.isSpinning = true;
        if (this.mIsTrial) {
            this.lblSession.string = ``;
            this.resetSession();
            let rIdx = Utils.randomRangeInt(0, ForestTrial.Result.length);
            this.forestPlayerResponse.SpinData = ForestTrial.Result[rIdx];
            this.trialBalance -= 2000;
            ForestAudioManager.Instance.playEffect(AUDIO_CLIP.REEL_SPIN);
            this.slotMachine.startSpin(this.forestPlayerResponse.SpinData.SlotsData).then(() => {
                this.onSpinCompleted();
                this.trialBalance += this.getPayLinePrize();
            })
            return;
        }
        if(this.freeTicket > 0 && this.totalFreeSpin <= 0) {
            this._spin(true);
        }
        else {
            this._spin();
        }
    }


    private _spin(isTicket: boolean = false) {
        ForestSignalRClient.getInstance().send(
            isTicket ? 'SpinForTicket' : 'Spin', [{
                "RoomID": this.roomID,
                "CurrencyID": Configs.Login.CurrencyID,
                "Lines": this.popupSelectLine.getSelectedLines().join(",")
            }],
            (data) => {
                if (data.c < 0) {
                    this.isSpinning = false;
                    this.currentAutoSpin = 0;
                    this.showToast("", App.instance.getTextLang("me" + data.c)).then();
                }
            }
        )
    }

    private stopAutoSpin() {
        if (this.btnStopAutoSpin.node.active) {
            this.currentAutoSpin = 0;
        }
    }


    onClickTurbo(target: cc.Toggle, eventData: string) {
        this.slotMachine.setTurbo(target.isChecked);
    }

    private parsePositionData(positionData: string): { payLines: number[][], commonPayLines: number[] } {
        const payLines = positionData.split(";").map(row => row.split(",").map(Number));
        const commonPayLines = Array.from(new Set(payLines.flat()));
        return { payLines, commonPayLines };
    }


    private parsePrizeData(prizeData: string): string[] {
        return prizeData.split(";");
    }


    private onSpinCompleted() {
        ForestAudioManager.Instance.stopEffect(AUDIO_CLIP.REEL_SPIN);
        const payLinePrize: number = this.getPayLinePrize();
        let isFreeSpinCompleted = false;
        this.lblWinCash.string = '0';

        if (this.totalFreeSpin > 0) {
            this.totalFreeSpinPrize += payLinePrize;
            this.totalFreeSpin--;
            if (this.totalFreeSpin === 0) {
                isFreeSpinCompleted = true;
            }
        }
        else {
            if (this.freeTicket > 0) {
                this.freeTicket--;
            }
            if (this.currentAutoSpin > 0) {
                this.currentAutoSpin--;
            }
        }


        const { payLines, commonPayLines } = this.parsePositionData(this.forestPlayerResponse.SpinData.PositionData);
        const prizeData = this.parsePrizeData(this.forestPlayerResponse.SpinData.PrizesData);
        let delayTime = 0.5;

        if (payLinePrize > 0) {
            delayTime = 1.5;
            this.showTextPayLine(payLinePrize);
            this.slotMachine.highlightItems(commonPayLines, prizeData).then();
            Tween.numberTo(this.lblWinCash, payLinePrize, 0.5);
        }

        this.node.runAction(
            cc.sequence(
                cc.delayTime(delayTime),
                cc.callFunc(async () => {
                    this.showEffects(async () => {
                        this.isSpinning = false;
                        if (this.totalFreeSpin > 0) {
                            this.isSpinning = true;
                            this._spin();
                            return;
                        }
                        if (isFreeSpinCompleted) {
                            ForestAudioManager.Instance.playMusic(AUDIO_CLIP.MUSIC);
                            await this.showRewardFreeSpinTotalWin(this.totalFreeSpinPrize, () => {
                                this.totalFreeSpinPrize = 0;
                            });
                        }
                        if (this.currentAutoSpin > 0) {
                            this.isSpinning = true;
                            this._spin();
                            return;
                        }
                        if (payLinePrize) {
                            for (let i = 0; i < payLines.length; i++) {
                                await this.slotMachine.highlightItems(payLines[i], [prizeData[i]]);
                            }
                        }
                    })
                })
            )
        )
    }

    private initEffects() {
        this.effectBigWin.active = false;
        // this.effectSuperWin.active = false;
        // this.effectMegaWin.active = false;
        this.effectBonus.active = false;
        this.effectFreeSpin.active = false;
        this.effectJackpot.active = false;

        this._effectCreator = {
            [EnumEffect.BIGWIN]: {
                condition: () => {
                    return this.getPayLinePrize() >= 85 * this.forestPlayerResponse.BetValue;
                },
                effect: async () => {
                    let prize = this.effectBigWin.getChildByName("BigWinPrize").getComponent(cc.Label);
                    prize.string = '';
                    Tween.numberTo(prize, this.getPayLinePrize(), 8);
                    ForestAudioManager.Instance.playEffect(AUDIO_CLIP.FX_BIGWIN);
                    this.scheduleOnce(() => {
                        ForestAudioManager.Instance.playEffect(AUDIO_CLIP.COUNTER, true);
                    }, 4);
                    this.scheduleOnce(() => {
                        ForestAudioManager.Instance.stopEffect(AUDIO_CLIP.COUNTER);
                    }, 8);
                    await this.playEffectAnimation(this.effectBigWin, 9);
                }
            },
            [EnumEffect.SUPERWIN]: {
                condition: () => {
                    return false;
                },
                effect: async () => {
                    return;
                }
            },
            [EnumEffect.MEGAWIN]: {
                condition: () => {
                    return false;
                },
                effect: async () => {
                    return;
                }
            },
            [EnumEffect.BONUS]: {
                condition: () => {
                    return this.forestPlayerResponse.SpinData.BonusGameData !== "";
                },
                effect: async () => {
                    this.lblHeSoNhan.string = `x${this.forestPlayerResponse.SpinData.StartBonus}`;
                    ForestAudioManager.Instance.playEffect(AUDIO_CLIP.FX_BONUS);
                    await this.playEffectAnimation(this.effectBonus, 2);
                    ForestAudioManager.Instance.playMusic(AUDIO_CLIP.MUSIC_BONUS);
                    await this.actPopupBonusGame();
                }
            },
            [EnumEffect.FREESPIN]: {
                condition: () => {
                    return this.forestPlayerResponse.SpinData.IsFreeSpin;
                },
                effect: async () => {
                    ForestAudioManager.Instance.playEffect(AUDIO_CLIP.FX_FREESPIN);
                    await this.playEffectAnimation(this.effectFreeSpin, 3);
                    ForestAudioManager.Instance.playMusic(AUDIO_CLIP.MUSIC_FREESPIN);
                }
            },
            [EnumEffect.JACKPOT]: {
                condition: () => {
                    return this.forestPlayerResponse.SpinData.IsJackpot;
                },
                effect: async () => {
                    let prize = this.effectJackpot.getChildByName("JackpotPrize").getComponent(cc.Label);
                    let num = this.effectJackpot.getChildByName("JackpotNum").getComponent(cc.Label);
                    num.string = this.forestPlayerResponse.SpinData.JackpotNum.toString();
                    prize.string = '';
                    Tween.numberTo(prize, this.getPayLinePrize(), 10.6);
                    ForestAudioManager.Instance.playEffect(AUDIO_CLIP.JACKPOT);
                    this.scheduleOnce(() => {
                        ForestAudioManager.Instance.playEffect(AUDIO_CLIP.COUNTER, true);
                    }, 3);
                    this.scheduleOnce(() => {
                        ForestAudioManager.Instance.stopEffect(AUDIO_CLIP.COUNTER);
                    }, 10.6);
                    await this.playEffectAnimation(this.effectJackpot, 12.6);
                }
            }
        }
    }

    private showEffects(finishCallback: Function) {
        let awaitable: Function[] = [];

        // Check and add jackpot or win effects (mutually exclusive)
        if (this._effectCreator[EnumEffect.JACKPOT].condition()) {
            this.currentAutoSpin = 0;
            awaitable.push(this._effectCreator[EnumEffect.JACKPOT].effect);
        } else if (this._effectCreator[EnumEffect.BIGWIN].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BIGWIN].effect);
        }

        if (this._effectCreator[EnumEffect.BONUS].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BONUS].effect);
        }

        if (this._effectCreator[EnumEffect.FREESPIN].condition()) {
            const newFreeSpin = this.forestPlayerResponse.SpinData.TotalFreeSpin;
            if (this.totalFreeSpin <= 0) {
                let z = this.effectFreeSpin.getChildByName("FreeSpinCount");
                z.getComponent(cc.Label).string = `${newFreeSpin}`;
                awaitable.push(this._effectCreator[EnumEffect.FREESPIN].effect);
            }
            this.totalFreeSpin += newFreeSpin;
        }

        // Execute all effects in sequence
        (async () => {
            for (const aw of awaitable) {
                await aw();
            }
        })().then(() => {
            finishCallback();
        });
    }

    private async playEffectAnimation(effectTarget: cc.Node, duration: number): Promise<void> {
        return new Promise((resolve) => {
            effectTarget.active = true;

            let sequence: cc.Tween<cc.Node>;

            sequence = cc.tween(effectTarget)
                .set({ opacity: 0 })
                .to(0.2, { opacity: 255 });

            let textNode = effectTarget.getChildByName("text");
            if (textNode) {
                let wave = cc.tween(textNode)
                    .repeat(3, cc.tween()
                        .by(0.5, { y: 5 }, { easing: "sineInOut" })
                        .by(0.5, { y: -5 }, { easing: "sineInOut" })
                    );
                sequence.then(wave);
            }

            sequence.start();

            this.scheduleOnce(() => {
                effectTarget.active = false;
                resolve();
            }, duration);
        });
    }

    showFloatText(text: string) {
        this.floatText.node.active = true;
        this.floatText.node.getComponent(cc.Label).string = App.instance.getTextLang(text);
        this.floatText.play();

        this.floatText.on('finished', () => {
            this.floatText.node.active = false;
        })
    }

    private async showToast(title: string, msg: string): Promise<void> {
        let lblTitle = this.toast.getChildByName("Title").getComponent(cc.Label);
        let lblMessage = this.toast.getChildByName("Message").getComponent(cc.Label);

        lblTitle.string = title;
        lblMessage.string = msg;

        this.toast.stopAllActions();
        this.toast.active = true;
        this.toast.scale = 0.8;
        return new Promise(resolve => {
            this.toast.runAction(
                cc.sequence(
                    cc.scaleTo(0.2, 1),
                    cc.delayTime(2),
                    cc.callFunc(() => {
                        this.toast.active = false;
                        resolve();
                    })
                )
            );
        });
    }

    private async getRoomMultiplier(roomID: number): Promise<number> {
        return new Promise((resolve) => {
            Http.get(
                Configs.App.DOMAIN_CONFIG['GetListJackpot'],
                { CurrencyID: Configs.Login.CurrencyID },
                (status, res) => {
                    if (status !== 200 || !res?.d) {
                        resolve(0);
                        return;
                    }

                    const item = res.d.find(item =>
                        item.gameID === Configs.GameId88.Forest &&
                        item.roomID === roomID &&
                        item.nextJackpot === 0
                    );

                    resolve(item ? item.multiplier : 0);
                }
            );
        });
    }

    private updateMultiplierIcon() {
        this.getRoomMultiplier(this.roomID).then((multiplier) => {
            this.iconMultiplier.node.active = multiplier > 0;
            this.iconMultiplier.spriteFrame = multiplier ? this[`iconX${multiplier}`] : null;
        });
    }

    private checkFreeTicket(){
        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountTicket'], { CurrencyID: Configs.Login.CurrencyID, GameID: Configs.GameId88.Forest }, (status, res) => {
            if (status === 200) {
                const data = res.d.filter(item => item.roomID === this.roomID);
                const count = data.reduce((sum, item) => sum + item.balance, 0);
                this.freeTicket = count;
            }
        })
    }


    private actBetDecrease() {
    if (this.mIsTrial) {
        this.showToast("", App.instance.getTextLang("me35")).then();
        return;
    }
    this.roomID = this.roomID === 1 ? 3 : this.roomID - 1;
}

private actBetIncrease() {
    if (this.mIsTrial) {
        this.showToast("", App.instance.getTextLang("me35")).then();
        return;
    }
    this.roomID = this.roomID === 3 ? 1 : this.roomID + 1;
}

    private async actPopupBonusGame(): Promise<void> {
        const data = this.forestPlayerResponse.SpinData.BonusGameData;
        const startBonus = this.forestPlayerResponse.SpinData.StartBonus;
        this.popupBonus.node.active = true;
        this.popupBonus.showPopup(data, startBonus);

        await new Promise<void>(resolve => {
            this.popupBonus.onFinishProcess = (value: number) => {
                ForestAudioManager.Instance.playMusic(AUDIO_CLIP.MUSIC);
                ForestAudioManager.Instance.playEffect(AUDIO_CLIP.REWARD_BONUS);
                this.showToast(
                    App.instance.getTextLang("sl21"),
                    App.instance.getTextLang("sl52") + "\n"
                    + Utils.formatNumber(value) + "\n"
                    + App.instance.getTextLang("sl88")
                ).then(() => {
                    ForestSignalRClient.getInstance().send(
                        'PlayBonusGame', [{
                            "RoomID": this.roomID,
                            "CurrencyID": Configs.Login.CurrencyID,
                        }],
                        (data) => { /* Handle response if needed */
                            if (data.c < 0) {
                                this.showToast("", App.instance.getTextLang(`me${data.c}`)).then();
                            }
                        }
                    );
                    resolve();
                });
            };
        });
    }

    private onClickSound(){
        ForestAudioManager.Instance.muteEffect(!this.toggleSound.isChecked);
    }

    private onClickMusic(){
        ForestAudioManager.Instance.muteMusic(!this.toggleMusic.isChecked);
    }

    private actPopupLSH() {
        this.popupLSH.show();
    }

    private actPopupLSC() {
        this.popupLSC.show();
    }

    private actPopupGuide() {
        this.popupGuide.show();
    }

    private actPopupSelectLine() {
        if (this.mIsTrial) {
            this.showToast("", App.instance.getTextLang("me35")).then();
            return;
        }
        if (this.popupSelectLine) {
            this.popupSelectLine.show();
        }
    }

    private backToLobby() {
        ForestSignalRClient.getInstance().dontReceive();
        App.instance.bigSlotGame[Configs.GameId88.Forest] = null;
        App.instance.gotoLobby();
    }

    private actHidden() {
        if(this.currentAutoSpin > 0){ 
            App.instance.showConfirmDialog(
                App.instance.getTextLang("sl74"),
                () => {
                    this.node.parent.setPosition(cc.v3(10000, 0, 0));
                },
                true
            );
        }
        else {
            App.instance.ShowAlertDialog(App.instance.getTextLang("sl90"));
        }
    }

    private async showRewardFreeSpinTotalWin(win: number, cb: () => void) {
        await this.showToast(
            App.instance.getTextLang("sl21"),
            App.instance.getTextLang("sl87").replace("{0}", Utils.formatNumber(win))
        );
        cb && cb();
    }

    private getPayLinePrize(): number {
        return this.forestPlayerResponse.SpinData.PayLinePrizeValue;
    }

    private showTextPayLine(prize: number) {
        if (this._effectCreator[EnumEffect.BIGWIN].condition() || this._effectCreator[EnumEffect.JACKPOT].condition()) return;
        ForestAudioManager.Instance.playEffect(AUDIO_CLIP.WIN);
        this.lblPayLine.string = '';
        Tween.numberTo(this.lblPayLine, prize, 0.5);
        this.lblPayLine.node.parent.active = true;
        this.scheduleOnce(() => {
            this.lblPayLine.node.parent.active = false;
        }, 1.5);
    }

    private resetSession() {
        this.slotMachine.resetAllItems();
        this.node.stopAllActions();
        ForestAudioManager.Instance.stopAllEffect();
    }

}