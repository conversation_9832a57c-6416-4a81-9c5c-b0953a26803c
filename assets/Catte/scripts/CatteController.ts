// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import BundleControl from "../../Loading/src/BundleControl";
import ChatInGame from "../../Lobby/ChatInGame/ChatInGame";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import CardGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/CardGameSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import CatteHUDController from "./Catte.HUD";
import { loadBundleCatte } from "./Catte.Utils";
import CattePlayerController from "./CattePlayerController";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Catte/CateController")
export default class CatteController extends cc.Component {

    public static Instance: CatteController;

    @property(CatteHUDController)
    HUD: CatteHUDController;

    @property(CattePlayerController)
    PlayerList: CattePlayerController[] = [];

    @property(cc.Sprite)
    dealer: cc.Sprite;

    @property(cc.Node)
    WinPopUp: cc.Node;

    spriteFramesDe: cc.SpriteFrame[] = [];
    spriteFramesNo: cc.SpriteFrame[] = [];

    Player: CattePlayerController;
    chatIngame: ChatInGame = null;

    private cards: cc.SpriteFrame[] = [];

    private currentPlayersList: any[] = [];
    private currentPlayerData: any;

    private isBetting: boolean = false;
    private isQuitting: boolean = false;

    protected onLoad(): void {
        CatteController.Instance = this;
    }

    protected start(): void {
        loadBundleCatte(() => {
            var bundle = cc.assetManager.getBundle("Catte");

            bundle.loadDir("res/normal", cc.Texture2D, (err, textures) => {
                if (err) {
                    console.error("Lỗi khi tải ảnh:", err);
                    return;
                }

                // Chuyển texture thành SpriteFrame
                this.spriteFramesNo = textures.map((texture: cc.Texture2D) => {
                    return new cc.SpriteFrame(texture);
                });

                // Gọi hàm phát animation
                this.playAnimationnormal();
            });

            bundle.loadDir("res/chiabai", cc.Texture2D, (err, textures) => {
                if (err) {
                    console.error("Lỗi khi tải ảnh:", err);
                    return;
                }

                // Chuyển texture thành SpriteFrame
                this.spriteFramesDe = textures.map((texture: cc.Texture2D) => {
                    return new cc.SpriteFrame(texture);
                });

            });

            bundle.loadDir("res/Card/", cc.SpriteFrame, (err, spriteFrames: cc.SpriteFrame[]) => {
                if (err) {
                    console.error("Failed to load atlas:", err);
                    return;
                }
                this.cards = spriteFrames
            });
        })

        const data = App.instance.DataPass[0];


        if (this.HUD)
            this.HUD.setInfoLabel(data);

        this.scheduleOnce(() => {
            this.playerJoin(App.instance.DataPass[0]);
        }, 1);

        CardGameSignalRClient.getInstance().receive('joinRoom', (data: any) => {
            if (data.r) {
                App.instance.showToast(App.instance.getTextLang('me6'));
                this.playerJoin(data.r);
            }
        });

        CardGameSignalRClient.getInstance().receive('roomData', (data: any) => {
            if (data.r) {
                this.onHandleRoom(data.r);
            }
        });

        // CardGameSignalRClient.getInstance().receive('showCard', (data: any) => {
        //     if (data.r) {
        //         cc.log('showCard', data.r);
        //     }

        // });

        CardGameSignalRClient.getInstance().receive('registerLeavingRoom', (data: any) => {
            if (!data.r) return;
            cc.log(data.r);
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.AccountId === data.r.id) {
                    if (e.Data.AccountId != this.currentPlayerData.AccountId) {
                        App.instance.showToast(App.instance.getTextLang('me11004').replace("{0}", e.Data.Nickname));
                    } else {
                        if (data && data.code == 11007) {
                            var stringaler = App.instance.getTextLang("me11007");
                            App.instance.ShowAlertDialog(Utils.formatString(stringaler, Utils.formatNumber(data.prms[0]), data.prms[0] == 1 ? App.instance.getTextLang("hi25") : App.instance.getTextLang("TLN_COIN")));
                        }
                        this.node.removeFromParent();
                        App.instance.gotoLobby();
                    }
                }
            })
        });

        CardGameSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.AccountId === accountId) {
                    e.showChatMsg(content);
                }
            })
        });
    }


    actChat() {
        App.instance.inactivityTimer = 0;
        if (this.chatIngame == null) {
            let cb = (prefab) => {
                this.chatIngame = cc.instantiate(prefab).getComponent("ChatInGame");
                this.HUD.node.addChild(this.chatIngame.node);
                this.chatIngame.show(Configs.GameId88.Catte);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/ChatInGame", cb);
        } else {
            this.chatIngame.show(Configs.GameId88.Catte);
        }

    }

    backToLobby() {
        if (this.isQuitting) return;
        this.isQuitting = true;
        this.node.removeFromParent();
        App.instance.gotoLobby();
        this.sendExit();
    }

    sendExit() {
        CardGameSignalRClient.getInstance().send('ExitRoom', [], null);
    }

    playAnimationnormal() {
        var thiz = this;
        let index = 0;
        let forward = true; // Biến kiểm soát hướng
        const frameRate = 1 / 30; // 100ms mỗi frame
        if (thiz.spriteFramesNo.length <= 0) return;
        // Hủy mọi schedule trước khi đặt mới
        this.dealer.unscheduleAllCallbacks();

        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesNo[index];

            // Nếu đến cuối mảng, đảo chiều
            if (index === thiz.spriteFramesNo.length - 1) {
                forward = false;
            }
            // Nếu đến đầu mảng, đổi hướng đi tiếp
            else if (index === 0) {
                forward = true;
            }

            // Cập nhật index theo hướng
            index += forward ? 1 : -1;
        }, frameRate, cc.macro.REPEAT_FOREVER);
    }

    playAnimationDealer() {
        var thiz = this;
        let frameIndex = 0;
        if (thiz.spriteFramesDe.length <= 0) return;
        const finishDealer = () => {
            cc.log("Animation completed!");
            thiz.playAnimationnormal();
        };
        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesDe[frameIndex];
            frameIndex = (frameIndex + 1) % thiz.spriteFramesDe.length;
            frameIndex++;
            if (frameIndex >= this.spriteFramesDe.length - 1) {
                // Gọi callback khi hoàn thành
                this.dealer.unscheduleAllCallbacks();
                finishDealer();
            }

        }, 1 / 30, this.spriteFramesDe.length - 1);

    }

    getCardByName(cardNum: string): cc.SpriteFrame {
        for (let spr of this.cards) {
            if (spr.name === cardNum) {
                return spr;
            }
        }
        return null;
    }

    playerJoin(data) {
        const newArr = [
            ...data.Players.filter(item => item.Nickname.split('[X]')[1] === Configs.Login.Nickname),
            ...data.Players.filter(item => item.Nickname.split('[X]')[1] !== Configs.Login.Nickname)
        ];
        this.currentPlayersList = newArr;
        this.updatePlayerList(this.currentPlayersList);
    }

    updatePlayerList(players: any[]) {
        if (players && players.length) {
            this.PlayerList.forEach((e, i) => {
                if (players[i]) {
                    if (players[i].Nickname.split('[X]')[1] === Configs.Login.Nickname) {
                        this.Player = e;
                        this.currentPlayerData = players[i];
                        e.setInfo(players[i], true);
                    } else {
                        e.setInfo(players[i], false);
                    }

                } else {
                    e.setEmpty();
                }
            })
        }
    }

    onHandleRoom(data) {
        const newArr = [
            ...data.Players.filter(item => item.Nickname.split('[X]')[1] === Configs.Login.Nickname),
            ...data.Players.filter(item => item.Nickname.split('[X]')[1] !== Configs.Login.Nickname)
        ];
        this.currentPlayersList = newArr;
        this.updatePlayerList(this.currentPlayersList);
        const session = data.Session;
        if (session) {
            if (session.Phrase === 1) {
                App.instance.showToast(App.instance.getTextLang("ca45"));
            } else if (session.Phrase === 4) {
                this.PlayerList.forEach((e) => {
                    if (e.Data) {
                        if (e.Data.AccountId === session.Winner) {
                            e.showWin(true, session.Timeout);
                        } else {
                            e.showWin(false, session.Timeout);
                        }
                        if (session.Prizes && session.Prizes.length) {
                            session.Prizes.forEach((prize) => {

                                if (prize.id === e.Data.AccountId) {
                                    e.showPrize(prize.prize);
                                }
                            })
                        }
                    }
                })
                if (session.Winner === this.currentPlayerData.AccountId) {
                    this.showWinPopUp();
                }
            } else if (session.Phrase === 3) {
                this.PlayerList.forEach((e) => {
                    if (e.Data) {
                        e.stopTimeMiming();
                        if (e.Data.AccountId === session.Role) {
                            e.showTimming(session.Timeout);
                        }
                    }
                })
                if (this.HUD) {
                    if (session.Role === this.currentPlayerData.AccountId) {
                        this.HUD.activeGroupButton(true, session.Actions);
                    } else {
                        this.HUD.activeGroupButton(false, []);
                    }
                }
            }
        }
    }


    getCardInfo(id: number) {

        if (id < 0 || id > 51) {
            return this.getCardByName('Flip');
        }

        const cardNumber = id % 13;
        const cardSuite = id - cardNumber;

        const cardNumberTextMap = ["2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K", "A"];
        const cardSuiteTextMap: { [key: number]: string } = {
            0: "club",
            13: "heart",
            26: "spade",
            39: "diamond"
        };

        const text = `${cardSuiteTextMap[cardSuite]}-${cardNumberTextMap[cardNumber]}`;
        return this.getCardByName(text);
    }

    showWinPopUp() {
        if (this.WinPopUp) {
            this.WinPopUp.scaleX = 0;
            this.WinPopUp.scaleY = 0;
            cc.tween(this.WinPopUp).to(0.25, { scaleX: 1, scaleY: 1 }, { easing: "elasticOut" })
                .delay(1).call(() => {
                    cc.tween(this.WinPopUp).to(0.25, { scaleX: 0, scaleY: 0 }, { easing: "elasticOut" }).start();
                }).start();
        }
    }

    faceDownCard() {
        this.sendShowCard(true);
    }

    playCard() {
        this.sendShowCard(false);
    }

    sendShowCard(faceDown: boolean) {
        if (this.Player.CurrentCardSelecting) {
            App.instance.showLoading(true);
            CardGameSignalRClient.getInstance().send('ShowCard', [
                this.Player.CurrentCardSelecting,
                faceDown
            ], (data) => {
                App.instance.showLoading(false);
                if (data.r) {
                    this.Player.stopTimeMiming();
                } else {
                    App.instance.showToast(App.instance.getTextLang("ca-27000"));
                }
            });
        } else {
            App.instance.showToast(App.instance.getTextLang("ca-27000"));
        }
    }
}
