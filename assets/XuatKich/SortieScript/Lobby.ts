import App from "../../Lobby/LobbyScript/Script/common/App";
import {SortiePhotonClient} from "../../Lobby/LobbyScript/Script/networks/SortiePhotonClient";
import {PhotonClient} from "../../Lobby/LobbyScript/Script/networks/PhotonClient";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Play from "./Play";
import Configs from "../../Lobby/MoveScript/Configs";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import PanelMenu from "./PanelMenu";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("Sortie/Lobby")
export default class Lobby extends cc.Component {

    public static instance: Lobby = null;

    @property(cc.Node)
    playNode: cc.Node = null;

    @property(cc.Label)
    lblGoldBalance: cc.Label = null;
    @property(cc.Label)
    lblGemBalance: cc.Label = null;
    @property(cc.Label)
    currentJackpot: cc.Label = null;

    @property(cc.AudioClip)
    bgMusic1: cc.AudioClip = null;

    @property(cc.AudioClip)
    bgMusic2: cc.AudioClip = null;

    private play: Play = null;
    private photonClient: SortiePhotonClient = null;
    private currentMusicIndex: number = 0;

    listGunConfig = [];
    clientParameterConfig = null;

    @property(cc.Node)
    popupContainer: cc.Node = null;
    @property(cc.Prefab)
    exchangeCoinPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    popupGuidePrefab: cc.Prefab = null;
    @property(cc.Prefab)
    popupHistoryPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    popupJackpotPrefab: cc.Prefab = null;

    @property(cc.Node)
    popupSetting: cc.Node = null;
    @property(cc.Node)
    iconClosePopupSetting: cc.Node = null;

    @property(cc.Node)
    iconOffSoundMusic: cc.Node = null;
    @property(cc.Node)
    iconOnSoundMusic: cc.Node = null;

    @property(cc.Node)
    iconOffSoundEffect: cc.Node = null;
    @property(cc.Node)
    iconOnSoundEffect: cc.Node = null;

    private BILLION_GOLD: number = **********;
    public currentUserAccumulate = 0;

    protected start() {
        this.photonClient = SortiePhotonClient.getInstance();
        this.photonClient.connect();
        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Account, (res: any) => {
            if (res.errCode < 0) {
                App.instance.showErrLoading(PhotonClient.getErrMsg(res.errCode));
                return;
            }

            cc.log("Logged in successfully");
            App.instance.showLoading(false);
            this.photonClient.isLoggedIn = true;

            this.listGunConfig = JSON.parse(res.vals[SortiePhotonClient.EParameterCodes.GunConfigsResponse]);
            this.clientParameterConfig = JSON.parse(res.vals[SortiePhotonClient.EParameterCodes.ClientParameterConfig]);

            var IAccountModel = JSON.parse(res.vals[SortiePhotonClient.EParameterCodes.LoginResponse]);
            this.currentUserAccumulate = IAccountModel.ua;
            const goldBalance = IAccountModel.go;
            const gemBalance = IAccountModel.ge;

            // const scaleGold = (goldBalance >= this.BILLION_GOLD) ? 0.9 : 1.0;
            // this.lblGoldBalance.node.setScale(scaleGold);
            // const scaleGem = (gemBalance >= this.BILLION_GOLD) ? 0.9 : 1.0;
            // this.lblGemBalance.node.setScale(scaleGem);

            this.lblGoldBalance.string = Utils.formatNumber(goldBalance);
            this.lblGemBalance.string = Utils.formatNumber(gemBalance);

            var data = [];
            data.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.GetCurrentJackpot);
            data.push(SortiePhotonClient.EParameterCodes.CurrencyId, Configs.Login.CurrencyID);
            this.photonClient.sendOperation(PhotonClient.EOperationCodes.Game, data, true);

            this.playMusic();
        });

        this.photonClient.addResponseListener(PhotonClient.EOperationCodes.Game, (res: any) => {
            if (res.errCode < 0) {
                return;
            }

            const keys = Object.keys(res.vals);
            keys.forEach(key => {
                var code = parseInt(key);
                var data = res.vals[key];

                if (code == SortiePhotonClient.EParameterCodes.CurrentJackpot) {
                    Tween.numberTo(this.currentJackpot, data.value, 0.3);
                }
            });
        });
        this.updateSoundIcons();
    }

    updateBalance(gold: number, gem: number) {
        this.lblGemBalance.string = Utils.formatNumber(gem);
        this.lblGoldBalance.string = Utils.formatNumber(gold);
    }

    playMusic() {
        let visitCount = parseInt(cc.sys.localStorage.getItem("XK_visit_count") || "0", 10);
        visitCount++;

        cc.sys.localStorage.setItem("XK_visit_count", visitCount.toString());

        this.currentMusicIndex = visitCount % 2;

        let musicToPlay = this.currentMusicIndex === 0 ? this.bgMusic1 : this.bgMusic2;
        let isPlaying = JSON.parse(cc.sys.localStorage.getItem("XK_is_playing"));

        if (musicToPlay && isPlaying) {
            cc.audioEngine.playMusic(musicToPlay, true);
        }

        this.updateSoundIcons();
    }

    onLoad() {
        Lobby.instance = this;

        this.play = this.playNode.getComponent(Play);
        this.play.node.active = false;
        this.lblGoldBalance.string = "0";
        this.lblGemBalance.string = "0";


        if (cc.sys.localStorage.getItem("XK_is_playing") === null) {
            cc.sys.localStorage.setItem("XK_is_playing", JSON.stringify(true));
        }

        if (cc.sys.localStorage.getItem("XK_is_effect_on") === null) {
            cc.sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(true));
        }
    }

    public show(isShow: boolean) {
        this.node.active = isShow;
        if (isShow) {
            cc.audioEngine.stopAll();
            this.playMusic();
        }
    }

    actBack() {
        this.node.stopAllActions();
        cc.audioEngine.stopMusic();
        cc.audioEngine.stopAll();
        this.node.removeFromParent();
        App.instance.gotoLobby();
        SortiePhotonClient.getInstance().peer.disconnect();
    }

    actPlay() {
        if (!this.photonClient || !this.photonClient.isLoggedIn) {
            return;
        }

        var params = [];
        params.push(SortiePhotonClient.EParameterCodes.OperationSubCode, SortiePhotonClient.EOperationSubCodes.JoinRoom);
        this.photonClient.sendOperation(PhotonClient.EOperationCodes.Game, params, true);

        this.playNode.active = true;
        this.show(false);
        cc.audioEngine.stopMusic();
        PanelMenu.instance.hide();
        PanelMenu.instance.updateSoundEffect();
        Play.instance.updateSoundMusic();
    }

    actExchangeCoin() {
        let exchangeCoin = cc.instantiate(this.exchangeCoinPrefab);
        this.popupContainer.addChild(exchangeCoin);
        exchangeCoin.getComponent("PopupExchangeCoin").show();
    }

    actShowPopupGuide() {
        let popupGuide = cc.instantiate(this.popupGuidePrefab);
        this.popupContainer.addChild(popupGuide);
        popupGuide.getComponent("PopupGuideXK").show();
    }

    actShowPopupHistory() {
        let popupHistory = cc.instantiate(this.popupHistoryPrefab);
        this.popupContainer.addChild(popupHistory);
        popupHistory.getComponent("PopupHistory").show();
    }

    actShowPopupTopJackpot() {
        let popupJackpot = cc.instantiate(this.popupJackpotPrefab);
        this.popupContainer.addChild(popupJackpot);
        popupJackpot.getComponent("PopupJackpot").show();
    }

    actShowPopupSetting() {
        this.popupSetting.active = true;
    }

    actClosePopupSetting() {
        this.popupSetting.active = false;
    }

    turnOnSoundMusic() {
        cc.audioEngine.resumeMusic();
        cc.sys.localStorage.setItem("XK_is_playing", JSON.stringify(true));
        this.playMusic();
        this.updateSoundIcons();
    }

    turnOffSoundMusic() {
        cc.audioEngine.pauseMusic();
        cc.sys.localStorage.setItem("XK_is_playing", JSON.stringify(false));
        this.updateSoundIcons();
    }

    turnOnSoundEffect() {
        cc.log("turnOnSoundEffect");
        let isEffectOn = JSON.parse(cc.sys.localStorage.getItem("XK_is_effect_on") || "false");
        if (!isEffectOn) {
            cc.sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(true));
            cc.audioEngine.setEffectsVolume(1);
        }
        this.iconOnSoundEffect.active = true;
        this.iconOffSoundEffect.active = false;
    }

    turnOffSoundEffect() {
        cc.log("turnOffSoundEffect");
        let isEffectOn = JSON.parse(cc.sys.localStorage.getItem("XK_is_effect_on") || "true");
        if (isEffectOn) {
            cc.sys.localStorage.setItem("XK_is_effect_on", JSON.stringify(false));
            cc.audioEngine.setEffectsVolume(0);
        }
        this.iconOnSoundEffect.active = false;
        this.iconOffSoundEffect.active = true;
    }

    updateSoundIcons() {
        let isPlaying = JSON.parse(cc.sys.localStorage.getItem("XK_is_playing") || "false");
        this.iconOnSoundMusic.active = isPlaying;
        this.iconOffSoundMusic.active = !isPlaying;

        let isEffectOn = JSON.parse(cc.sys.localStorage.getItem("XK_is_effect_on") || "false");
        this.iconOnSoundEffect.active = isEffectOn;
        this.iconOffSoundEffect.active = !isEffectOn;
    }


}
