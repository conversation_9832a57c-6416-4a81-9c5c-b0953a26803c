[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "EffectBigWin", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}], "_active": true, "_components": [{"__id__": 41}, {"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [640, 360, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}, {"__id__": 5}], "_prefab": {"__id__": 6}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2, "_originalHeight": 2, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1b05bfce-e24e-4a01-8ea9-07b8f056da85"}, "fileId": "c2843sM3BHj4/azFYRFIrI", "sync": false}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 11}, {"__id__": 14}], "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 981, "height": 688}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "effect_1", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2200, "height": 1100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "33bcadcb-3ecd-4e51-b912-9f2e3098e335"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1b05bfce-e24e-4a01-8ea9-07b8f056da85"}, "fileId": "49qMFEbylB1aRNgyCrBVaQ", "sync": false}, {"__type__": "cc.Node", "_name": "lblWin", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 38.93, "height": 88.2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -230, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "0", "_N$string": "0", "_fontSize": 70, "_lineHeight": 70, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1b05bfce-e24e-4a01-8ea9-07b8f056da85"}, "fileId": "6eB9Vd74dA7pgPiova9Tyr", "sync": false}, {"__type__": "cc.Node", "_name": "Particle_Coin", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 15}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 1}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 508.816, 0, 0, 0, 0, 1, 30, 30, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.ParticleSystem3D", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_materials": [{"__uuid__": "ccbf8604-d805-47b5-86fb-e62130461d42"}], "duration": 5, "_capacity": 100, "loop": true, "playOnAwake": true, "_prewarm": false, "_simulationSpace": 1, "simulationSpeed": 2, "startDelay": {"__id__": 16}, "startLifetime": {"__id__": 17}, "startColor": {"__id__": 18}, "scaleSpace": 1, "startSize": {"__id__": 19}, "startSpeed": {"__id__": 20}, "startRotation": {"__id__": 21}, "gravityModifier": {"__id__": 22}, "rateOverTime": {"__id__": 23}, "rateOverDistance": {"__id__": 24}, "bursts": [], "_shapeModule": {"__id__": 25}, "_velocityOvertimeModule": {"__id__": 27}, "_textureAnimationModule": {"__id__": 32}, "_renderMode": 0, "_velocityScale": 1, "_lengthScale": 1, "_mesh": null, "_id": ""}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.GradientRange", "_mode": 0, "color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 2, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 10, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0.7, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 10, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.ShapeModule", "enable": true, "_shapeType": 0, "emitFrom": 3, "radius": 1, "radiusThickness": 1, "_angle": 1.0471975511965976, "_arc": 6.283185307179586, "arcMode": 0, "arcSpread": 5, "arcSpeed": {"__id__": 26}, "length": 5, "boxThickness": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_position": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_scale": {"__type__": "cc.Vec3", "x": 80, "y": 6, "z": 1}, "alignToDirection": false, "randomDirectionAmount": 0, "sphericalDirectionAmount": 0, "randomPositionAmount": 0}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.VelocityOvertimeModule", "enable": true, "space": 1, "x": {"__id__": 28}, "y": {"__id__": 29}, "z": {"__id__": 30}, "speedModifier": {"__id__": 31}}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 5, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 1, "multiplier": 1}, {"__type__": "cc.TextureAnimationModule", "_enable": true, "_mode": 0, "numTilesX": 6, "numTilesY": 3, "animation": 0, "randomRow": true, "rowIndex": 0, "frameOverTime": {"__id__": 33}, "startFrame": {"__id__": 37}, "cycleCount": 7}, {"__type__": "cc.CurveRange", "mode": 1, "curve": {"__id__": 34}, "multiplier": 1}, {"__type__": "cc.AnimationCurve", "keyFrames": [{"__id__": 35}, {"__id__": 36}], "preWrapMode": 2, "postWrapMode": 2}, {"__type__": "cc.Keyframe", "time": 0, "value": 0, "inTangent": 0, "outTangent": 0}, {"__type__": "cc.Keyframe", "time": 1, "value": 1, "inTangent": 0, "outTangent": 0}, {"__type__": "cc.CurveRange", "mode": 0, "constant": 0, "multiplier": 1}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 14}, "asset": {"__uuid__": "a1f969c9-4911-448d-8ba2-371e160d0eb9"}, "fileId": "c0JiTawypHppu7Yg4zEnRt", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "3e6b619d-f878-4f6b-9636-c0355245b682"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1b05bfce-e24e-4a01-8ea9-07b8f056da85"}, "fileId": "dbmroBZu1Oj6+uNBdzaP7/", "sync": false}, {"__type__": "6299cZHXThBJ59v/Zzw2nts", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": {"__id__": 2}, "container": {"__id__": 7}, "duration": 10, "prizeValue": {"__id__": 12}, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "1b05bfce-e24e-4a01-8ea9-07b8f056da85"}, "fileId": "", "sync": false}]