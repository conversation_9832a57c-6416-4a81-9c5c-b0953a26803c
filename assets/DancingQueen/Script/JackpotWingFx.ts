const { ccclass, property } = cc._decorator;

@ccclass
export default class JackpotWingFx extends cc.Component {

    @property(cc.Node)
    rightWing: cc.Node = null;

    @property(cc.Node)
    leftWing: cc.Node = null;

    @property
    angleUp: number = 5;

    @property
    angleDown: number = -5;

    @property
    duration: number = 0.5;

    protected onEnable(): void {
        this.rightWing.angle = this.angleUp;
        this.leftWing.angle = this.angleDown;
        // wing separate
        cc.tween(this.rightWing)
            .to(this.duration, { angle: this.angleDown }, { easing: 'sineOut' })
            .to(this.duration, { angle: this.angleUp }, { easing: 'sineIn' })
            .union()
            .repeatForever()
            .start();

        cc.tween(this.leftWing)
            .to(this.duration, { angle: this.angleUp }, { easing: 'sineOut' })
            .to(this.duration, { angle: this.angleDown }, { easing: 'sineIn' })
            .union()
            .repeatForever()
            .start();
    }

    protected onDisable(): void {
        this.rightWing.stopAllActions();
        this.leftWing.stopAllActions();
    }
}
