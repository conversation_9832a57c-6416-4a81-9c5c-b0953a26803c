import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";

interface AccumulateGameListener {
    selectedItem?(itemID: number): void;
    fastPlay?(): void;
}

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("DancingQueen/DQAccumulate")
export default class DQAccumulate extends cc.Component {
    @property(cc.Node)
    private items: cc.Node = null;

    @property(cc.Label)
    private lblTotalPrize: cc.Label = null;

    @property(cc.Label)
    private lblTotalMultiplier: cc.Label = null;

    @property(cc.Label)
    private lblWin: cc.Label = null;

    @property([cc.SpriteFrame])
    private randomGift: cc.SpriteFrame[] = [];

    @property(cc.SpriteFrame)
    private CSymbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private RSymbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private ASymbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private ZSymbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private YSymbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private x1Symbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private x2Symbol: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    private x3Symbol: cc.SpriteFrame = null;

    private heso = 1;
    private prize = 0;
    private win = 0;
    private left = 0;
    public onFinished: Function;
    private dataBonus: AccumulateItem[] = [];
    public listeners: AccumulateGameListener = null;

    private _countdownInterval: number;
    private time = 0;

    protected start(): void {
        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(cc.Button);
            node["isOpen"] = false;
            node["sprite"] = node.getComponentInChildren(cc.Sprite);

            node["btn"].node.on('click', () => {
                cc.log(this.dataBonus);
                const value: AccumulateItem = this.dataBonus[this.dataBonus.length - this.left];
                this.onItemClick(node, value);
            });
        }
    }

    private onItemClick(node: cc.Node, value: AccumulateItem) {
        if (node["isOpen"] == false && this.left > 0) {
            node["isOpen"] = true;
            this.listeners.selectedItem && this.listeners.selectedItem(value.PrizeID);
            switch (value.PrizeID) {
                case 301:
                    node["sprite"].spriteFrame = this.CSymbol;
                    this.prize += value.PrizeStep;
                    break;
                case 302:
                    node["sprite"].spriteFrame = this.RSymbol;
                    this.prize += value.PrizeStep;
                    break;
                case 303:
                    node["sprite"].spriteFrame = this.ASymbol;
                    this.prize += value.PrizeStep;
                    break;
                case 304:
                    node["sprite"].spriteFrame = this.ZSymbol;
                    this.prize += value.PrizeStep;
                    break;
                case 305:
                    node["sprite"].spriteFrame = this.YSymbol;
                    this.prize += value.PrizeStep;
                    break;
                case 306:
                    node["sprite"].spriteFrame = this.x1Symbol;
                    this.heso += value.Multiplier;
                    break;
                case 307:
                    node["sprite"].spriteFrame = this.x2Symbol;
                    this.heso += value.Multiplier;
                    break;
                case 308:
                    node["sprite"].spriteFrame = this.x3Symbol;
                    this.heso += value.Multiplier;
                    break;
            }
            this.updateUI();
            this.left--;
            if (this.left <= 0) {
                this.hidden();
            }
        }
    }

    updateUI() {
        this.lblTotalPrize.string = Utils.formatNumber(this.prize);
        this.lblTotalMultiplier.string = this.heso.toString();
        Tween.numberTo(this.lblWin, this.prize * this.heso, 1);
    }


    show(bonus: AccumulateItem[], onFinished: Function) {
        this.node.active = true;
        this.items.active = true;

        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["isOpen"] = false;
            node["btn"] = node.getComponent(cc.Button);
            node["sprite"] = node.getComponentInChildren(cc.Sprite);

            node["sprite"].node.active = true;
            node["sprite"].spriteFrame = this.randomGift[Utils.randomRangeInt(0, this.randomGift.length)];
            node["btn"].interactable = true;
        }

        this.dataBonus = bonus;
        this.onFinished = onFinished;
        this.left = this.dataBonus.length;
        this.win = 0;
        this.heso = 1;
        this.prize = 0;

        this.startCountDown(15);
    }

    hidden() {
        this.scheduleOnce((() => {
            this.node.active = false;
            this.onFinished && this.onFinished();
        }), 2);
    }

    private startCountDown(duration: number) {
        this.time = duration;
        this._countdownInterval = setInterval(() => {
            this.time--;
            if (this.time <= 0) {
                this.endGame();
            }
        }, 1000);
    }

    private _clearTimer() {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    endGame() {
        this._clearTimer();
        this.left = 0;
    }
}

interface AccumulateItem {
    Step: number;
    PrizeID: number;
    Multiplier: number;
    PrizeStep: number;
}

export interface AccumulateGameData {
    GameSessionID: number;
    CurrentStep: number;
    TotalStep: number;
    PrizeID: number;
    Multiplier: number;
    TotalMultiplier: number;
    StepValue: number;
    TotalStepValue: number;
    IsCrazy: number;
    TotalPrizeValue: number;
    PrizeData: string;
    Items: number;
    SelectedItems: number[];
    AccumulateItemsData: AccumulateItem[];
}
