import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import App from "../../Lobby/LobbyScript/Script/common/App";
import DancingController from "./DQController";
import <PERSON><PERSON><PERSON> from "./CrazyChar";
import DQAudioManager, { AUDIO_CLIP } from "./DQAudioManager";

interface AccumulateGameListener {
    selectedItem?(itemID: number): void;
    fastPlay?(): void;
}

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("DancingQueen/DQAccumulate")
export default class DQAccumulate extends cc.Component {
    @property(cc.Node)
    private items: cc.Node = null;

    @property(cc.Label)
    private lblTotalPrize: cc.Label = null;

    @property(cc.Label)
    private lblHeSo: cc.Label = null;

    @property(cc.Label)
    private lblWin: cc.Label = null;

    @property(cc.SpriteAtlas)
    private symbolAtlas: cc.SpriteAtlas = null;

    @property(cc.Node) activeC: cc.Node = null;
    @property(cc.Node) activeR: cc.Node = null;
    @property(cc.Node) activeA: cc.Node = null;
    @property(cc.Node) activeZ: cc.Node = null;
    @property(cc.Node) activeY: cc.Node = null;

    @property(CrazyChar) charC: CrazyChar = null;
    @property(CrazyChar) charR: CrazyChar = null;
    @property(CrazyChar) charA: CrazyChar = null;
    @property(CrazyChar) charZ: CrazyChar = null;
    @property(CrazyChar) charY: CrazyChar = null;

    private _heso = 1;
    private _win = 0;
    private _totalPrize = 0;
    private left = 0;
    public onFinished: Function;
    private dataBonus: AccumulateItem[] = [];
    public listeners: AccumulateGameListener = null;

    private _countdownInterval: number;
    private time = 0;

    public get win() {
        return this._win;
    }
    public set win(value: number) {
        this._win = value;
        this.lblWin.string = Utils.formatNumber(value);
    }

    public get heso() {
        return this._heso;
    }
    public set heso(value: number) {
        this._heso = value;
        this.lblHeSo.string = `x${value}`;
    }

    protected onLoad(): void {
        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(cc.Button);
            node["isOpen"] = false;
            node["sprite"] = node.getComponentInChildren(cc.Sprite);

            node["btn"].node.on('click', () => {
                DQAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
                const value: AccumulateItem = this.dataBonus[this.dataBonus.length - this.left];
                this._clearTimer();
                this.onItemClick(node, i, value);
            });
        }
    }

    private onItemClick(node: cc.Node, index: number, value: AccumulateItem) {
        if (node["isOpen"] == false && this.left > 0) {
            node["isOpen"] = true;
            this.listeners.selectedItem && this.listeners.selectedItem(index);
            switch (value.PrizeID) {
                case 301:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('C')
                    this.win = value.PrizeStep;
                    this._totalPrize += value.PrizeStep;
                    this.activeC.active = true;
                    this.charC.charCount++;
                    break;
                case 302:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('R')
                    this.win = value.PrizeStep;
                    this._totalPrize += value.PrizeStep;
                    this.activeR.active = true;
                    this.charR.charCount++;
                    break;
                case 303:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('A')
                    this.win = value.PrizeStep;
                    this._totalPrize += value.PrizeStep;
                    this.activeA.active = true;
                    this.charA.charCount++;
                    break;
                case 304:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('Z')
                    this.win = value.PrizeStep;
                    this._totalPrize += value.PrizeStep;
                    this.activeZ.active = true;
                    this.charZ.charCount++;
                    break;
                case 305:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('Y')
                    this.win = value.PrizeStep;
                    this._totalPrize += value.PrizeStep;
                    this.activeY.active = true;
                    this.charY.charCount++;
                    break;
                case 306:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('x1')
                    this.heso += value.Multiplier;
                    break;
                case 307:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('x2')
                    this.heso += value.Multiplier;
                    break;
                case 308:
                    node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame('x3')
                    this.heso += value.Multiplier;
                    break;
            }
            this.updateTotalPrize();
            this.left--;
            this.checkEndGame();
        }
    }

    updateTotalPrize() {
        const totalPrize = this._totalPrize * this.heso;
        Tween.numberTo(this.lblTotalPrize, totalPrize, 0.3);
    }


    show(bonus: AccumulateItem[], onFinished: Function) {
        this.node.active = true;
        this.items.active = true;

        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["isOpen"] = false;
            node["btn"] = node.getComponent(cc.Button);
            node["sprite"] = node.getComponentInChildren(cc.Sprite);

            node["sprite"].node.active = true;
            node["sprite"].spriteFrame = this.symbolAtlas.getSpriteFrame(`Gift${i % 3 + 1}`);
            
            node["btn"].interactable = true;
        }

        this.dataBonus = bonus;
        this.onFinished = onFinished;
        this.left = this.dataBonus.length;
        this.win = 0;
        this.heso = 1;
        this._totalPrize = 0;
        this.resetUI();

        this.lblWin.string = '0';
        this.lblHeSo.string = 'x1';
        this.lblTotalPrize.string = '0';

        this.startCountDown(15);
    }


    hidden() {
        DancingController.getInstance().confirmDialog.show4(
            App.instance.getTextLang("sl52") + '\n' + Utils.formatNumber(this._totalPrize * this.heso) + '\n' + App.instance.getTextLang("dc27"),
            null,
            null,
            (isConfirm) => {
                if (isConfirm) {
                    this.node.active = false;
                    this.resetUI();
                    this.onFinished && this.onFinished();
                }
            }
        );
    }

    private startCountDown(duration: number) {
        this.time = duration;
        this._countdownInterval = setInterval(() => {
            this.time--;
            if (this.time <= 0) {
                this._clearTimer();
                this.resetUI();
                this.node.active = false;
                this.onFinished && this.onFinished();
                this.listeners.fastPlay && this.listeners.fastPlay();
            }
        }, 1000);
    }

    private _clearTimer() {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    private checkEndGame() {
        if (this.left <= 0) {
            this.endGame();
        }
    }

    endGame() {
        this._clearTimer();
        this.left = 0;
        this.scheduleOnce(() => {
            this.hidden();
        }, 1)
    }

    private resetUI() {
        this.activeC.active = false;
        this.activeR.active = false;
        this.activeA.active = false;
        this.activeZ.active = false;
        this.activeY.active = false;
        this.charC.charCount = 0;
        this.charR.charCount = 0;
        this.charA.charCount = 0;
        this.charZ.charCount = 0;
        this.charY.charCount = 0;
    }
}

interface AccumulateItem {
    Step: number;
    PrizeID: number;
    Multiplier: number;
    PrizeStep: number;
}

export interface AccumulateGameData {
    GameSessionID: number;
    CurrentStep: number;
    TotalStep: number;
    PrizeID: number;
    Multiplier: number;
    TotalMultiplier: number;
    StepValue: number;
    TotalStepValue: number;
    IsCrazy: number;
    TotalPrizeValue: number;
    PrizeData: string;
    Items: number;
    SelectedItems: number[];
    AccumulateItemsData: AccumulateItem[];
}
