import App from "../../Lobby/LobbyScript/Script/common/App";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import DancingController from "./DQController";

// data bonus: 
interface BonusGameListener {
    selectedItem?(itemID: number): void;
    fastPlay?(): void;
}

const { ccclass, property, menu } = cc._decorator;
@ccclass
@menu("DancingQueen/DQPopupBonus")
export default class DQPopupBonus extends cc.Component {
    @property(cc.Node) items: cc.Node = null;
    @property(cc.Node) miniGame: cc.Node = null;

    @property(cc.Label) lblTurn: cc.Label = null;
    @property(cc.Label) lblHeSoNhan: cc.Label = null;

    @property(cc.Label) lblItemPrize: cc.Label = null;
    @property(cc.Label) lblTotalPrize: cc.Label = null;
    @property(cc.Node) btnQuickPlay: cc.Node = null;

    private _countdownInterval: number;
    bonusData: BonusItem[] = [];
    onFinish: Function = null;
    private _totalTurn: number;
    private _totalPrize: number;
    left = 0;
    time = 0;
    itemID: number;


    public listeners: BonusGameListener = null;
    get totalTurn() {
        return this._totalTurn;
    }

    set totalTurn(value: number) {
        this._totalTurn = value;
        this.lblTurn.string = `${value}`;
    }

    get totalPrize() {
        return this._totalPrize;
    }

    set totalPrize(value: number) {
        this._totalPrize = value;
        Tween.numberTo(this.lblTotalPrize, value, 1);
    }


    onLoad() {
        this.btnQuickPlay.on('click', () => {
            this.listeners.fastPlay && this.listeners.fastPlay();
            this.totalPrize = this.bonusData[this.bonusData.length - 1].PrizeValue;
            this.endGame();
        });
    }

    protected onEnable(): void {
        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(cc.Button);
            node["label"] = node.getComponentInChildren(cc.Label);
            node["sprite"] = node.getComponentInChildren(cc.Sprite);
            node["btn"].node.on('click', () => {
                this._clearTimer();
                const value: BonusItem = this.bonusData[this.bonusData.length - this.left];
                if (node["isOpen"] == false) {
                    node["isOpen"] = true;
                    node["sprite"].node.active = false;
                    node["btn"].interactable = false;

                    this.left--;
                    this.totalTurn--;
                    this.listeners.selectedItem && this.listeners.selectedItem(this.itemID++);

                    if (value.PrizeValue > 0) {
                        node['label'].string = Utils.formatNumber(value.PrizeValue);
                        this.lblItemPrize.string = Utils.formatNumber(value.PrizeValue);
                        this.totalPrize += value.PrizeValue;
                    }
                    else {
                        this.showMiniGame();
                    }

                }
            });
        }
    }


    show(bonus: BonusGameData, onFinished: Function) {
        this.node.active = true;
        this.items.active = true;
        this.miniGame.active = false;

        for (let node of this.items.children) {
            node["isOpen"] = false;
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);
            node["btn"] = node.getComponent(cc.Button);

            node["label"].string = "";
            node["sprite"].node.active = true;
            node["btn"].interactable = true;
        }

        this.bonusData = bonus.BonusItemsData;
        this.onFinish = onFinished;
        this.totalTurn = bonus.TotalTurn;
        this.left = bonus.TotalTurn;

        this.btnQuickPlay.active = true;
        this.lblTurn.node.parent.active = true;
        this.lblHeSoNhan.node.parent.active = false;

        this.lblItemPrize.string = '0';
        this.totalPrize = 0;
        this.itemID = 1;

        this.startCountdown(15);
    }

    showMiniGame() {
        this.miniGame.active = true;
        this.items.active = false;
        this.lblHeSoNhan.node.parent.active = true;
        this.lblTurn.node.parent.active = false;

        for (let node of this.miniGame.children) {
            node["isOpen"] = false;
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);
            node["btn"] = node.getComponent(cc.Button);

            node["label"].string = "";
            node["sprite"].node.active = true;
            node["btn"].interactable = true;

            this.lblHeSoNhan.string = '';
            this.lblHeSoNhan.node.parent.active = true;
            this.lblTurn.node.parent.active = false;

            node['btn'].node.off('click');
        }

        for (let node of this.miniGame.children) {
            node["btn"].node.on('click', () => {
                cc.log("on click mini item")
                const value: BonusItem = this.bonusData[this.bonusData.length - 1];
                if (node["isOpen"] == false) {
                    node["isOpen"] = true;
                    node["sprite"].node.active = false;
                    node["btn"].interactable = false;
                    node['label'].string = `x${value.Multiplier}`;
                    this.lblHeSoNhan.string = `x${value.Multiplier}`;
                    this.totalPrize *= value.Multiplier;
                    this.scheduleOnce(() => {
                        this.miniGame.active = false;
                        this.left--;
                        this.totalTurn--;
                        this.listeners.selectedItem && this.listeners.selectedItem(this.itemID++);
                        this.endGame();
                    }, 1)
                }
            });
        }
    }

    startCountdown(duration: number) {
        this.time = duration;
        this._countdownInterval = setInterval(() => {
            this.time--;
            if (this.time <= 0) {
                this.endGame();
            }
        }, 1000);
    }

    private _clearTimer() {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    endGame() {
        this._clearTimer();
        this.totalTurn = 0;
        DancingController.getInstance().confirmDialog.show4(
            App.instance.getTextLang("sl52") + '\n' + Utils.formatNumber(this.totalPrize) + '\n' + App.instance.getTextLang("sl88"),
            null,
            null,
            (isConfirm) => {
                if (isConfirm) {
                    this.node.active = false;
                    this.onFinish && this.onFinish();
                }
            }
        );
    }

}


interface BonusItem {
    Step: number;
    PrizeID: number;
    Multiplier: number;
    PrizeValue: number;
}

export interface BonusGameData {
    ItemID: number;
    BonusSpinID: number;
    BetValue: number;
    Multiplier: number;
    TotalMultiplier: number;
    TotalTurn: number;
    CurrentTurn: number;
    BonusPrizeID: number;
    PrizeID: number;
    PrizeValue: number;
    AwardedPrizeValue: number;
    TotalPrizeValue: number;
    SelectedItemIDs?: number[];
    BonusItemsData?: BonusItem[];
}
