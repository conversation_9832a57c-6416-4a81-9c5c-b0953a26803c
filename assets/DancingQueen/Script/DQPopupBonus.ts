import App from "../../Lobby/LobbyScript/Script/common/App";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import DancingController from "./DQController";
import DQAudioManager, { AUDIO_CLIP } from "./DQAudioManager";

// data bonus: 
interface BonusGameListener {
    selectedItem?(itemID: number): void;
    fastPlay?(): void;
}

const { ccclass, property, menu } = cc._decorator;
@ccclass
@menu("DancingQueen/DQPopupBonus")
export default class DQPopupBonus extends cc.Component {
    @property(cc.Node) items: cc.Node = null;
    @property(cc.Node) miniGame: cc.Node = null;

    //@property(cc.Label) lblTurn: cc.Label = null;
    //@property(cc.Label) lblHeSoNhan: cc.Label = null;

    @property(cc.Label) lblItemPrize: cc.Label = null;
    @property(cc.Label) lblTotalPrize: cc.Label = null;
    @property(cc.Node) btnQuickPlay: cc.Node = null;

    bonusData: BonusItem[] = [];
    onFinish: Function = null;
    private _totalPrize: number;
    left = 0;
    bonusPrizeID: number;

    private _countdownInterval: number;
    time = 0;


    public listeners: BonusGameListener = null;

    get totalPrize() {
        return this._totalPrize;
    }

    set totalPrize(value: number) {
        this._totalPrize = value;
        Tween.numberTo(this.lblTotalPrize, value, 1);
    }


    onLoad() {
        this.btnQuickPlay.on('click', () => {
            DQAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
            this.btnQuickPlay.active = false;
            this.time = 0;
            this.listeners.fastPlay && this.listeners.fastPlay();
            this.endGame();
        });
    }

    protected onEnable(): void {
        for (let i = 0; i < this.items.childrenCount; i++) {
            let node = this.items.children[i];
            node["btn"] = node.getComponent(cc.Button);
            node["label"] = node.getComponentInChildren(cc.Label);
            // node["sprite"] = node.getComponentInChildren(cc.Sprite);
            node['anim'] = node.getComponent(cc.Animation);
            node["isOpen"] = false;
            node["btn"].node.on('click', () => {
                DQAudioManager.Instance.playEffect(AUDIO_CLIP.BUBBLE);
                node['anim'].play('bonus_explode');
                this._clearTimer();
                const value: BonusItem = this.bonusData[this.bonusData.length - this.left];
                if (node["isOpen"] == false) {
                    node["isOpen"] = true;
                    // node["sprite"].node.active = false;
                    node["btn"].interactable = false;

                    this.left--;
                    this.listeners.selectedItem && this.listeners.selectedItem(i + 1);

                    if (value.PrizeValue > 0) {
                        node['label'].string = Utils.formatNumber(value.PrizeValue);
                        this.lblItemPrize.string = Utils.formatNumber(value.PrizeValue);
                        this.totalPrize += value.PrizeValue;
                    }
                    else {
                        node['label'].string = '0'
                        this.lblItemPrize.string = '0';
                        this.showMiniGame();
                    }

                }
            });
        }
    }


    show(bonus: BonusGameData, onFinished: Function) {
        this.node.active = true;
        this.items.active = true;
        this.miniGame.active = false;

        for (let node of this.items.children) {
            this.node.active = true;
            node["isOpen"] = false;
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node['anim'] = node.getComponent(cc.Animation);
            //node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);
            node["btn"] = node.getComponent(cc.Button);

            node["label"].string = "";
            // node["sprite"].node.active = true;
            node["btn"].interactable = true;
            node['anim'].play('bonus_idle');
        }

        this.bonusData = bonus.BonusItemsData;
        this.onFinish = onFinished;
        this.left = bonus.TotalTurn;
        this.bonusPrizeID = bonus.BonusPrizeID;

        this.btnQuickPlay.active = true;

        this.lblItemPrize.string = '0';
        this.lblTotalPrize.string = '0';
        this.totalPrize = 0;

        this.startCountdown(15);
    }

    showMiniGame() {
        this.miniGame.active = true;
        this.miniGame.children.forEach(node => node.active = false);
        this.scheduleOnce(() => {
            this.items.active = false;
            this.miniGame.children.forEach(node => node.active = true);
        }, 1)

        for (let i = 0; i < this.miniGame.children.length; i++) {
            let node = this.miniGame.children[i];
            node["label"] = node.getChildByName("label").getComponent(cc.Label);
            node['anim'] = node.getComponent(cc.Animation);
            node["sprite"] = node.getChildByName("sprite").getComponent(cc.Sprite);
            node["btn"] = node.getComponent(cc.Button);

            node["label"].string = "";
            node["label"].node.opacity = 255;
            node["sprite"].node.opacity = 255;
            node["btn"].interactable = true;
            node['anim'].play('bonus_idle');

            node['btn'].node.off('click');
        }

        let multiplier = this.bonusData[this.bonusData.length - 1].Multiplier;
        let hideMultipliers = [];
        for (let m of this.getMultipliersByBonusPrizeID(this.bonusPrizeID)) {
            if (m !== multiplier) {
                hideMultipliers.push(m);
            }
        }


        for (let i = 0; i < this.miniGame.children.length; i++) {
            let node = this.miniGame.children[i];
            node["btn"].node.on('click', () => {
                DQAudioManager.Instance.playEffect(AUDIO_CLIP.BUBBLE);

                this.miniGame.children.forEach(node => node["btn"].interactable = false);

                node['anim'].play('bonus_explode');
                node['label'].string = `x${multiplier}`;

                this.totalPrize *= multiplier;

                this.scheduleOnce(() => {
                    let remIdx = 0;
                    this.miniGame.children.forEach((node, index) => {
                        node['anim'].stop();
                        if (index !== i) {
                            let mult = hideMultipliers[remIdx++];
                            cc.log('multi', mult);
                            node['label'].string = `x${mult}`;
                            node['label'].node.opacity = 100;
                            node['sprite'].node.runAction(cc.fadeOut(0.5));
                        }
                    });
                },1.5)

                this.scheduleOnce(() => {
                    this.miniGame.active = false;
                    this.left--;
                    this.listeners.selectedItem && this.listeners.selectedItem(100);
                    this.endGame();
                }, 3.5)
            });
        }
    }

    // Multiplier assignment based on BonusPrizeID
    private getMultipliersByBonusPrizeID(bonusPrizeID: number): number[] {
        switch (bonusPrizeID) {
            case 25:
                return [3, 4, 5];
            case 26:
                return [2, 3, 4];
            case 27:
                return [1, 2, 3];
            default:
                return [1, 2, 3]; // Default fallback
        }
    }

    // Reveal other nodes with decreased opacity
    private revealOtherNodes(selectedIndex: number) {
        for (let i = 0; i < this.miniGame.children.length; i++) {
            if (i !== selectedIndex) {
                let node = this.miniGame.children[i];
                node['label'].string = `x${node["multiplier"]}`;
                node['label'].node.opacity = 128;
            }
        }
    }

    startCountdown(duration: number) {
        this.time = duration;
        this._countdownInterval = setInterval(() => {
            this.time--;
            if (this.time <= 0) {
                this.endGame();
                this.listeners.fastPlay && this.listeners.fastPlay();
            }
        }, 1000);
    }

    private _clearTimer() {
        if (this._countdownInterval !== null) {
            clearInterval(this._countdownInterval);
            this._countdownInterval = null;
        }
    }

    endGame() {
        this._clearTimer();
        this.left = 0;
        this.time = 0;
        this.btnQuickPlay.active = false;
        this.totalPrize = this.bonusData[this.bonusData.length - 1].PrizeValue;
        DQAudioManager.Instance.playEffect(AUDIO_CLIP.FINISH_BONUS);
        DancingController.getInstance().confirmDialog.show4(
            App.instance.getTextLang("sl52") + '\n' + Utils.formatNumber(this.totalPrize) + '\n' + App.instance.getTextLang("sl88"),
            null,
            null,
            (isConfirm) => {
                if (isConfirm) {
                    this.node.active = false;
                    this.onFinish && this.onFinish();
                }
            }
        );
    }

}


interface BonusItem {
    Step: number;
    PrizeID: number;
    Multiplier: number;
    PrizeValue: number;
}

export interface BonusGameData {
    ItemID: number;
    BonusSpinID: number;
    BetValue: number;
    Multiplier: number;
    TotalMultiplier: number;
    TotalTurn: number;
    CurrentTurn: number;
    BonusPrizeID: number;
    PrizeID: number;
    PrizeValue: number;
    AwardedPrizeValue: number;
    TotalPrizeValue: number;
    SelectedItemIDs?: number[];
    BonusItemsData?: BonusItem[];
}
