
const { ccclass, property } = cc._decorator;

@ccclass
export default class CrazyChar extends cc.Component {
    @property(cc.Node)
    charActive: cc.Node = null;

    @property(cc.Label)
    charNum: cc.Label = null;

    private _charCount: number = 0;
    public get charCount(): number {
        return this._charCount;
    }
    public set charCount(value: number) {
        this._charCount = value;
        this.charNum.string = value.toString();
        this.charNum.node.parent.active = value > 0;
        this.charActive.active = value > 0;
        if (value === 1) {
            this.charActive.scale = 0;
            cc.tween(this.charActive).to(0.3, { scale: 1 }, { easing: 'backOut' }).start();
        }
    }

    onLoad() {
        this.charActive.active = false;
        this.charNum.string = '0';
        this.charNum.node.parent.active = false;
    }
}
