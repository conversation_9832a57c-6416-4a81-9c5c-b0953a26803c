import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Configs from "../../Lobby/MoveScript/Configs";
import Http from "../../Lobby/MoveScript/Http";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("DancingQueen/DQPopupLSH")
export default class DQPopupLSH extends cc.Component {
    @property(cc.Node)
    itemTemplate: cc.Node = null;

    @property(cc.Node)
    itemContainer: cc.Node = null;

    private currentPage: number = 1;
    private readonly pageSize: number = 20;
    private isLoading: boolean = false;
    private hasMoreData: boolean = true;

    show(){
        this.node.active = true;
        this.itemContainer.removeAllChildren();
        this.resetPagination();
        this.loadData();

        this.getComponentInChildren(cc.ScrollView).node.on('scroll-to-bottom', this.onScrollToBottom, this);
    }

    dismiss(){
        this.node.active = false;
        this.getComponentInChildren(cc.ScrollView).node.off('scroll-to-bottom', this.onScrollToBottom, this);
    }

    private resetPagination() {
        this.currentPage = 1;
        this.hasMoreData = true;
        this.isLoading = false;
    }

    private onScrollToBottom() {
        if (!this.isLoading && this.hasMoreData) {
            this.loadData().then();
        }
    }

    private async loadData() {
        this.isLoading = true;
        App.instance.showLoading(true);
        try {
            const result = await this.fetchHistory(this.currentPage);
            this.createItems(result);
            this.hasMoreData = result.length >= this.pageSize;
        } catch (e) {
            cc.error("Error fetching Jackpot History", e);
        } finally {
            this.isLoading = false;
            App.instance.showLoading(false);
        }
    }

    private fetchHistory(page: number): Promise<any[]> {
        return new Promise((resolve, reject) => {
            Http.get(Configs.App.DOMAIN_CONFIG['Dancing_GetTransactionLog'], {
                "CurrencyID": Configs.Login.CurrencyID,
                "type": 2,
                "Page": page,
                "PageSize": this.pageSize,
            }, (status, response) => {
                if (status === 200) {
                    resolve(response["d"]);
                } else {
                    reject(new Error("Error fetching Jackpot History"));
                }
            })
        })
    }

    private createItems(result: any[]) {
        for (let i = 0; i < result.length; i++) {
            let itemRow = cc.instantiate(this.itemTemplate);
            itemRow.active = true;
            this.itemContainer.addChild(itemRow);
            itemRow.children[0].getComponent(cc.Label).string = Utils.formatDatetime(result[i].createTime, "dd/MM/yyyy HH:mm:ss");
            itemRow.children[1].getComponent(cc.Label).string = Utils.formatNumberMin(result[i].betValue);
            itemRow.children[2].getComponent(cc.Label).string = `${result[i].nickname}`;
            itemRow.children[3].getComponent(cc.Label).string = result[i].jackPotNum;
            itemRow.children[4].getComponent(cc.Label).string = Utils.formatNumber(result[i].paylinePrizeValue);
        }
    }

}
