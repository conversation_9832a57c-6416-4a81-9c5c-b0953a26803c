import ConfirmDialog from "../../Lobby/LobbyScript/Script/common/ConfirmDialog";

const {ccclass, menu} = cc._decorator;

@ccclass
@menu("DancingQueen/DQConfirmDialog")
export class DQConfirmDialog extends ConfirmDialog {
    private autoCloseTimer: number = null;

    show4(msg: string, doneTitle?: string, confirmTitle?: string, onDismissed?: (isConfirm: boolean) => void): void {
        // Call parent show4 method
        super.show4(msg, doneTitle, confirmTitle, onDismissed);

        // Clear any existing timer
        if (this.autoCloseTimer !== null) {
            this.unschedule(this.autoClose);
            this.autoCloseTimer = null;
        }

        // Schedule auto-close after 5 seconds
        this.scheduleOnce(this.autoClose, 5);
    }

    private autoClose = () => {
        // Auto-close without confirming (equivalent to cancel/done button)
        this.actConfirm();
    }

    actConfirm() {
        // Clear auto-close timer when user manually confirms
        if (this.autoCloseTimer !== null) {
            this.unschedule(this.autoClose);
            this.autoCloseTimer = null;
        }
        super.actConfirm();
    }

    dismiss() {
        // Clear auto-close timer when dialog is manually dismissed
        if (this.autoCloseTimer !== null) {
            this.unschedule(this.autoClose);
            this.autoCloseTimer = null;
        }
        super.dismiss();
    }
}
