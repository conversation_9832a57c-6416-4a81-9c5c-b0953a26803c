const { ccclass, property } = cc._decorator;

@ccclass
export default class DQPayLines extends cc.Component {
    protected onLoad() {
        this.node.children.forEach(child => child.active = false);
    }

    public async showPayLinesAnimation(prizeLines: string[]): Promise<void> {
        let activePayLines: cc.Node[] = [];
        for (let p of prizeLines) {
            activePayLines.push(this.node.getChildByName(`Line${p.split(",")[0]}`));
        }
        activePayLines.forEach(line => {
            if (line) {
                line.active = true;
            }
        });
    }

    public showLinesSelected(lineCount: number) {
        this.resetAllPayLines();
        for (let i = 1; i <= lineCount; i++) {
            this.node.getChildByName(`Line${i}`).active = true;
        }
    }

    public resetAllPayLines() {
        for (let line of this.node.children) {
            line.active = false;
        }
    }
}
