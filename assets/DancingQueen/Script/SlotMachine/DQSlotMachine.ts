import Utils from "../../../Lobby/LobbyScript/Script/common/Utils";
import {BaseSlotMachine} from "../../../Lobby/Slot/BaseSlotMachine";
import { SymbolState } from "../../../Lobby/Slot/BaseSlotSymbol";
import DQPayLines from "./DQPayLines";
import { DQSlotSymbol } from "./DQSlotSymbol";
import DQWildColumn from "./DQWildColumn";

const {ccclass, property} = cc._decorator;

const TURBO_CONFIG = {
    elasticPercent: 30,
    symbolOffset: 24,
    spinDuration: 1.5,
    delayReel: 0.1,
    rowCount: 3,
};

const NORMAL_CONFIG = {
    elasticPercent: 30,
    symbolOffset: 24,
    spinDuration: 2.5,
    delayReel: 0.25,
    rowCount: 3,
};

@ccclass("DQSlotMachine")
export class DQSLotMachine extends BaseSlotMachine {

    @property(DQPayLines)
    payLines: DQPayLines = null;

    @property(DQWildColumn)
    wildColumn: DQWildColumn = null;

    protected initializeSymbol(symbolNode: cc.Node, isBlur: boolean): void {
        // random in list [5,6,7,8,9,10,11]
        let sID = `${Utils.randomRangeInt(5, 11)}`
        symbolNode.getComponent(DQSlotSymbol).setIsBlur(isBlur).setId(sID).show();
    }

    override async highlightItems(indexes: number[], prizeLines?: string[]): Promise<void> {
        // for (let item of this.itemPosition) {
        //     await item.getComponent(DQSlotSymbol).setState(SymbolState.HIDE);
        // }
        await this.payLines.showPayLinesAnimation(prizeLines);
        await super.highlightItems(indexes, prizeLines);
    }

    override async resetAllItems(): Promise<void> {
        super.resetAllItems();
        this.payLines.resetAllPayLines();
    }

    public setTurbo(isTurbo: boolean){
        this.setConfig(isTurbo ? TURBO_CONFIG : NORMAL_CONFIG);
    }

    public async showWildColumnAnimation(spinData: string){
        // parse to array
        const matrix = spinData.split(",").map(Number);
        let wildColumns = [];
        for(let i = 0; i < matrix.length; i++){
            if(matrix[i] == 1){
                let colIdx = i % this.reels.length;
                if(wildColumns.indexOf(colIdx) == -1){
                    wildColumns.push(colIdx);
                }
            }
        }
        await this.wildColumn.showWildColumnAnimation(wildColumns);
        wildColumns.forEach((col) => {
            for(let i = 0; i < 3; i++){
                let item = this.getItemAtPosition(i * this.reels.length + col + 1);  // +1 because of the base 1
                item.getComponent(DQSlotSymbol).setId("1").show();
            }
        });
    }

    public async startSpin(resultSpin: string): Promise<void> {
        await super.startSpin(resultSpin);
        await this.showWildColumnAnimation(resultSpin);
    }

}
