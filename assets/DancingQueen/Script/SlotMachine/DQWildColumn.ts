
const {ccclass, property} = cc._decorator;

@ccclass
export default class DQWildColumn extends cc.Component {

    private wildCols: cc.Node[] = []; 

    protected onLoad(){
        for(let i = 0; i < this.node.childrenCount; i++){
            this.wildCols.push(this.node.children[i]);
            this.wildCols[i].active = false;
        }
    }

    public async showWildColumnAnimation(wildColumns: number[]): Promise<void> {
        cc.log("showWildColumnAnimation", wildColumns);
        await Promise.all(wildColumns.map(async (col) => {
            this.wildCols[col].active = true;
            await this.playWildOn(col);
        }));
    }

    private playWildOn(col: number): Promise<void> {
        this.wildCols[col].getComponent(cc.Animation).play('wild_column');
        return new Promise<void>((resolve) => {
            this.wildCols[col].getComponent(cc.Animation).once(cc.Animation.EventType.FINISHED, () => {
                //anim fadeout
                this.wildCols[col].runAction(cc.sequence(
                    cc.fadeOut(0.5),
                    cc.callFunc(() => {
                        this.wildCols[col].active = false;
                        this.wildCols[col].opacity = 255;
                        resolve();
                    })
                ));
            });
        });
    }
}
