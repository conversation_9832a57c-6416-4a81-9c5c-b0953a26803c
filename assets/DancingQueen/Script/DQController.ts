import { EffectC<PERSON>, EnumEffect, SlotPlayerResponse } from "../../Lobby/Slot/SlotConfig";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import DQPopupLSC from "./DQPopupLSC";
import DQPopupLSH from "./DQPopupLSH";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import DQPopupGuide from "./DQPopupGuide";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";
import DQSignalRClient from "../../Lobby/LobbyScript/Script/networks/DQSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import App from "../../Lobby/LobbyScript/Script/common/App";
import { DQSLotMachine } from "./SlotMachine/DQSlotMachine";
import Http from "../../Lobby/MoveScript/Http";
import { BottumHudController } from "./BottomHud/BottomHudController";
import { ModelController } from "./ModelController";
import { DQConfirmDialog } from "./DQConfirmDialog";
import { EffectDialog } from "./EffectDialog";
import { InfoView } from "../../Lobby/Slot/InfoView";
import DQAccumulate, { AccumulateGameData } from "./DQAccumulate";
import DQPopupBonus, { BonusGameData } from "./DQPopupBonus";
import { SlotIconEvent } from "../../Lobby/Common/SlotIconEvent";
import DQTrial from "./DQTrial";
import DQAudioManager, { AUDIO_CLIP } from "./DQAudioManager";

export const enum GameStatus {
    SPIN = 1,
    X2,
    BONUS,
    CRAZY
}

export interface IDancingObserver {
    onChangeRoom(sender: DQController, roomID: number): void;
}

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("DancingQueen/DQController")
export default class DQController extends cc.Component {
    private static sharedInstance?: DQController;
    private observers: { [index: string]: IDancingObserver } = {};

    public static getInstance(): DQController {
        return this.sharedInstance || (this.sharedInstance = new this());
    }

    public addObserver(key: string, observer: IDancingObserver) {
        this.observers[key] = observer;
    }

    public removeObserver(key: string) {
        delete this.observers[key];
    }

    public async dispatch(callback: (observer: IDancingObserver) => Promise<void>) {
        await Promise.all(Object.keys(this.observers).map(key => callback(this.observers[key])));
    }

    @property(DQSLotMachine)
    private slotMachine: DQSLotMachine = null;

    @property(DQAccumulate)
    private crazyMiniGame: DQAccumulate = null;

    @property(DQPopupBonus)
    private bonusMiniGame: DQPopupBonus = null;

    @property(cc.Node) popupContainer: cc.Node = null;
    @property(DQAudioManager) audioManager: DQAudioManager = null;
    @property(ModelController) modelControl: ModelController = null;

    @property(cc.Prefab) private popupHistoryPrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupWinnersPrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupGuidePrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupConfirmPrefab: cc.Prefab = null;
    @property(cc.Prefab) private effectBigWinPrefab: cc.Prefab = null;
    @property(cc.Prefab) private effectBonusPrefab: cc.Prefab = null;
    @property(cc.Prefab) private effectCrazyPrefab: cc.Prefab = null;
    @property(cc.Prefab) private effectFreeSpinPrefab: cc.Prefab = null;
    @property(cc.Prefab) private effectJackpotPrefab: cc.Prefab = null;

    @property(cc.Label) lblJackpot: cc.Label = null;
    @property(cc.Label) lblSession: cc.Label = null;
    @property(cc.Label) lblBet: cc.Label = null;
    @property(cc.Label) lblTotalLine: cc.Label = null;
    @property(cc.Label) lblTotalBet: cc.Label = null;
    @property(cc.Label) lblWinCash: cc.Label = null;
    @property(cc.Label) lblWinCashFreeSpin: cc.Label = null;
    @property(cc.Label) lblBalanceFreeSpin: cc.Label = null;
    @property(cc.Label) lblPayline: cc.Label = null;
    @property(cc.Label) lblAutoSpinLeft: cc.Label = null;
    @property(cc.Label) lblTrialBalance: cc.Label = null;
    @property(cc.Label) lblTotalPrizeFreeSpin: cc.Label = null;
    @property(cc.Label) lblTotalFreeSpin: cc.Label = null;
    @property(cc.Label) lblFreeTicket: cc.Label = null;

    @property(cc.Button) btnSpin: cc.Button = null;
    @property(cc.Toggle) buttonTurbo: cc.Toggle = null;
    @property(cc.Button) btnStopAutoSpin: cc.Button = null;
    @property(cc.Button) btnLineUp: cc.Button = null;
    @property(cc.Button) btnLineDown: cc.Button = null;
    @property(cc.Button) btnBetUp: cc.Button = null;
    @property(cc.Button) btnBetDown: cc.Button = null;

    @property(cc.Toggle) toggleSetting: cc.Toggle = null;
    @property(cc.Toggle) toggleTrial: cc.Toggle = null;
    @property(cc.Toggle) toggleMusic: cc.Toggle = null;
    @property(cc.Toggle) toggleSound: cc.Toggle = null;

    @property(BottumHudController) bottomHud: BottumHudController = null;

    @property(cc.Node) mainGame: cc.Node = null;
    @property(cc.Node) autoFrame: cc.Node = null;

    @property(InfoView) infoPanel: InfoView = null;
    @property(SlotIconEvent) eventIcon: SlotIconEvent = null;
    @property(DQConfirmDialog) confirmDialog: DQConfirmDialog = null;

    @property(cc.Sprite) paylineBox: cc.Sprite = null;
    @property(cc.SpriteFrame) paylineBox1: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) paylineBox2: cc.SpriteFrame = null;

    private _popupHistory: DQPopupLSC = null;
    private _popupWinners: DQPopupLSH = null;
    private _popupGuide: DQPopupGuide = null;
    private _popupConfirm: DQConfirmDialog = null;

    private _effectCreator: EffectCreator = null;


    private playerResponse: DancingPlayer = null;
    private isHolding = false;
    private holdTimeout: any = null;
    private _isShowingPayLines = false;

    private _isFreeSpin = false;

    get popupHistory(): DQPopupLSC {
        if (this._popupHistory == null) {
            this._popupHistory = cc.instantiate(this.popupHistoryPrefab).getComponent(DQPopupLSC);
            this._popupHistory.node.parent = this.popupContainer;
        }
        return this._popupHistory;
    }

    get popupWinners(): DQPopupLSH {
        if (this._popupWinners == null) {
            this._popupWinners = cc.instantiate(this.popupWinnersPrefab).getComponent(DQPopupLSH);
            this._popupWinners.node.parent = this.popupContainer;
        }
        return this._popupWinners;
    }

    get popupGuide(): DQPopupGuide {
        if (this._popupGuide == null) {
            this._popupGuide = cc.instantiate(this.popupGuidePrefab).getComponent(DQPopupGuide);
            this._popupGuide.node.parent = this.popupContainer;
        }
        return this._popupGuide;
    }

    get popupConfirm(): DQConfirmDialog {
        if (this._popupConfirm == null) {
            this._popupConfirm = cc.instantiate(this.popupConfirmPrefab).getComponent(DQConfirmDialog);
            this.node.parent.addChild(this._popupConfirm.node);
        }
        return this._popupConfirm;
    }

    private _isSpinning = false;
    public get isSpinning() {
        return this._isSpinning;
    }
    public set isSpinning(value: boolean) {
        this._isSpinning = value;
        this.btnSpin.interactable = !value;
        this.btnLineUp.interactable = !value;
        this.btnLineDown.interactable = !value;
        this.btnBetUp.interactable = !value;
        this.btnBetDown.interactable = !value;
        this.toggleTrial.interactable = !value;
        if (value) this.autoFrame.active = false;
    }

    private _autoSpinLeft = 0;
    public get autoSpinLeft() {
        return this._autoSpinLeft;
    }
    public set autoSpinLeft(value: number) {
        this._autoSpinLeft = value;
        this.btnStopAutoSpin.node.active = value > 0;
        this.btnSpin.node.active = value <= 0;
        this.lblAutoSpinLeft.string = value.toString();
    }

    private _roomID: number = 1;
    public get roomID() {
        return this._roomID;
    }
    public set roomID(value: number) {
        this._roomID = value;
        DQSignalRClient.getInstance().send(
            'PlayNow', [{ "CurrencyID": Configs.Login.CurrencyID, "RoomID": value }],
            (data) => {
            }
        )
    }

    private _totalLine: number = 25;
    public get totalLine() {
        return this._totalLine;
    }
    public set totalLine(value: number) {
        this._totalLine = value;
        this.lblTotalLine.string = value.toString();
    }

    private _freeTicket: number = 0;
    public get freeTicket(): number {
        return this._freeTicket;
    }
    public set freeTicket(value: number) {
        this._freeTicket = value;
        this.lblFreeTicket.string = value.toString() + ' Free'
        this.lblFreeTicket.node.parent.active = value > 0;
    }

    private isTrial: boolean = false;
    private _trialBalance: number = 0;
    public get trialBalance(): number {
        return this._trialBalance;
    }
    public set trialBalance(value: number) {
        this._trialBalance = value;
        this.lblTrialBalance.string = Utils.formatNumber(value);
    }

    private _totalPrizeFreeSpin: number = 0;
    public get totalPrizeFreeSpin(): number {
        return this._totalPrizeFreeSpin;
    }
    public set totalPrizeFreeSpin(value: number) {
        this._totalPrizeFreeSpin = value;
        this.lblTotalPrizeFreeSpin.string = Utils.formatNumber(value);
    }

    private _totalFreeSpin: number = 0;
    public get totalFreeSpin(): number {
        return this._totalFreeSpin;
    }
    public set totalFreeSpin(value: number) {
        this._totalFreeSpin = value;
        this.lblTotalFreeSpin.string = value.toString();
    }


    protected onLoad() {
        window["Dancing"] = this;
        DQController.sharedInstance = this;
        this.initListeners();
        this.initEffects();
        this.initHubs();
        this.slotMachine.setTurbo(false);
        this.slotMachine.initializeReels();
        this.modelControl.changeNormalOutfit();
    }

    protected start() {
        this.autoSpinLeft = 0;
        this.bottomHud.setState(GameStatus.SPIN);
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        this.totalLine = 25;
        this.roomID = 1;
    }

    protected onEnable(): void {
        this.toggleMusic.isChecked = !this.audioManager.isMuteMusic;
        this.toggleSound.isChecked = !this.audioManager.isMuteEffect;

        // Resume audio if it was paused (when game becomes visible again)
        if (this.audioManager.isPaused) {
            this.audioManager.resumeAllAudio();
        }
    }

    private initListeners() {
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);

        const autoSelects = [10, 20, 50, 100, 500, 1000, 2000];
        autoSelects.forEach((value, index) => {
            this.autoFrame.children[index].on('click', () => {
                this.audioManager.playEffect(AUDIO_CLIP.CLICK);
                this.autoFrame.active = false;
                if (this.isTrial) {
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me35"));
                    return;
                }
                this.autoSpinLeft = value;
                this.onSpin();
            })
        })

        this.initMiniGameListeners();

        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            this.lblBalanceFreeSpin.string = Utils.formatNumber(Configs.Login.GoldCoin);
            this.infoPanel.setInfo(Configs.Login.Nickname, Configs.Login.Avatar, Configs.Login.GoldCoin);
        }, this);
    }



    private initEffects() {
        this._effectCreator = {
            [EnumEffect.BIGWIN]: {
                condition: () => {
                    // if in freespin, don't show bigwin
                    if (this._isFreeSpin) return false;
                    const payLinePrize = this.getPayLinePrize();
                    return payLinePrize >= 100 * this.playerResponse.BetValue;
                },
                effect: async () => {
                    await new Promise<void>(resolve => {
                        const payLinePrize = this.getPayLinePrize();
                        this.audioManager.playEffect(AUDIO_CLIP.BIGWIN);
                        Tween.numberTo(this.lblWinCash, payLinePrize, 7);
                        this.audioManager.playEffect(AUDIO_CLIP.COUNTER, true);
                        this.scheduleOnce(() => {
                            this.audioManager.stopEffect(AUDIO_CLIP.COUNTER);
                        }, 7.5);
                        this.playEffectAnim(this.effectBigWinPrefab).showEffect(payLinePrize, () => {
                            resolve();
                        });
                    });
                }
            },
            [EnumEffect.BONUS]: {
                condition: () => {
                    return this.playerResponse.SpinData.GameStatus === 3
                },
                effect: async () => {
                    await new Promise<void>(resolve => {
                        this.audioManager.playEffect(AUDIO_CLIP.TEXT_EFFECT);
                        this.playEffectAnim(this.effectBonusPrefab).showEffect(0, () => {
                            resolve();
                        });
                    });
                    await this.showBonusGame();
                }
            },
            [EnumEffect.CRAZY]: {
                condition: () => {
                    return this.playerResponse.SpinData.GameStatus === 4
                },
                effect: async () => {
                    await new Promise<void>(resolve => {
                        this.audioManager.playEffect(AUDIO_CLIP.TEXT_EFFECT);
                        this.modelControl.changeCrazyOutfit();
                        this.playEffectAnim(this.effectCrazyPrefab).showEffect(0, () => {
                            resolve();
                        });
                    });
                    this.slotMachine.node.active = false;
                    this.bottomHud.setState(GameStatus.CRAZY);
                    await this.showAccumulateGame();
                    this.slotMachine.node.active = true;
                    this.bottomHud.setState(GameStatus.SPIN);
                    this.modelControl.changeNormalOutfit();
                }
            },
            [EnumEffect.FREESPIN]: {
                condition: () => {
                    return this.playerResponse.SpinData.IsFreeSpin;
                },
                effect: async () => {
                    const totalFreeSpin = parseInt(this.playerResponse.SlotInfo.FreeSpins);
                    await new Promise<void>(resolve => {
                        this.modelControl.changeFreeSpinOutfit();
                        this.audioManager.playEffect(AUDIO_CLIP.TEXT_EFFECT);
                        this.playEffectAnim(this.effectFreeSpinPrefab).showEffect(totalFreeSpin, () => {
                            resolve();
                        }, false);
                    });
                    this.bottomHud.setState(GameStatus.X2);
                    this.slotMachine.changeToGoldSymbol();
                }
            },
            [EnumEffect.JACKPOT]: {
                condition: () => {
                    return this.playerResponse.SpinData.IsJackpot;
                },
                effect: async () => {
                    await new Promise<void>(resolve => {
                        const payLinePrize = this.getPayLinePrize();
                        this.audioManager.playEffect(AUDIO_CLIP.JACKPOT);
                        Tween.numberTo(this.lblWinCash, payLinePrize, 7);
                        this.audioManager.playEffect(AUDIO_CLIP.COUNTER, true);
                        this.scheduleOnce(() => {
                            this.audioManager.stopEffect(AUDIO_CLIP.COUNTER);
                        }, 7.5);
                        this.playEffectAnim(this.effectJackpotPrefab).showEffect(payLinePrize, () => {
                            resolve();
                        });
                    });
                }
            }
        }
    }


    private initHubs() {
        DQSignalRClient.getInstance().receive("JoinGame", (data: DancingPlayer) => {
            cc.log("JoinGame", data);

            this.playerResponse = data;
            this.lblSession.string = ``;
            this.lblBet.string = Utils.formatNumber(data.BetValue);
            this.lblTotalBet.string = Utils.formatNumber(data.BetValue * this.totalLine);
            this.updateEventIcon();
            this.checkFreeTicket();

            if (data.AccumulateGame && data.AccumulateGame.AccumulateItemsData) {
                this._effectCreator[EnumEffect.CRAZY].effect();
            }
            else if (data.SlotInfo.FreeSpins) {
                this.totalFreeSpin = data.SlotInfo.FreeSpins;
                this.modelControl.changeFreeSpinOutfit();
                this.playEffectAnim(this.effectFreeSpinPrefab).showEffect(this.totalFreeSpin, () => {
                    this.bottomHud.setState(GameStatus.X2);
                    this.slotMachine.changeToGoldSymbol();
                    this._isFreeSpin = true;
                    this.totalPrizeFreeSpin = data.SlotInfo.FreeSpinPrizeValue;
                    this.onSpin();
                }, false);
            }

        })

        DQSignalRClient.getInstance().receive("ResultSpin", async (data: DancingPlayer) => {
            cc.log("ResultSpin", data);
            this.playerResponse = data;
            this.lblSession.string = `#${data.SpinData.SpinID}`;

            this.slotMachine.resetAllItems();
            this.node.stopAllActions();
            this.updateGoldCoin(parseInt(data.Account.GoldBalance));
            this.bottomHud.setFreespinMultiplier(data.SpinData.FreeSpinMultiplier);
            this.audioManager.playEffect(AUDIO_CLIP.REEL_SPIN);
            await this.slotMachine.startSpin(data.SpinData.SlotsData);
            this.onSpinComplete();
        })

        DQSignalRClient.getInstance().receive("resultBonusGame", (data: BonusGameData) => {
            cc.log("ResultBonusGame", data);
        })

        DQSignalRClient.getInstance().receive("resultAccumulateGame", (data: AccumulateGameData) => {
            cc.log("ResultAccumulateGame", data);
            if (data.GameSessionID === 0) {
                this.confirmDialog.show4(
                    App.instance.getTextLang("sl52")
                    + '\n' + Utils.formatNumber(data.TotalPrizeValue)
                    + '\n' + App.instance.getTextLang("dc27"),
                    null,
                    null,
                    (isConfirm) => {

                    }
                );
            }
        })

        DQSignalRClient.getInstance().receive("UpdateJackPot", (data: number) => {
            cc.log("UpdateJackpot", data);
            Tween.numberTo(this.lblJackpot, data, 0.5);
        })

        DQSignalRClient.getInstance().receive("MessageError", (data: number) => {
            cc.log("MessageError", data);
            App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data));
        })
    }

    private initMiniGameListeners() {
        this.bonusMiniGame.listeners = {
            selectedItem: (itemID) => {
                this.invokePlayBonusWithItemID(itemID);
                // this.bonusMiniGame.unittest();
            },
            fastPlay: () => {
                this.invokeFastPlayBonus();
            }
        }

        this.crazyMiniGame.listeners = {
            selectedItem: (itemID) => {
                this.invokePlayCrazyWithItemID(itemID);
            },
            fastPlay: () => {
                this.invokeFastPlayCrazy();
            }
        }
    }

    private parsePositionData(positionData: string): { payLines: number[][], commonPayLines: number[] } {
        const payLines = positionData.split(";").map(row => row.split(",").map(Number));
        const commonPayLines = Array.from(new Set(payLines.flat()));
        return { payLines, commonPayLines };
    }


    private parsePrizeData(prizeData: string): string[] {
        return prizeData.split(";");
    }

    private getPayLinePrize() {
        return this.playerResponse.SpinData.PayLinePrizeValue;
    }

    private onSpinComplete() {
        this.audioManager.stopEffect(AUDIO_CLIP.REEL_SPIN);
        const payLinePrize: number = this.getPayLinePrize();
        let isFreeSpinCompleted = false;
        this.lblWinCash.string = '0';
        this.lblWinCashFreeSpin.string = '0';
        const { payLines, commonPayLines } = this.parsePositionData(this.playerResponse.SpinData.PositionData);
        const prizeData = this.parsePrizeData(this.playerResponse.SpinData.PrizesData);
        let delayTime = 0.5;

        if (this.totalFreeSpin > 0) {
            this.totalPrizeFreeSpin += payLinePrize;
            this.totalFreeSpin--;
            if (this.totalFreeSpin === 0) {
                isFreeSpinCompleted = true;
            }
        }
        else {
            if (this.freeTicket > 0) {
                this.freeTicket--;
            }
            if (this.autoSpinLeft > 0) {
                this.autoSpinLeft--;
            }
        }


        if (payLinePrize > 0) {
            delayTime = 1.2;
            this.showTextPayLine(payLinePrize);
            this.slotMachine.highlightItems(commonPayLines, prizeData).then();
        }

        // Add a flag to track if a new spin has started
        this._isShowingPayLines = false;

        this.node.runAction(
            cc.sequence(
                cc.delayTime(delayTime),
                cc.callFunc(() => {
                    this.showEffects(async () => {
                        this.isSpinning = false;
                        if (this.totalFreeSpin) {
                            this.onSpin();
                            return;
                        }
                        if (isFreeSpinCompleted) {
                            await this.showPopupRewardFreeSpin();
                            this._isFreeSpin = false;
                            this.totalPrizeFreeSpin = 0;
                            this.modelControl.changeNormalOutfit();
                            this.bottomHud.setState(GameStatus.SPIN);
                            this.slotMachine.changeToNormalGold();
                        }
                        if (this.autoSpinLeft > 0) {
                            this.onSpin();
                            return;
                        }
                        if (payLinePrize > 0) {
                            // Set the flag to indicate we're showing pay lines
                            this._isShowingPayLines = true;

                            for (let i = 0; i < payLines.length; i++) {
                                // Check if a new spin has started
                                if (!this._isShowingPayLines || this.isSpinning) {
                                    break;
                                }
                                await this.slotMachine.highlightItems(payLines[i], [prizeData[i]]);
                            }
                        }
                    })
                })
            )
        )
    }

    private showEffects(finishCallback: Function) {
        let awaitable: Function[] = [];

        // Check and add big win or jackpot effect (mutually exclusive)
        if (this._effectCreator[EnumEffect.JACKPOT].condition()) {
            this.autoSpinLeft = 0;
            awaitable.push(this._effectCreator[EnumEffect.JACKPOT].effect);
        } else if (this._effectCreator[EnumEffect.BIGWIN].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BIGWIN].effect);
        }

        // Check other effects
        if (this._effectCreator[EnumEffect.BONUS].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BONUS].effect);
        }

        if (this._effectCreator[EnumEffect.FREESPIN].condition()) {
            let newTotalFreeSpin = this.playerResponse.SpinData.TotalFreeSpin;
            if (!this._isFreeSpin) {
                this.totalFreeSpin = newTotalFreeSpin;
                this._isFreeSpin = true;
                awaitable.push(this._effectCreator[EnumEffect.FREESPIN].effect);
            }
            else if (newTotalFreeSpin > this.totalFreeSpin) {
                this.totalFreeSpin = newTotalFreeSpin;
            }
        }

        if (this._effectCreator[EnumEffect.CRAZY].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.CRAZY].effect);
        }
        // Execute all effects in sequence
        (async () => {
            for (const aw of awaitable) {
                await aw();
            }
        })().then(() => {
            finishCallback();
        });
    }

    private updateGoldCoin(coin: number) {
        if (this.isTrial) return;
        if (this._isFreeSpin) {
            Configs.Login.GoldCoin += this.getPayLinePrize();
        }
        else {
            Configs.Login.GoldCoin = coin;
        }
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    public showGuide() {
        this.toggleSetting.isChecked = false;
        this.popupGuide.show();
        this.bottomHud.activeBackButton();
        this.mainGame.active = false;
    }

    // Show history popup
    public showHistory() {
        this.toggleSetting.isChecked = false;
        this.popupHistory.show();
        this.bottomHud.activeBackButton();
        this.mainGame.active = false;
    }

    // Show jackpot history popup
    public showWinners() {
        this.toggleSetting.isChecked = false;
        this.popupWinners.show();
        this.bottomHud.activeBackButton();
        this.mainGame.active = false;
    }

    private playEffectAnim(prefab: cc.Prefab): EffectDialog {
        const effect = cc.instantiate(prefab);
        this.node.parent.addChild(effect);
        const effectComp = effect.getComponent(EffectDialog);
        return effectComp;
    }

    public backFromPopup() {
        const gameStatus = this.playerResponse.SlotInfo.GameStatus;
        this.bottomHud.setState(gameStatus);
        this.popupHistory.node.active = false;
        this.popupWinners.node.active = false;
        this.popupGuide.node.active = false;
        switch (gameStatus) {
            case GameStatus.SPIN:
                this.mainGame.active = true;
                break;
            case GameStatus.BONUS:
                break;
            case GameStatus.X2:
                this.mainGame.active = true;
                break;
            case GameStatus.CRAZY:
                this.crazyMiniGame.node.active = true;
                break;
        }
    }

    private async showAccumulateGame() {
        await new Promise<void>(resolve => {
            this.crazyMiniGame.show(this.playerResponse.AccumulateGame.AccumulateItemsData, () => {
                resolve();
            });
        })
    }

    private async showBonusGame(): Promise<void> {
        await new Promise<void>(resolve => {
            this.bonusMiniGame.show(this.playerResponse.BonusGame, () => {
                resolve();
            });
        });

    }

    private onSpin() {
        this.isSpinning = true;
        if (this.isTrial) {
            this.lblSession.string = '';
            this.slotMachine.resetAllItems();
            this.node.stopAllActions();
            let rIdx = Utils.randomRangeInt(0, DQTrial.Result.length);
            this.playerResponse.SpinData = DQTrial.Result[rIdx];
            this.trialBalance -= this.playerResponse.BetValue * this.totalLine; // Deduct bet amount
            this.audioManager.playEffect(AUDIO_CLIP.REEL_SPIN);
            this.slotMachine.startSpin(this.playerResponse.SpinData.SlotsData).then(() => {
                this.onSpinComplete();
                this.trialBalance += this.getPayLinePrize(); // Add winnings
            });
            return;
        }
        if (this.freeTicket > 0 && this.totalFreeSpin <= 0) {
            this._spinWithTicket();
        }
        else {
            this._spin();
        }
    }

    private _spin() {
        const payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "RoomID": this.roomID,
            "TotalLine": this.totalLine,
        }
        DQSignalRClient.getInstance().send(
            'Spin', [payload],
            (data) => {
                if (data.c < 0) {
                    this.isSpinning = false;
                    this.autoSpinLeft = 0;
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }
    private _spinWithTicket() {
        const payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "RoomID": this.roomID,
            "TotalLine": this.totalLine,
        }
        DQSignalRClient.getInstance().send(
            'SpinForTicket', [payload],
            (data) => {
                if (data.c < 0) {
                    this.isSpinning = false;
                    this.autoSpinLeft = 0;
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }

    private stopAutoSpin() {
        this.autoSpinLeft = 0;
    }

    private onClickTurbo(target: cc.Toggle, eventData: string) {
        DQAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK);
        this.slotMachine.setTurbo(target.isChecked);
    }

    private actBetChange(event: cc.Event, data: string) {
        DQAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK_BET);
        if (this.isTrial) {
            App.instance.ShowAlertDialog(App.instance.getTextLang("me35"));
            return;
        }
        const direction = parseInt(data);
        let roomID = this._roomID;
        roomID += direction;
        if (roomID < 1) roomID = 3;
        if (roomID > 3) roomID = 1;

        this.roomID = roomID;
    }

    private actLineUp() {
        DQAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK_LINE);
        if (this.isTrial) {
            App.instance.ShowAlertDialog(App.instance.getTextLang("me35"));
            return;
        }
        this._totalLine++;
        if (this._totalLine > 25) {
            this._totalLine = 1; // 🔁 Tuần hoàn về 1
        }
        this.totalLine = this._totalLine;
        this.lblTotalBet.string = Utils.formatNumber(this._totalLine * this.playerResponse.BetValue);
        this.slotMachine.payLines.showLinesSelected(this._totalLine);
    }

    private actLineDown() {
        DQAudioManager.Instance.playEffect(AUDIO_CLIP.CLICK_LINE);
        if (this.isTrial) {
            App.instance.ShowAlertDialog(App.instance.getTextLang("me35"));
            return;
        }
        this._totalLine--;
        if (this._totalLine < 1) {
            this._totalLine = 25; // 🔁 Tuần hoàn về 25
        }
        this.totalLine = this._totalLine;
        this.lblTotalBet.string = Utils.formatNumber(this._totalLine * this.playerResponse.BetValue);
        this.slotMachine.payLines.showLinesSelected(this._totalLine);
    }

    private onTouchStart() {
        if (this.isSpinning) {
            return;
        }
        this.isHolding = false;

        this.holdTimeout = setTimeout(() => {
            this.isHolding = true;
            this.autoFrame.active = true;
        }, 800);
    }

    private onTouchEnd() {
        if (this.isSpinning) {
            return;
        }
        clearTimeout(this.holdTimeout);

        if (!this.isHolding) {
            this.onSpin(); // gọi nếu nhấn thả trước 2s
        }
    }

    private onTouchCancel() {
        if (this.isSpinning) {
            return;
        }
        clearTimeout(this.holdTimeout);
    }

    private backToLobby() {
        DQSignalRClient.getInstance().dontReceive();
        App.instance.bigSlotGame[Configs.GameId88.Dancing] = null;
        App.instance.gotoLobby();
    }

    actHidden() {
        this.toggleSetting.isChecked = false;
        if (this.autoSpinLeft > 0) {
            App.instance.showConfirmDialog(
                App.instance.getTextLang("sl74"),
                () => {
                    // Pause all audio before hiding the game
                    this.audioManager.pauseAllAudio();
                    this.node.parent.setPosition(cc.v3(10000, 0, 0));
                },
                true
            );
        }
        else {
            App.instance.ShowAlertDialog(App.instance.getTextLang("sl90"));
        }
    }

    actSound() {
        this.audioManager.muteEffect(!this.audioManager.isMuteEffect);
    }

    actMusic() {
        this.audioManager.muteMusic(!this.audioManager.isMuteMusic);
    }

    updateEventIcon() {
        this.getRoomMultiplier(this.roomID).then((multiplier) => {
            this.eventIcon.node.active = multiplier > 0;
            this.eventIcon.setIcon(multiplier, 0);
        });
    }

    checkFreeTicket() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountTicket'], { CurrencyID: Configs.Login.CurrencyID, GameID: Configs.GameId88.Dancing }, (status, res) => {
            if (status === 200) {
                const data = res.d.filter(item => item.roomID === this.roomID);
                const count = data.reduce((sum, item) => sum + item.balance, 0);
                this.freeTicket = count;
            }
        })
    }

    private async getRoomMultiplier(roomID: number): Promise<number> {
        return new Promise((resolve) => {
            Http.get(
                Configs.App.DOMAIN_CONFIG['GetListJackpot'],
                { CurrencyID: Configs.Login.CurrencyID },
                (status, res) => {
                    if (status !== 200 || !res?.d) {
                        resolve(0);
                        return;
                    }

                    const item = res.d.find(item =>
                        item.gameID === Configs.GameId88.Dancing &&
                        item.roomID === roomID &&
                        item.nextJackpot === 0
                    );

                    resolve(item ? item.multiplier : 0);
                }
            );
        });
    }

    invokePlayCrazyWithItemID(itemID: number) {
        cc.log("ItemID: ", itemID);
        DQSignalRClient.getInstance().send(
            'PlayCrazyBonus', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.playerResponse.RoomID,
                "ItemID": itemID,
            }],
            (data) => {
                // check c
                if (data.c < 0) {
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }

    invokePlayBonusWithItemID(itemID: number) {
        cc.log("ItemID: ", itemID);
        DQSignalRClient.getInstance().send(
            'PlayBonusGame', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.playerResponse.RoomID,
                "ItemID": itemID,
            }],
            (data) => {
                // check c
                if (data.c < 0) {
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }

    invokeFastPlayBonus() {
        DQSignalRClient.getInstance().send(
            'PlayBonusGameAll', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.playerResponse.RoomID,
            }],
            (data) => {
                // check c
                if (data.c < 0) {
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }

    invokeFastPlayCrazy() {
        DQSignalRClient.getInstance().send(
            'PlayCrazyBonusAll', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.playerResponse.RoomID,
            }],
            (data) => {
                // check c
                if (data.c < 0) {
                    App.instance.ShowAlertDialog(App.instance.getTextLang("me" + data.c));
                }
            }
        )
    }

    private async showPopupRewardFreeSpin(): Promise<void> {
        return new Promise<void>((resolve) => {
            this.confirmDialog.show4(
                App.instance.getTextLang("sl52")
                + '\n' + Utils.formatNumber(this.totalPrizeFreeSpin)
                + '\n' + App.instance.getTextLang("sl89"),
                null,
                null,
                (isConfirm) => {
                    resolve();
                }
            );
        });
    }

    private showTextPayLine(prize: number) {
        if (this._effectCreator[EnumEffect.BIGWIN].condition() || this._effectCreator[EnumEffect.JACKPOT].condition()) return;
        this.audioManager.playEffect(AUDIO_CLIP.WIN);
        this.lblPayline.string = '';
        Tween.numberTo(this.lblPayline, prize, 0.7);
        Tween.numberTo(this.lblWinCash, prize, 0.7);
        if (this._isFreeSpin) {
            Tween.numberTo(this.lblWinCashFreeSpin, prize, 0.7);
        }
        this.lblPayline.node.parent.active = true;
        this.scheduleOnce(() => {
            this.lblPayline.node.parent.active = false;
        }, 1.5);

        this.paylineBox.spriteFrame = this._isFreeSpin ? this.paylineBox1 : this.paylineBox2;
    }

    onToggleTrial() {
        if (this.isSpinning) return;
        this.toggleSetting.isChecked = false;
        this.isTrial = this.toggleTrial.isChecked;
        if (this.isTrial) {
            this.lblBet.string = '100';
            this.lblTotalLine.string = '25';
            this.lblTotalBet.string = '2.500'; // 100 * 25 lines
            this.trialBalance = 10_000_000;
        } else {
            if (this.playerResponse) {
                this.lblBet.string = Utils.formatNumber(this.playerResponse.BetValue);
                this.lblTotalLine.string = this.totalLine.toString();
                this.lblTotalBet.string = Utils.formatNumber(this.playerResponse.BetValue * this.totalLine);
            }
        }
        this.lblTrialBalance.node.parent.parent.active = this.isTrial;
    }

}

interface DancingPlayer extends SlotPlayerResponse {
    AccumulateGame: AccumulateGameData;
}
