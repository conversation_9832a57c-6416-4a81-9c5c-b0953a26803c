import Utils from "../../../Lobby/LobbyScript/Script/common/Utils";
import { GameStatus } from "../DQController";

const {ccclass, property, menu} = cc._decorator;

@ccclass
@menu("DancingQueen/BottumHudController")
export class BottumHudController extends cc.Component {
    @property(cc.Node) 
    spinBar: cc.Node = null;

    @property(cc.Node)
    normalSpinNode: cc.Node = null;

    @property(cc.Node)
    x2SpinNode : cc.Node = null;

    @property(cc.Node) 
    crazyBar: cc.Node = null;

    @property(cc.Node) 
    btnBack: cc.Node = null;

    public activeBackButton(){
        this.node.children.forEach(child => {
            child.active = false;
        })
        this.btnBack.active = true;
    }

    public setState(state: GameStatus){
        this.btnBack.active = false;
        switch (state) {
            case GameStatus.SPIN:
                this.spinBar.active = true;
                this.normalSpinNode.active = true;
                this.x2SpinNode.active = false;
                this.crazyBar.active = false;
                break;
            case GameStatus.BONUS:
                break;
            case GameStatus.CRAZY:
                this.spinBar.active = false;
                this.normalSpinNode.active = false;
                this.x2SpinNode.active = false;
                this.crazyBar.active = true;
                break;
            case GameStatus.X2:
                this.spinBar.active = true;
                this.normalSpinNode.active = false;
                this.x2SpinNode.active = true;
                this.crazyBar.active = false;
                break;
        }
    }
}
