import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";

const {ccclass, property} = cc._decorator;

@ccclass
export class EffectDialog extends Dialog {
    @property
    private duration: number = 1;

    @property(cc.Label)
    private prizeValue: cc.Label = null;

    onDismissed: () => void;

    showEffect(value: number, cb: () => void, isLabelAnim: boolean = true) {
        super.show();
        if (this.prizeValue) {
            this.prizeValue.string = "0";
            const halfDuration = this.duration / 2;
            Tween.numberTo(this.prizeValue, value, isLabelAnim ? halfDuration : 0);
        }
        this.scheduleOnce(() => {
            this.dismiss();
            cb(); // ✅ GỌI callback sau khi dismiss
        }, this.duration);
    }

    _onShowed() {
        super._onShowed();
        this.node.setPosition(cc.v3(0, 0, 0));
    }

    _onDismissed() {
        super._onDismissed();
        // destroy node
        this.node.removeFromParent();
        this.node.destroy();
    }
    
}
