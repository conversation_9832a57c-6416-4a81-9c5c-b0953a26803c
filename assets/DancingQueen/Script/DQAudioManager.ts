import Configs from "../../Lobby/MoveScript/Configs";

const {ccclass, property} = cc._decorator;

export enum AUDIO_CLIP {
    BG1 = 'Music_Deep-House-Type-Beat',
    BG2 = 'Music_Type-Beat-Rider',
    BG3 = 'Music_Deep-House-Dance',
    CLICK = 'click',
    CLICK_BET = 'button_bet',
    CLICK_LINE = 'button_line',
    REEL_SPIN = 'spin',
    END_REEL_SPIN = 'SpinColumnStop',
    BIGWIN = 'thanglon',
    WIN = 'thangvua',
    JACKPOT = 'jackpot',
    EXPAND_WILD = 'expand_wild',
    TEXT_EFFECT = 'dancing_ping_bonus',
    COUNTER = 'counter',
    BUBBLE = 'bong_no',
    FINISH_BONUS = 'finish_bonus_dancing'
}

const Key = {
    SOUND: `${Configs.GameId88.Dancing}_sound_enabled`,
    MUSIC: `${Configs.GameId88.Dancing}_music_enabled`
}

@ccclass
export default class DQAudioManager extends cc.Component {
    public static Instance: DQAudioManager = null;

    @property(cc.AudioSource)
    bgMusic: cc.AudioSource = null;

    private _audioClipSet: { [key: string]: cc.AudioClip } = {};
    private _isMuteEffect = false;
    private _isMuteMusic = false;
    private _effectAudioIds: { [key: string]: number[] } = {};

    // Pause/Resume state tracking
    private _isPaused = false;
    private _pausedMusicState = false;
    private _pausedEffectState = false;

    protected onLoad(): void {
        DQAudioManager.Instance = this;
        this._loadSettings();

        cc.assetManager.getBundle("DancingQueen").loadDir("Asset/Sound", cc.AudioClip, (err, audioClips: cc.AudioClip[]) => {
            if (err) {
                cc.error(err);
                return;
            }
            audioClips.forEach((audioClip: cc.AudioClip) => {
                this._audioClipSet[audioClip.name] = audioClip;
            });

        });
    }

    private _loadSettings(): void {
        const musicValue = cc.sys.localStorage.getItem(Key.MUSIC);
        const soundValue = cc.sys.localStorage.getItem(Key.SOUND);

        const musicEnabled = musicValue !== null ? musicValue === "true" : true;
        const soundEnabled = soundValue !== null ? soundValue === "true" : true;

        this._isMuteMusic = !musicEnabled;
        this._isMuteEffect = !soundEnabled;

        this.bgMusic.mute = this._isMuteMusic;
        cc.audioEngine.setEffectsVolume(this._isMuteEffect ? 0 : 1);
    }

    public get isMuteMusic() {
        return this._isMuteMusic;
    }

    public get isMuteEffect() {
        return this._isMuteEffect;
    }


    public playEffect(name: AUDIO_CLIP, loop = false) {
        if (this._isMuteEffect) return;
        const audioClip = this._audioClipSet[name];
        if (audioClip) {
            const audioId = cc.audioEngine.play(audioClip, loop, 1);
            if (!this._effectAudioIds[name]) {
                this._effectAudioIds[name] = [];
            }
            this._effectAudioIds[name].push(audioId);
        }
    }

    public stopEffect(name: AUDIO_CLIP) {
        const ids = this._effectAudioIds[name];
        if (ids && ids.length > 0) {
            for (const id of ids) {
                cc.audioEngine.stop(id);
            }
            this._effectAudioIds[name] = [];
        }
    }

    public stopAllEffect() {
        cc.audioEngine.stopAllEffects();
    }

    public playMusic(name: AUDIO_CLIP, loop = true) {
        if (this._isMuteMusic) return;
        const audioClip = this._audioClipSet[name];
        if (audioClip) {
            this.bgMusic.clip = audioClip;
            this.bgMusic.loop = loop;
            this.bgMusic.play();
        }
    }

    public stopMusic() {
        if (this._isMuteMusic) return;
        this.bgMusic.stop();
    }

    public muteEffect(isMute: boolean) {
        this._isMuteEffect = isMute;
        cc.audioEngine.setEffectsVolume(isMute ? 0 : 1);
        cc.sys.localStorage.setItem(Key.SOUND, !isMute);
    }

    public muteMusic(isMute: boolean) {
        this._isMuteMusic = isMute;
        this.bgMusic.mute = isMute;
        cc.sys.localStorage.setItem(Key.MUSIC, !isMute);
    }

    public onClick() {
        this.playEffect(AUDIO_CLIP.CLICK);
    }

    /**
     * Pause all game audio (music and effects) while preserving user settings
     */
    public pauseAllAudio() {
        if (this._isPaused) return;

        this._isPaused = true;

        // Store current states
        this._pausedMusicState = this._isMuteMusic;
        this._pausedEffectState = this._isMuteEffect;

        // Pause music
        if (this.bgMusic.isPlaying) {
            this.bgMusic.pause();
        }

        // Stop all effects
        this.stopAllEffect();

        // Temporarily mute both music and effects
        this._isMuteMusic = true;
        this._isMuteEffect = true;
        this.bgMusic.mute = true;
        cc.audioEngine.setEffectsVolume(0);
    }

    /**
     * Resume all game audio with original user settings
     */
    public resumeAllAudio() {
        if (!this._isPaused) return;

        this._isPaused = false;

        // Restore original states
        this._isMuteMusic = this._pausedMusicState;
        this._isMuteEffect = this._pausedEffectState;

        // Apply restored settings
        this.bgMusic.mute = this._isMuteMusic;
        cc.audioEngine.setEffectsVolume(this._isMuteEffect ? 0 : 1);

        // Resume music if it was not muted originally
        if (!this._isMuteMusic && this.bgMusic.clip) {
            this.bgMusic.resume();
        }
    }

    /**
     * Check if audio is currently paused
     */
    public get isPaused(): boolean {
        return this._isPaused;
    }


}
