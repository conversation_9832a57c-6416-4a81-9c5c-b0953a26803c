import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import { BonusListener } from "./CaiShen.Controller";
import Doi<PERSON>eo from "./DoiHeo";
import Configs from "../../Lobby/MoveScript/Configs";
import CaiShenSignalRClient from "../../Lobby/LobbyScript/Script/networks/CaiShenSignalRClient";
import CaiShenController from "./CaiShen.Controller";

const { ccclass, property, menu } = cc._decorator;

export interface ItemData {
    ItemID: number;
    PrizeID: number;
    PrizeValue: number;
    isOpened: boolean;
}

@ccclass
@menu("CaiShen/PopupBonus")
export default class CaiShenPopupBonus extends cc.Component {
    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property(cc.Node)
    bar: cc.Node = null;

    @property(cc.Node)
    items: cc.Node = null;

    @property(DoiHeo)
    doiHeo: DoiHeo = null;

    @property(cc.Label)
    timerLabel: cc.Label = null;

    @property(cc.Sprite)
    timerProgress: cc.Sprite = null;

    @property([cc.Label])
    pigPrizes: cc.Label[] = [];

    @property(cc.Label)
    lblMyIndex: cc.Label = null;

    @property(cc.Label)
    lblHeSoNhan: cc.Label = null;

    public listener: BonusListener = null;
    public betValue: number;

    private countDown: number = 0;
    private timer: number = null;
    private bonusData: ItemData[] = [];

    public onFinishedProcess: Function = null;
    private latestBonusResponse: any = null;

    //private options: number[] = [];
    private _myPig: ItemData;
    private _itemRandom: ItemData[] = [];
    private sceneIndex: number = 0;

    set myPig(v: ItemData) {

        this._myPig = v;
        this.lblMyIndex.string = v.ItemID.toString();
    }

    get myPig() {
        return this._myPig;
    }

    protected onLoad() {
        this.pageView.getPages().map(m => m.active = true);
        for (let i = 0; i < this.items.childrenCount; i++) {
            let item = this.items.children[i];
            item["btn"] = item.getComponent(cc.Button);
            item["label"] = item.getComponentInChildren(cc.Label);
            item.on('click', () => {
                this.bonusData[i].isOpened = true;
                if (this.timer !== null) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
                this.myPig = this.bonusData[i];

                CaiShenSignalRClient.getInstance().send(
                    'PlayBonusGame', [{
                        "CurrencyID": Configs.Login.CurrencyID,
                        "RoomID": CaiShenController.getInstance().roomID,
                        "ItemID": this.bonusData[i].ItemID,
                        "IsFinish": 0,
                    }],
                    (data) => {
                        if (data.c < 0) {
                            App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                        }
                    }
                );


                this.sceneIndex = 1;
                this.pageView.scrollToPage(1, 0.3);
                this.showPage2();

            })
        }
    }

    showBonus(bonus: string, betValue: number) {
        this.node.active = true;
        this.bar.active = false;
        //this.loadBonusGameData(bonus);

        // this.options = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12,
        //     13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23];
        this.bonusData = [];
        bonus.split(";").map(item => {
            const [ItemID, PrizeID, PrizeValue] = item.split(",").map(Number);
            this.bonusData.push({ ItemID, PrizeID, PrizeValue, isOpened: false });
        });

        this.pigPrizes.forEach((v: cc.Label, i: number) => {
            v.string = Utils.formatNumber(this.bonusData[i].PrizeValue);
        })

        this.betValue = betValue;
        this.lblHeSoNhan.string = `x${this.bonusData[0].PrizeValue / this.betValue}`;
        this.lblMyIndex.string = '';

        this.startCountDown();
    }

    public handleRespone(data) {
        this.latestBonusResponse = data;
        if (data.PrizeValue > 0) {
            this.pageView.scrollToPage(4, 0.3);
            let page4 = this.pageView.getPages()[4];
            page4.getChildByName('result').getComponent(cc.Label).string = Utils.formatNumber(data.PrizeValue);
            page4.getChildByName('heso').getComponent(cc.Label).string = `x1`;
            page4.getChildByName('total').getComponent(cc.Label).string = Utils.formatNumber(data.PrizeValue);
            page4.getChildByName('btnClaim').on("click", () => {
                this.onFinishedProcess();
            })
        }
        if(data.Items.length > 0) {
            this.pageView.scrollToPage(1, 0.3);
        }

    }

    private generateIndex(): ItemData[] {
        let rand: ItemData[];
        do {
            rand = this.bonusData
                .filter(i => !i.isOpened)
                .sort(() => Math.random() - 0.5)
                .slice(0, 5);
        } while (rand.includes(this.myPig));
        return rand
    }

    showPage2() {
        let page2 = this.pageView.getPages()[1];
        let pigs = page2.getChildByName('pigs');
        let buttons = page2.getChildByName('buttons');
        this._itemRandom = this.generateIndex();
        let items = [...this._itemRandom, this.myPig];
        for (let i = 0; i < 6; i++) {
            let pig = pigs.children[i];
            let btn = buttons.children[i];

            btn["itemData"] = items[i];
            let anim = pig.getComponent(cc.Animation);
            let lblPrize = pig.getChildByName("prize").getComponent(cc.Label);
            let lblIndex = pig.getChildByName("label").getComponent(cc.Label);

            lblIndex.string = items[i].ItemID.toString();
            anim.play("pig_close");
            lblPrize.string = '';
            btn.active = true;
            if (items[i] === this.myPig) {
                btn.on("click", () => {
                    btn.active = false;
                    anim.play("pig_open");
                    Tween.numberTo(lblPrize, items[i].PrizeValue, 0.5);
                    this.scheduleOnce(() => {
                        CaiShenSignalRClient.getInstance().send(
                            'PlayBonusGame', [{
                                "CurrencyID": Configs.Login.CurrencyID,
                                "RoomID": CaiShenController.getInstance().roomID,
                                "ItemID": this.bonusData[i].ItemID,
                                "IsFinish": 1,
                            }],
                            (data) => {
                                if (data.c < 0) {
                                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                                }
                            }
                        );
                    }, 1.5)
                })
            } else {
                btn.on("click", () => {
                    this.sceneIndex = 2;
                    this.pageView.scrollToPage(2, 0.3);
                    this.showPage3(this.myPig, btn["itemData"]);
                })
            }
        }
    }

    showPage3(from: ItemData, to: ItemData) {
        let page3 = this.pageView.getPages()[2];
        page3.getChildByName("from").getComponent(cc.Label).string = from.ItemID.toString();
        page3.getChildByName("to").getComponent(cc.Label).string = to.ItemID.toString();
        page3.getChildByName("btnAgree").on("click", () => {
            this.myPig = to;
            let rand = this.generateIndex();
            CaiShenSignalRClient.getInstance().send(
                'PlayBonusGame', [{
                    "CurrencyID": Configs.Login.CurrencyID,
                    "RoomID": CaiShenController.getInstance().roomID,
                    "ItemID": to.ItemID,
                    "ItemRandom": rand.map(i => i.ItemID).join(","),
                    "IsFinish": 0,
                }],
                (data) => {
                    if (data.c < 0) {
                        App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                    }
                }
            );
        })
        page3.getChildByName("btnCancel").on("click", () => {
            this.pageView.scrollToPage(1, 0.3);
        })
    }


    dismiss() {
        this.bar.active = true;
        this.node.active = false;
        this.sceneIndex = 0;
        this.pageView.scrollToPage(0, 0.3);
        this.bonusData = [];
        this._itemRandom = [];
        this.latestBonusResponse = null;
        //clear timer
        if (this.timer !== null) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }
























































    // protected onLoad() {
    //     this.selectItemPage.children.forEach((piggy: cc.Node, index: number) => {
    //         piggy.getComponentInChildren(cc.Label).string = `${index + 1}`;
    //         piggy.on('click', () => {
    //             // Heo đang chọn save ở đây
    //             this.itemBonus = [this.bonusGameData.get(index + 1)];
    //             this.onSelectPiggyBank();
    //         })
    //     });
    // }

    // public loadBonusGameData(bonusData: string) {
    //     bonusData.split(";").map(item => {
    //         const [ItemID, PrizeID, PrizeValue] = item.split(",").map(Number);
    //         this.bonusGameData.set(ItemID, {
    //             ItemID,
    //             PrizeID,
    //             PrizeValue,
    //         })
    //     });


    //     /**
    //      * -    Step1: Hệ thống lấy ngẫu nhiên 24 giá trị trong 40 giá trị sẵn có sau khi kiểm tra điều kiện sinh giá trị.
    //      * -	Step2: Hiển thị 24 giá trị đã chọn và lợn đất.
    //      */
    //     this.pigPrizes.forEach((v: cc.Label, i: number) => {
    //         v.string = Utils.formatNumber(this.bonusGameData.get(i + 1).PrizeValue);
    //     })
    // }


    private startCountDown() {
        clearInterval(this.timer);

        let totalTime: number;
        switch (this.sceneIndex) {
            case 0:
                totalTime = 150;
                break;
            case 1:
                totalTime = 100;
                break;
            case 5:
                totalTime = 150;
                break;
            default:
                this.setTimeLabel(0);
                return;
        }

        this.countDown = totalTime;
        this.setTimeLabel(this.countDown);

        let elapsed = 0;

        this.timer = setInterval(() => {
            elapsed += 100;
            this.countDown = Math.max(0, totalTime - elapsed / 1000);
            this.setTimeLabel(Math.ceil(this.countDown));

            this.timerProgress.fillRange = this.countDown / totalTime;

            if (this.countDown <= 0) {
                clearInterval(this.timer);
                this.autoSelect();
            }
        }, 100);
    }

    private setTimeLabel(t: number) {
        if (t <= 0) {
            this.timerLabel.node.parent.active = false;
        } else {
            this.timerLabel.node.parent.active = true;
            this.timerLabel.string = `${t}s`;
        }
    }


    private autoSelect() {
        switch (this.sceneIndex) {
            case 0:
                // this.itemBonus = [this.bonusGameData.get(1)];
                this.onSelectPiggyBank();
                break;
            case 2:
                this.onClickQuickPlay();
                break;
            case 5:
                this.onClickQuickPlay();
                break;
        }
    }


    private onSelectPiggyBank() {
        // this.listener.select && this.listener.select(this,
        //     this.itemBonus[0].ItemID,
        //     this.itemBonus.slice(1).map(i => i.ItemID));

        // if (test === "choose") {
        //     setTimeout(() => {
        //         cc.log("itemID: ", this.itemBonus[0].ItemID);
        //         cc.log("itemBonus: ", this.itemBonus.slice(1).map(i => i.ItemID));
        //         cc.log("false");
        //         this.onResult({
        //             ItemID: this.itemBonus[0].ItemID,
        //             Step: 1,
        //             SpinID: **********,
        //             PrizeValue: 0,
        //             BonusItemData: "",
        //             SourceID: 0,
        //             MerchantID: 0,
        //             RoomID: 1,
        //             ResponseStatus: 0,
        //             Items: [],
        //             ItemRandom: "",
        //             AverageValue: 0
        //         })
        //     }, 100)
        // } else if (test === "change") {
        //     setTimeout(() => {
        //         cc.log("itemID: ", this.itemBonus[0].ItemID);
        //         cc.log("itemBonus: ", this.itemBonus.slice(1).map(i => i.ItemID));
        //         cc.log("false");
        //         this.onResult({
        //             ItemID: this.itemBonus[0].ItemID,
        //             Step: 1,
        //             SpinID: **********,
        //             PrizeValue: 0,
        //             BonusItemData: `6,103,3000;9,104,4000;10,103,3000;16,106,6000;23,120,20000`,
        //             SourceID: 0,
        //             MerchantID: 0,
        //             RoomID: 1,
        //             ResponseStatus: 0,
        //             Items: this.itemBonus.slice(1),
        //             ItemRandom: "9,6,16,23,10",
        //             AverageValue: 5473
        //         })
        //     }, 100)
        // } else if (test === "finish") {
        //     setTimeout(() => {
        //         cc.log("itemID: ", this.itemBonus[0].ItemID);
        //         cc.log("itemBonus: ", this.itemBonus.slice(1).map(i => i.ItemID));
        //         cc.log("true");
        //         this.onResult({
        //             ItemID: this.itemBonus[0].ItemID,
        //             Step: 1,
        //             SpinID: **********,
        //             PrizeValue: 6900000,
        //             BonusItemData: "6,103,3000;9,104,4000;10,103,3000;16,106,6000;23,120,20000",
        //             SourceID: 0,
        //             MerchantID: 0,
        //             RoomID: 1,
        //             ResponseStatus: 0,
        //             Items: this.itemBonus.slice(1),
        //             ItemRandom: "9,6,16,23,10",
        //             AverageValue: 0
        //         })
        //     }, 100)
        //}

    }

    private onClickQuickPlay() {
        //this.listener.quickPlay && this.listener.quickPlay();
    }


    // public onResult(data: any) {
    //     let pigSelectors = this.pages[2].children;
    //     this.latestBonusResponse = data;

    //     if (data.PrizeValue > 0) {
    //         this.showLayer(6);
    //         return;
    //     }

    //     if (data.Items.length > 0) {
    //         this.itemBonus = [this.bonusGameData.get(data.ItemID), ...data.Items];
    //     } else {
    //         this.itemBonus = this.generateItemBonus(data.ItemID);
    //     }

    //     for (let i = 0; i < this.itemBonus.length; i++) {
    //         let pigNode = pigSelectors[i];
    //         let itemData = this.itemBonus[i];

    //         // Assign ItemID to the "index" label
    //         pigNode.getChildByName("index").getComponentInChildren(cc.Label).string = itemData.ItemID.toString();

    //         let canChange = data.ItemID !== itemData.ItemID; // Check if it's a different ItemID

    //         if (canChange) {
    //             pigNode.getChildByName("button").getComponentInChildren(cc.Label).string = App.instance.getTextLang("gof13");
    //             pigNode.getChildByName("button").on("click", () => {
    //                 this.newSelectedPiggy = itemData.ItemID; // Assign selected ItemID
    //                 this.showLayer(3);
    //             });
    //         } else {
    //             pigNode.getChildByName("button").getComponentInChildren(cc.Label).string = App.instance.getTextLang("gof24");
    //             pigNode.getChildByName("button").on("click", () => {
    //                 this.onClickQuickPlay();
    //                 // Handle when the selected piggy is the original one (no action specified in the request)
    //             });
    //         }
    //     }

    //     if (data.Items.length > 0) {
    //         this.showLayer(4);
    //     } else {
    //         this.showLayer(2);
    //     }
    // }

    // private onChangePage(target: cc.Button, pageIndex: string) {
    //     this.showLayer(parseInt(pageIndex));
    // }

    private hideBonus() {
        this.onFinishedProcess();
    }
}
