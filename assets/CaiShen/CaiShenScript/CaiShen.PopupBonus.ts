import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Configs from "../../Lobby/MoveScript/Configs";
import CaiShenSignalRClient from "../../Lobby/LobbyScript/Script/networks/CaiShenSignalRClient";
import CaiShenController from "./CaiShen.Controller";
import PiggyItem, { Mode } from "./PiggyItem";
import { DecideControl } from "./Bonus/DecideControl";
import SwapPigControl from "./Bonus/SwapPigControl";
import EndControl from "./Bonus/EndControl";
import FinalDecisionControl from "./Bonus/FinalDecisionControl";

const { ccclass, property, menu } = cc._decorator;

enum BonusState {
    INIT, CHOOSE_PIG, DECIDE_ACTION, SWAP_PIG, REVEAL_PIGS, FINAL_DECISION, END
}

export interface ItemData {
    ItemID: number;
    PrizeID: number;
    PrizeValue: number;
}

@ccclass
@menu("CaiShen/PopupBonus")
export default class CaiShenPopupBonus extends cc.Component {
    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property(cc.Node) leftColumn: cc.Node = null;
    @property(cc.Node) rightColumn: cc.Node = null;

    @property(cc.Node) choosePigUI: cc.Node = null;
    @property(DecideControl) decideControl: DecideControl = null;
    @property(SwapPigControl) swapPigControl: SwapPigControl = null;
    @property(FinalDecisionControl) finalDecisionControl: FinalDecisionControl = null;
    @property(EndControl) endControl: EndControl = null;

    @property(cc.Label)
    timerLabel: cc.Label = null;

    @property(cc.Sprite)
    timerProgress: cc.Sprite = null;

    @property(cc.Label)
    lblMyIndex: cc.Label = null;

    @property(cc.Label)
    lblHeSoNhan: cc.Label = null;

    private state: BonusState;
    private roundCount = 0;
    private gameStateMachine: GameStateMachine;
    private bonusData: ItemData[] = [];
    private myPig: ItemData;
    private onFinished: Function = null;
    private isBroken: number[] = [];
    private preparedItems: ItemData[] = [];
    private prizeNodes: cc.Node[] = [];
    public heso = 0;


    /**
     *  1 is finish, else 0
     */
    invokePlayBonusGame(itemID?: number, isFinish?: number, itemRandom?: number[]) {
        let payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "RoomID": CaiShenController.getInstance().roomID,
        };
        if (itemID !== undefined) {
            payload['itemID'] = itemID;
        }
        if (itemRandom !== undefined) {
            payload['itemRandom'] = itemRandom.join(",");
        }
        if (isFinish !== undefined) {
            payload['isFinish'] = isFinish;
        }

        CaiShenSignalRClient.getInstance().send(
            'PlayBonusGame', [payload],
            (data) => {
                if (data.c < 0) {
                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                }
            }
        );
    }

    protected onLoad() {
        this.gameStateMachine = new GameStateMachine((state) => {
            this.onStateChange(state);
        });
        CaiShenSignalRClient.getInstance().receive('resultBonusGame', (data) => {
            cc.log("resultBonusGame", data.BonusGame);
            if (data.BonusGame.PrizeValue > 0) {
                this.endControl.setInfo(data.BonusGame.PrizeValue, 1);
            }
            if(data.BonusGame.AverageValue > 0) {
                this.finalDecisionControl.setInfo(data.BonusGame.AverageValue, data.BonusGame.PrizeValue, 1);
            }
        })
        this.leftColumn.children.forEach(node => {
            this.prizeNodes.push(node);
        });
        this.rightColumn.children.forEach(node => {
            this.prizeNodes.push(node);
        });
        this.pageView.content.children.forEach(node => {
            node.active = true;
        })
    }

    showBonus(bonus: string, onFinished: Function) {
        this.onFinished = onFinished;
        this.node.active = true;

        this.gameStateMachine.changeState(BonusState.CHOOSE_PIG);

        this.bonusData = bonus.split(";").map(item => {
            const [ItemID, PrizeID, PrizeValue] = item.split(",").map(Number);
            return { ItemID, PrizeID, PrizeValue };
        });

        for (let i = 0; i < this.bonusData.length; i++) {
            this.prizeNodes[i].getComponent(cc.Label).string = Utils.formatNumber(this.bonusData[i].PrizeValue);
            let piggyItem = this.choosePigUI.children[i];
            piggyItem.on('click', () => {
                this.myPig = this.bonusData[i];
                this.invokePlayBonusGame(this.myPig.ItemID, 0);
                this.generatePreparedItems();
                this.gameStateMachine.changeState(BonusState.DECIDE_ACTION);
            })
        }

        this.lblHeSoNhan.string = `x${this.heso}`;
        this.lblMyIndex.string = '';
        this.myPig = null;
        this.roundCount = 0;
    }

    private generatePreparedItems() {
        // looking for 5 random item distinct and exlude myPig, exlude broken items
        const eligibleItems = this.bonusData.filter(item => item.ItemID !== this.myPig.ItemID && !this.isBroken.includes(item.ItemID));
        const shuffled = this.shuffle(eligibleItems).slice(0, 5);
        this.preparedItems = shuffled;
    }


    startCountdown(seconds: number, onFinish: () => void) {
        let remaining = seconds;
        this.timerLabel.string = remaining.toString();
        this.schedule(() => {
            remaining--;
            this.timerLabel.string = remaining.toString();
            if (remaining <= 0) {
                this.unscheduleAllCallbacks();
                onFinish();
            }
        }, 1);
    }

    clearCountdown() {
        this.unscheduleAllCallbacks();
    }

    onStateChange(state: BonusState) {
        this.state = state;
        this.lblHeSoNhan.node.parent.active = true;
        this.lblMyIndex.node.parent.active = true;
        switch (state) {
            case BonusState.INIT:
                break;
            case BonusState.CHOOSE_PIG:
                this.pageView.scrollToPage(0, 0.3);
                break;
            case BonusState.DECIDE_ACTION:
                this.pageView.scrollToPage(1, 0.3);
                this.decideControl.buttonGroup.active = true;
                // this.startCountdown(10, () => {
                //     this.gameStateMachine.changeState(BonusState.REVEAL_PIGS);
                // });

                this.decideControl.pig1.init(this.myPig, Mode.BREAK);
                this.decideControl.pig2.init(this.preparedItems[0], Mode.SWAP);
                this.decideControl.pig3.init(this.preparedItems[1], Mode.SWAP);
                this.decideControl.pig4.init(this.preparedItems[2], Mode.SWAP);
                this.decideControl.pig5.init(this.preparedItems[3], Mode.SWAP);
                this.decideControl.pig6.init(this.preparedItems[4], Mode.SWAP);

                this.decideControl.pig1.onClickCallback = () => {
                    this.invokePlayBonusGame(this.myPig.ItemID, 1);
                    this.decideControl.pig1.revealValue();
                    this.scheduleOnce(() => {
                        this.gameStateMachine.changeState(BonusState.END);
                    }, 2)
                }

                for (let p of [this.decideControl.pig2, this.decideControl.pig3, this.decideControl.pig4, this.decideControl.pig5, this.decideControl.pig6]) {
                    p.onClickCallback = () => {
                        this.swapPigControl.setInfo(this.myPig, p.data);
                        this.gameStateMachine.changeState(BonusState.SWAP_PIG);
                        this.swapPigControl.btnAgree.node.on('click', () => {
                            let newPreparedItems = [...this.preparedItems];
                            let index = newPreparedItems.findIndex(item => item.ItemID === p.data.ItemID);
                            if (index !== -1) {
                                newPreparedItems[index] = this.myPig;
                            }
                            this.invokePlayBonusGame(p.data.ItemID, 0, newPreparedItems.map(i => i.ItemID));
                        });
                    };
                }
                break;
            case BonusState.SWAP_PIG:
                this.pageView.scrollToPage(2, 0.3);
                this.swapPigControl.btnCancel.node.on('click', () => {
                    this.gameStateMachine.changeState(BonusState.REVEAL_PIGS);
                });
                break;
            case BonusState.REVEAL_PIGS:
                this.pageView.scrollToPage(1, 0.3);
                this.decideControl.buttonGroup.active = false;
                // this.decideControl.pig1.revealValue();
                this.decideControl.pig2.revealValue();
                this.decideControl.pig3.revealValue();
                this.decideControl.pig4.revealValue();
                this.decideControl.pig5.revealValue();
                this.decideControl.pig6.revealValue();
                this.scheduleOnce(()=>{
                    //final decision
                    this.gameStateMachine.changeState(BonusState.FINAL_DECISION);
                },2)
                break;
            case BonusState.FINAL_DECISION:
                this.pageView.scrollToPage(3, 0.3);
                this.lblHeSoNhan.node.parent.active = false;
                this.lblMyIndex.node.parent.active = false;
                this.finalDecisionControl.enter();
                this.finalDecisionControl.btnDoi.node.on('click', () => {
                    this.invokePlayBonusGame(100, 1);
                    this.scheduleOnce(()=>{
                        this.gameStateMachine.changeState(BonusState.END);
                    },2)
                });
                this.finalDecisionControl.btnKhongDoi.node.on('click', () => {
                    cc.log("Chưa làm luồng này")
                    // this.invokePlayBonusGame(0, 1);
                    // this.gameStateMachine.changeState(BonusState.END);
                });
                break;
            case BonusState.END:
                this.lblHeSoNhan.node.parent.active = false;
                this.lblMyIndex.node.parent.active = false;
                this.pageView.scrollToPage(4, 0.3);
                this.endControl.enter();
                break;
        }
    }

    actHide() {
        this.node.active = false;
        this.onFinished?.();
    }

    shuffle<T>(array: T[]): T[] {
        const result = array.slice();
        for (let i = result.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [result[i], result[j]] = [result[j], result[i]];
        }
        return result;
    }

}

class GameStateMachine {
    private currentState: BonusState = BonusState.INIT;
    private stateChangeCallback: (state: BonusState) => void;

    constructor(onStateChange: (state: BonusState) => void) {
        this.stateChangeCallback = onStateChange;
    }

    public changeState(newState: BonusState) {
        this.currentState = newState;
        if (this.stateChangeCallback) this.stateChangeCallback(this.currentState);
    }

    public getState(): BonusState {
        return this.currentState;
    }
}
