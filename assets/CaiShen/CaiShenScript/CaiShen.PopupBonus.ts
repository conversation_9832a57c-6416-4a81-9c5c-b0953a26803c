import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Configs from "../../Lobby/MoveScript/Configs";
import CaiShenSignalRClient from "../../Lobby/LobbyScript/Script/networks/CaiShenSignalRClient";
import CaiShenController from "./CaiShen.Controller";
import PiggyItem, { Mode } from "./PiggyItem";
import { DecideControl } from "./Bonus/DecideControl";
import SwapPigControl from "./Bonus/SwapPigControl";
import EndControl from "./Bonus/EndControl";
import FinalDecisionControl from "./Bonus/FinalDecisionControl";

const { ccclass, property, menu } = cc._decorator;

enum BonusState {
    INIT, CHOOSE_PIG, DECIDE_ACTION, SWAP_PIG, REVEAL_PIGS, FINAL_DECISION, END
}

export interface ItemData {
    ItemID: number;
    PrizeID: number;
    PrizeValue: number;
}

@ccclass
@menu("CaiShen/PopupBonus")
export default class CaiShenPopupBonus extends cc.Component {
    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property(cc.Node) leftColumn: cc.Node = null;
    @property(cc.Node) rightColumn: cc.Node = null;

    @property(cc.Node) choosePigUI: cc.Node = null;
    @property(DecideControl) decideControl: DecideControl = null;
    @property(SwapPigControl) swapPigControl: SwapPigControl = null;
    @property(FinalDecisionControl) finalDecisionControl: FinalDecisionControl = null;
    @property(EndControl) endControl: EndControl = null;

    @property(cc.Label)
    lblTimer: cc.Label = null;

    @property(cc.Sprite)
    progressTimer: cc.Sprite = null;

    @property(cc.Label)
    lblMyIndex: cc.Label = null;

    @property(cc.Label)
    lblHeSoNhan: cc.Label = null;

    @property(cc.Button)
    btnFastPlay: cc.Button = null;

    @property([cc.Label])
    prizeLabels: cc.Label[] = [];

    private gameStateMachine: GameStateMachine;
    private bonusData: ItemData[] = [];
    private myPigIdx: number;
    private onFinished: Function = null;
    private openedIDs: number[] = [];
    private openningIDs: number[] = [];

    heso = 0;
    private duration = 0;
    private _time = 0;
    private _timerInterval: number;

    private swapLeft = 3;

    //get/set time
    get time() {
        return this._time;
    }
    set time(value: number) {
        this._time = value;
        // Cập nhật label theo giây (hiển thị nguyên)
        this.lblTimer.string = Math.ceil(value).toString() + 's';
        this.lblTimer.node.active = value > 0;

        // Cập nhật progress mượt
        const ratio = Math.max(0, Math.min(1, value / this.duration));
        this.progressTimer.fillRange = ratio;
    }


    onLoad() {
        this.gameStateMachine = new GameStateMachine((state) => {
            this.onStateChange(state);
        });
        CaiShenSignalRClient.getInstance().receive('resultBonusGame', async (data) => {
            const bonusGame = data.BonusGame;
            if (bonusGame.Items.length > 0) {
                for (const item of bonusGame.Items) {
                    this.openedIDs.push(item.ItemID);
                }

                let items = bonusGame.Items;
                for (const pig of this.decideControl.pigs.children) {
                    const item = items.find(o => o.ItemID === pig.getComponent(PiggyItem).itemID);
                    if (item) {
                        await pig.getComponent(PiggyItem).revealValue(item.PrizeValue);
                        this.prizeLabels.find(o => {
                            return o.node['bonus_data'].ItemID === item.ItemID && o.node.active
                        }).node.color = cc.Color.BLACK.fromHEX('#666666');
                    }
                }
                this.scheduleOnce(() => {
                    this.gameStateMachine.changeState(BonusState.FINAL_DECISION);
                }, 2)
            }
            if (bonusGame.AverageValue > 0) {
                this.finalDecisionControl.setInfo(bonusGame.AverageValue, this.heso);
            }
            if (bonusGame.PrizeValue > 0) {
                this.endControl.setInfo(bonusGame.PrizeValue, this.heso);
                await this.decideControl.pig1.revealValue(data.BonusGame.PrizeValue);
                this.gameStateMachine.changeState(BonusState.END);
            }
        })
    }
    /**
     *  1 is finish, else 0
     */
    invokePlayBonusGame(itemID?: number, isFinish?: number, itemRandom?: number[]) {
        let payload = {
            "CurrencyID": Configs.Login.CurrencyID,
            "RoomID": CaiShenController.getInstance().roomID,
        };
        if (itemID !== undefined) {
            payload['itemID'] = itemID;
        }
        if (itemRandom !== undefined) {
            payload['itemRandom'] = itemRandom.join(",");
        }
        if (isFinish !== undefined) {
            payload['isFinish'] = isFinish;
        }

        CaiShenSignalRClient.getInstance().send(
            'PlayBonusGame', [payload],
            (data) => {
                if (data.c < 0) {
                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                }
            }
        );
    }

    protected init() {
        this.pageView.content.children.forEach(node => {
            node.active = true;
        })
    }

    showBonus(bonus: string, onFinished: Function) {
        this.init();
        this.onFinished = onFinished;
        this.node.active = true;

        this.bonusData = bonus.split(";").map(item => {
            const [ItemID, PrizeID, PrizeValue] = item.split(",").map(Number);
            return { ItemID, PrizeID, PrizeValue };
        });

        for (let i = 0; i < this.bonusData.length; i++) {
            this.prizeLabels[i].string = Utils.formatNumber(this.bonusData[i].PrizeValue);
            this.prizeLabels[i].node.color = cc.Color.WHITE;
            this.prizeLabels[i].node['bonus_data'] = this.bonusData[i];
            let piggyItem = this.choosePigUI.children[i];
            piggyItem.off('click');
            piggyItem.on('click', () => {
                this.myPigIdx = (i + 1);
                this.lblMyIndex.string = `${i + 1}`;
                this.invokePlayBonusGame(i + 1, 0);
                let ids = this.shuffle([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);
                ids = ids.filter(id => id !== this.myPigIdx);
                ids = ids.slice(0, 5).sort();
                this.openningIDs = ids;
                this.decideControl.pig1.init(this.myPigIdx, Mode.BREAK);
                this.decideControl.pig2.init(ids[0], Mode.SWAP);
                this.decideControl.pig3.init(ids[1], Mode.SWAP);
                this.decideControl.pig4.init(ids[2], Mode.SWAP);
                this.decideControl.pig5.init(ids[3], Mode.SWAP);
                this.decideControl.pig6.init(ids[4], Mode.SWAP);
                this.gameStateMachine.changeState(BonusState.DECIDE_ACTION);
            })
        }

        this.lblHeSoNhan.string = `x${this.heso}`;
        this.myPigIdx = null;
        this.lblMyIndex.string = '';
        this.btnFastPlay.node.active = true;
        this.swapLeft = 3;
        this.openedIDs = [];
        this.gameStateMachine.changeState(BonusState.CHOOSE_PIG);
    }

    onStateChange(state: BonusState) {
        this.clearTimer();
        switch (state) {
            case BonusState.INIT:
                break;
            case BonusState.CHOOSE_PIG:
                this.pageView.scrollToPage(0, 0.3);

                this.startCountdown(15, () => {
                    this.invokePlayBonusGame(1, 1);
                });
                this.showFooter();
                break;
            case BonusState.DECIDE_ACTION:
                this.pageView.scrollToPage(1, 0.3);
                this.swapLeft--;

                this.startCountdown(10, () => {
                    // this.invokePlayBonusGame(this.myPigIdx, 0, this.openningIDs);
                    this.gameStateMachine.changeState(BonusState.REVEAL_PIGS);
                });

                this.decideControl.buttonGroup.active = true;

                this.decideControl.pig1.onClickCallback = () => {
                    this.invokePlayBonusGame(this.myPigIdx, 1);
                }

                let ids = [this.decideControl.pig2.itemID, this.decideControl.pig3.itemID, this.decideControl.pig4.itemID, this.decideControl.pig5.itemID, this.decideControl.pig6.itemID];

                for (let p of [this.decideControl.pig2, this.decideControl.pig3, this.decideControl.pig4, this.decideControl.pig5, this.decideControl.pig6]) {
                    p.onClickCallback = () => {
                        this.swapPigControl.setInfo(this.myPigIdx, p.itemID);
                        this.gameStateMachine.changeState(BonusState.SWAP_PIG);
                        this.swapPigControl.btnAgree.node.off('click');
                        this.swapPigControl.btnAgree.node.on('click', () => {

                            let newIDs = [this.myPigIdx];

                            for (let id of ids) {
                                if (id !== p.itemID) newIDs.push(id);
                            }

                            this.myPigIdx = p.itemID;
                            this.lblMyIndex.string = `${p.itemID}`;
                            this.openningIDs = newIDs;


                            this.gameStateMachine.changeState(BonusState.REVEAL_PIGS);
                        });
                    };
                }
                this.showFooter();
                break;
            case BonusState.SWAP_PIG:
                this.pageView.scrollToPage(2, 0.3);
                this.startCountdown(10, () => {
                    // this.invokePlayBonusGame(this.myPigIdx, 0, this.openningIDs);
                    this.gameStateMachine.changeState(BonusState.REVEAL_PIGS);
                })

                this.swapPigControl.btnCancel.node.off('click');
                this.swapPigControl.btnCancel.node.on('click', () => {
                    // this.invokePlayBonusGame(this.myPigIdx, 0, this.openningIDs);
                    this.gameStateMachine.changeState(BonusState.REVEAL_PIGS);
                });
                this.hideFooter();
                break;
            case BonusState.REVEAL_PIGS:
                this.pageView.scrollToPage(1, 0.3);
                if (this.swapLeft === 0) {
                    this.invokePlayBonusGame(this.myPigIdx, 1, this.openningIDs);
                } else {
                    this.invokePlayBonusGame(this.myPigIdx, 0, this.openningIDs);
                }
                this.decideControl.buttonGroup.active = false;
                this.showFooter();
                break;
            case BonusState.FINAL_DECISION:
                if(this.swapLeft === 0) return;
                this.pageView.scrollToPage(3, 0.3);
                this.startCountdown(10, () => {
                    this.invokePlayBonusGame(100, 1);
                })
                this.finalDecisionControl.enter();
                this.finalDecisionControl.btnDoi.node.off('click');
                this.finalDecisionControl.btnKhongDoi.node.off('click');
                this.finalDecisionControl.btnDoi.node.on('click', () => {
                    this.invokePlayBonusGame(100, 1);
                });
                this.finalDecisionControl.btnKhongDoi.node.on('click', () => {
                    let ids = this.shuffle([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);
                    ids = ids.filter(id => id !== this.myPigIdx && !this.openedIDs.includes(id));
                    ids = ids.slice(0, 5).sort();
                    this.openningIDs = ids;
                    this.decideControl.pig1.init(this.myPigIdx, Mode.BREAK);
                    this.decideControl.pig2.init(ids[0], Mode.SWAP);
                    this.decideControl.pig3.init(ids[1], Mode.SWAP);
                    this.decideControl.pig4.init(ids[2], Mode.SWAP);
                    this.decideControl.pig5.init(ids[3], Mode.SWAP);
                    this.decideControl.pig6.init(ids[4], Mode.SWAP);
                    this.gameStateMachine.changeState(BonusState.DECIDE_ACTION);
                });
                this.hideFooter();
                break;
            case BonusState.END:
                this.pageView.scrollToPage(4, 0.3);
                this.btnFastPlay.node.active = false;
                this.hideFooter();
                break;
        }
    }

    startCountdown(duration: number, cb: () => void) {
        this.clearTimer(); // Dừng timer cũ nếu có

        this.duration = duration;
        this._time = duration;
        this.time = this._time; // cập nhật giao diện ban đầu

        this._timerInterval = setInterval(() => {
            this._time -= 0.1;

            if (this._time <= 0) {
                this._time = 0;
                this.time = 0;

                this.clearTimer(); // clear trước khi gọi callback
                cb && cb();        // gọi callback trực tiếp
            } else {
                this.time = this._time;
            }
        }, 100);
    }


    private clearTimer() {
        if (this._timerInterval !== null) {
            clearInterval(this._timerInterval);
            this._timerInterval = null;
        }

        this._time = 0;
        this.time = 0;
        this.lblTimer.node.active = false;
        this.progressTimer.fillRange = 0;
    }

    hideFooter() {
        this.lblHeSoNhan.node.parent.active = false;
        this.lblMyIndex.node.parent.active = false;
    }

    showFooter() {
        this.lblHeSoNhan.node.parent.active = true;
        this.lblMyIndex.node.parent.active = true;
    }

    onClickFastPlay() {
        this.clearTimer();
        this.invokePlayBonusGame(1, 1);
    }

    actHide() {
        this.node.active = false;
        this.onFinished?.();
    }

    shuffle<T>(array: T[]): T[] {
        const result = array.slice();
        for (let i = result.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [result[i], result[j]] = [result[j], result[i]];
        }
        return result;
    }

}

class GameStateMachine {
    private currentState: BonusState = BonusState.INIT;
    private stateChangeCallback: (state: BonusState) => void;

    constructor(onStateChange: (state: BonusState) => void) {
        this.stateChangeCallback = onStateChange;
    }

    public changeState(newState: BonusState) {
        this.currentState = newState;
        if (this.stateChangeCallback) this.stateChangeCallback(this.currentState);
    }

    public getState(): BonusState {
        return this.currentState;
    }
}
