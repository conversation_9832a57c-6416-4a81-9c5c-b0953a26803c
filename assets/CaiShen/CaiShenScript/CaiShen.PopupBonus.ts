import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import { BonusListener } from "./CaiShen.Controller";
import Doi<PERSON>eo from "./DoiHeo";
import Configs from "../../Lobby/MoveScript/Configs";
import CaiShenSignalRClient from "../../Lobby/LobbyScript/Script/networks/CaiShenSignalRClient";
import CaiShenController from "./CaiShen.Controller";
import PiggyItem from "./PiggyItem";

const { ccclass, property, menu } = cc._decorator;

enum BonusState {
    INIT, CHOOSE_PIG, DECIDE_ACTION, SWAP_PIG, REVEAL_PIGS, FINAL_DECISION, END
}

export interface ItemData {
    ItemID: number;
    PrizeID: number;
    PrizeValue: number;
    isOpened: boolean;
}

@ccclass
@menu("CaiShen/PopupBonus")
export default class CaiShenPopupBonus extends cc.Component {
    @property(cc.PageView)
    pageView: cc.PageView = null;

    @property(cc.Node)
    bar: cc.Node = null;

    @property(cc.Node)
    items: cc.Node = null;

    @property(DoiHeo)
    doiHeo: DoiHeo = null;

    @property(cc.Label)
    timerLabel: cc.Label = null;

    @property(cc.Sprite)
    timerProgress: cc.Sprite = null;

    @property([cc.Label])
    pigPrizes: cc.Label[] = [];

    @property(cc.Label)
    lblMyIndex: cc.Label = null;

    @property(cc.Label)
    lblHeSoNhan: cc.Label = null;

    private state: BonusState;
    private roundCount = 0;
    private piggyItems: PiggyItem[] = [];
    private selectedPigIndex: number = -1;
    private piggyValues: number[] = [];
    private gameStateMachine: GameStateMachine;



    public listener: BonusListener = null;
    public betValue: number;

    private bonusData: ItemData[] = [];

    public onFinishedProcess: Function = null;
    private latestBonusResponse: any = null;

    //private options: number[] = [];
    private _myPig: ItemData;
    private _itemRandom: ItemData[] = [];
    private sceneIndex: number = 0;

    set myPig(v: ItemData) {
        this._myPig = v;
        this.lblMyIndex.string = v.ItemID.toString();
    }

    get myPig() {
        return this._myPig;
    }








    invokePlayBonusGame(itemID: number, isFinish: number) {
        CaiShenSignalRClient.getInstance().send(
            'PlayBonusGame', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": CaiShenController.getInstance().roomID,
                "ItemID": itemID,
                "IsFinish": isFinish,
            }],
            (data) => {
                if (data.c < 0) {
                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                }
            }
        );
    }

    protected onLoad() {
        this.gameStateMachine = new GameStateMachine((state) => {
            this.onStateChange(state);
        });
        // this.pageView.getPages().map(m => m.active = true);
        // for (let i = 0; i < this.items.childrenCount; i++) {
        //     let item = this.items.children[i];
        //     item["btn"] = item.getComponent(cc.Button);
        //     item["label"] = item.getComponentInChildren(cc.Label);
        //     item.on('click', () => {
        //         this.bonusData[i].isOpened = true;
        //         if (this.timer !== null) {
        //             clearInterval(this.timer);
        //             this.timer = null;
        //         }
        //         this.myPig = this.bonusData[i];

        //         this.invokePlayBonusGame(itemID, 0);


        //         this.sceneIndex = 1;
        //         this.pageView.scrollToPage(1, 0.3);
        //         this.showPage2();

        //     })
        // }
    }

    showBonus(bonus: string) {
        this.node.active = true;
        this.bar.active = false;

        this.bonusData = [];
        bonus.split(";").map(item => {
            const [ItemID, PrizeID, PrizeValue] = item.split(",").map(Number);
            this.bonusData.push({ ItemID, PrizeID, PrizeValue, isOpened: false });
        });

        this.pigPrizes.forEach((v: cc.Label, i: number) => {
            v.string = Utils.formatNumber(this.bonusData[i].PrizeValue);
        })

        this.lblHeSoNhan.string = `x${this.bonusData[0].PrizeValue / this.betValue}`;
        this.lblMyIndex.string = '';

        this.startCountDown();
    }

    public handleRespone(data) {
        this.latestBonusResponse = data;
        if (data.PrizeValue > 0) {
            this.pageView.scrollToPage(4, 0.3);
            let page4 = this.pageView.getPages()[4];
            page4.getChildByName('result').getComponent(cc.Label).string = Utils.formatNumber(data.PrizeValue);
            page4.getChildByName('heso').getComponent(cc.Label).string = `x1`;
            page4.getChildByName('total').getComponent(cc.Label).string = Utils.formatNumber(data.PrizeValue);
            page4.getChildByName('btnClaim').on("click", () => {
                this.onFinishedProcess();
            })
        }
        if (data.Items.length > 0) {
            this.pageView.scrollToPage(1, 0.3);
        }

    }

    private generateIndex(): ItemData[] {
        let rand: ItemData[];
        do {
            rand = this.bonusData
                .filter(i => !i.isOpened)
                .sort(() => Math.random() - 0.5)
                .slice(0, 5);
        } while (rand.includes(this.myPig));
        return rand
    }

    showPage2() {
        let page2 = this.pageView.getPages()[1];
        let pigs = page2.getChildByName('pigs');
        let buttons = page2.getChildByName('buttons');
        this._itemRandom = this.generateIndex();
        let items = [...this._itemRandom, this.myPig];
        for (let i = 0; i < 6; i++) {
            let pig = pigs.children[i];
            let btn = buttons.children[i];

            btn["itemData"] = items[i];
            let anim = pig.getComponent(cc.Animation);
            let lblPrize = pig.getChildByName("prize").getComponent(cc.Label);
            let lblIndex = pig.getChildByName("label").getComponent(cc.Label);

            lblIndex.string = items[i].ItemID.toString();
            anim.play("pig_close");
            lblPrize.string = '';
            btn.active = true;
            if (items[i] === this.myPig) {
                btn.on("click", () => {
                    btn.active = false;
                    anim.play("pig_open");
                    Tween.numberTo(lblPrize, items[i].PrizeValue, 0.5);
                    this.scheduleOnce(() => {
                        CaiShenSignalRClient.getInstance().send(
                            'PlayBonusGame', [{
                                "CurrencyID": Configs.Login.CurrencyID,
                                "RoomID": CaiShenController.getInstance().roomID,
                                "ItemID": this.bonusData[i].ItemID,
                                "IsFinish": 1,
                            }],
                            (data) => {
                                if (data.c < 0) {
                                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                                }
                            }
                        );
                    }, 1.5)
                })
            } else {
                btn.on("click", () => {
                    this.sceneIndex = 2;
                    this.pageView.scrollToPage(2, 0.3);
                    this.showPage3(this.myPig, btn["itemData"]);
                })
            }
        }
    }

    showPage3(from: ItemData, to: ItemData) {
        let page3 = this.pageView.getPages()[2];
        page3.getChildByName("from").getComponent(cc.Label).string = from.ItemID.toString();
        page3.getChildByName("to").getComponent(cc.Label).string = to.ItemID.toString();
        page3.getChildByName("btnAgree").on("click", () => {
            this.myPig = to;
            let rand = this.generateIndex();
            CaiShenSignalRClient.getInstance().send(
                'PlayBonusGame', [{
                    "CurrencyID": Configs.Login.CurrencyID,
                    "RoomID": CaiShenController.getInstance().roomID,
                    "ItemID": to.ItemID,
                    "ItemRandom": rand.map(i => i.ItemID).join(","),
                    "IsFinish": 0,
                }],
                (data) => {
                    if (data.c < 0) {
                        App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                    }
                }
            );
        })
        page3.getChildByName("btnCancel").on("click", () => {
            this.pageView.scrollToPage(1, 0.3);
        })
    }


    dismiss() {
        this.bar.active = true;
        this.node.active = false;
        this.sceneIndex = 0;
        this.pageView.scrollToPage(0, 0.3);
        this.bonusData = [];
        this._itemRandom = [];
        this.latestBonusResponse = null;
        //clear timer
        if (this.timer !== null) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    startCountdown(seconds: number, onFinish: () => void) {
        let remaining = seconds;
        this.timerLabel.string = remaining.toString();
        this.schedule(() => {
            remaining--;
            this.timerLabel.string = remaining.toString();
            if (remaining <= 0) {
                this.unscheduleAllCallbacks();
                onFinish();
            }
        }, 1);
    }

    private setTimeLabel(t: number) {
        if (t <= 0) {
            this.timerLabel.node.parent.active = false;
        } else {
            this.timerLabel.node.parent.active = true;
            this.timerLabel.string = `${t}s`;
        }
    }

    onStateChange(state: BonusState) {
        this.state = state;
        switch (state) {
            case BonusState.INIT:
                break;
            case BonusState.CHOOSE_PIG:
                break;
            case BonusState.DECIDE_ACTION:
                break;
            case BonusState.SWAP_PIG:
                break;
            case BonusState.REVEAL_PIGS:
                break;
            case BonusState.FINAL_DECISION:
                break;
            case BonusState.END:
               
}

class GameStateMachine {
    private currentState: BonusState = BonusState.INIT;
    private stateChangeCallback: (state: BonusState) => void;

    constructor(onStateChange: (state: BonusState) => void) {
        this.stateChangeCallback = onStateChange;
    }

    public changeState(newState: BonusState) {
        this.currentState = newState;
        if (this.stateChangeCallback) this.stateChangeCallback(this.currentState);
    }

    public getState(): BonusState {
        return this.currentState;
    }
}
