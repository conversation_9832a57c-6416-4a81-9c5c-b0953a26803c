import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import { ItemData } from "./CaiShen.PopupBonus";

const {ccclass, property} = cc._decorator;

@ccclass
export default class DoiHeo extends cc.Component {

    @property([cc.Node])
    pigs: cc.Node[] = [];

    @property([cc.Button])
    btns: cc.Button[] = [];

    show(data: ItemData[], onClickItem: (index: ItemData) => void) {
        let items = [...data].sort((a,b)=> b.ItemID - a.ItemID);
        for(let i = 0; i < items.length; i++){
            let anim = this.pigs[i].getComponent(cc.Animation);
            let lblPrize = this.pigs[i].getChildByName("prize").getComponent(cc.Label);
            this.pigs[i].getChildByName("label").getComponent(cc.Label).string = items[i].ItemID.toString();
            anim.play("pig_close");
            lblPrize.string = '';
            this.btns[i].node.active = true;

            this.btns[i].node.on("click", () => {
                this.btns[i].node.active = false;
                anim.play("pig_open");
                Tween.numberTo(lblPrize, items[i].PrizeValue, 1.5);
                onClickItem(items[i]);
            })
        }
    }

}
