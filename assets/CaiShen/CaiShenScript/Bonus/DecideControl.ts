import PiggyItem from "../PiggyItem";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("CaiShen/Bonus/DecideControl")
export class DecideControl extends cc.Component {
    @property(cc.Node)
    pigs: cc.Node = null;
    @property(PiggyItem) pig1: PiggyItem = null;
    @property(PiggyItem) pig2: PiggyItem = null;
    @property(PiggyItem) pig3: PiggyItem = null;
    @property(PiggyItem) pig4: PiggyItem = null;
    @property(PiggyItem) pig5: PiggyItem = null;
    @property(PiggyItem) pig6: PiggyItem = null;

    @property(cc.Node) buttonGroup: cc.Node = null;
}
