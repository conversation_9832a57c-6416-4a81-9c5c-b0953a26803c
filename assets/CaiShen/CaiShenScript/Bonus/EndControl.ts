import Utils from "../../../Lobby/LobbyScript/Script/common/Utils";
const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("CaiShen/Bonus/EndControl")
export default class EndControl extends cc.Component {
    @property(cc.Label)
    lblPrize: cc.Label = null;

    @property(cc.Label)
    lblMultiplier: cc.Label = null;

    @property(cc.Label)
    lblTotalPrize: cc.Label = null;

    @property(cc.Button)
    btnClose: cc.Button = null;

    private _prize: number = 0;
    private _multiplier: number = 0;

    setInfo(prize: number, multiplier: number) {
        this._prize = prize;
        this._multiplier = multiplier;

        this.lblPrize.string = Utils.formatNumber(prize);
        this.lblMultiplier.string = `x${multiplier}`;
        this.lblTotalPrize.string = Utils.formatNumber(prize * multiplier);
    }

    reset(){
        this.lblPrize.string = '';
        this.lblTotalPrize.string = '';
        this.lblMultiplier.string = '';
        this.btnClose.node.active = false;
    }
}
