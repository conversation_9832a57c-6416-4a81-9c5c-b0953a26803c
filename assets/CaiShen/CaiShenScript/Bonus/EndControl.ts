import Tween from "../../../Lobby/LobbyScript/Script/common/Tween";
const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("CaiShen/Bonus/EndControl")
export default class EndControl extends cc.Component {
    @property(cc.Label)
    lblPrize: cc.Label = null;

    @property(cc.Label)
    lblMultiplier: cc.Label = null;

    @property(cc.Label)
    lblTotalPrize: cc.Label = null;

    @property(cc.Button)
    btnClose: cc.Button = null;

    private _prize: number = 0;
    private _multiplier: number = 0;

    setInfo(prize: number, multiplier: number) {
        this._prize = prize;
        this._multiplier = multiplier;
    }

    enter(){
        this.reset();
        Tween.numberTo(this.lblPrize, this._prize, 1);
        this.lblMultiplier.string = `x${this._multiplier}`;
        this.scheduleOnce(()=>{
            Tween.numberTo(this.lblTotalPrize, this._prize * this._multiplier, 1);
            this.btnClose.node.active = true;
        },1.1)
    }

    reset(){
        this.lblPrize.string = '';
        this.lblTotalPrize.string = '';
        this.lblMultiplier.string = '';
        this.btnClose.node.active = false;
    }
}
