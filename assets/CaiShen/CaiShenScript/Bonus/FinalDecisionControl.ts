const {ccclass, property} = cc._decorator;
import Tween from "../../../Lobby/LobbyScript/Script/common/Tween";

@ccclass
export default class FinalDecisionControl extends cc.Component {
    @property(cc.Label) lblPrizeGiven: cc.Label = null;
    @property(cc.Label) lblPrizeReceived: cc.Label = null;
    @property(cc.Label) lblMultiplier: cc.Label = null;
    @property(cc.Button) btnDoi: cc.Button = null;
    @property(cc.Button) btnKhongDoi: cc.Button = null;

    private _prizeGiven: number = 0;
    private _prizeReceived: number = 0;
    private _multiplier: number = 0;

    setInfo(prizeGiven: number, prizeReceived: number, multiplier: number) {
        this._prizeGiven = prizeGiven;
        this._prizeReceived = prizeReceived;
        this._multiplier = multiplier;
    }

    enter(){
        this.reset();
        Tween.numberTo(this.lblPrizeGiven, this._prizeGiven, 1);
        Tween.numberTo(this.lblPrizeReceived, this._prizeReceived, 1);
        this.lblMultiplier.string = `x${this._multiplier}`;
    }
    
    reset(){
        this.lblPrizeGiven.string = '';
        this.lblPrizeReceived.string = '';
        this.lblMultiplier.string = '';
    }
}
