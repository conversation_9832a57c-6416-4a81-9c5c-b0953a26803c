const {ccclass, property} = cc._decorator;
import Tween from "../../../Lobby/LobbyScript/Script/common/Tween";

@ccclass
export default class FinalDecisionControl extends cc.Component {
    @property(cc.Label) lblPrizeAvg: cc.Label = null;
    @property(cc.Label) lblPrizeFinal: cc.Label = null;
    @property(cc.Label) lblMultiplier: cc.Label = null;
    @property(cc.Button) btnDoi: cc.Button = null;
    @property(cc.Button) btnKhongDoi: cc.Button = null;

    private _prizeAverage: number = 0;
    private _prizeFinal: number = 0;
    private _multiplier: number = 0;

    setInfo(AverageValue: number, multiplier: number) {
        this._prizeAverage = AverageValue;
        this._prizeFinal = AverageValue * multiplier;
        this._multiplier = multiplier;
    }

    enter(){
        this.reset();
        Tween.numberTo(this.lblPrizeAvg, this._prizeAverage, 1);
        Tween.numberTo(this.lblPrizeFinal, this._prizeFinal, 1);
        this.lblMultiplier.string = `x${this._multiplier}`;
    }
    
    reset(){
        this.lblPrizeAvg.string = '';
        this.lblPrizeFinal.string = '';
        this.lblMultiplier.string = '';
    }
}
