import { ItemData } from "../CaiShen.PopupBonus";
const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("CaiShen/Bonus/SwapPigControl")
export default class SwapPigControl extends cc.Component {

    @property(cc.Label) lblFrom: cc.Label = null;
    @property(cc.Label) lblTo: cc.Label = null;
    @property(cc.Button) btnAgree: cc.Button = null;
    @property(cc.Button) btnCancel: cc.Button = null;

    public from: number = null;
    public to: number = null;

    setInfo(from: number, to: number) {
        this.from = from;
        this.to = to;
        this.lblFrom.string = from.toString();
        this.lblTo.string = to.toString();
    }
}
