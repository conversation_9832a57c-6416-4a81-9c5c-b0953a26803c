import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Http from "../../Lobby/MoveScript/Http";
import Configs from "../../Lobby/MoveScript/Configs";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Pagination from "../../Lobby/LobbyScript/Pagination";
const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("CaiShen/PopupLSC")
export default class CaiShenPopupLSC extends Dialog {
    @property(cc.Node)
    itemTemplate: cc.Node = null;

    @property(cc.Node)
    itemContainer: cc.Node = null;

    @property(cc.SpriteFrame)
    item1: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    item2: cc.SpriteFrame = null;

    @property(cc.Node)
    view: cc.Node = null;

    @property(Pagination)
    pagination: Pagination = null;


    private _dataLoaded: any[];

    protected onLoad(): void {
        this.pagination.initListener((page: number) => {
            this.displayData(page);
        })
    }

    show() {
        super.show();
        this.loadData();
    }

    dismiss() {
        super.dismiss();
    }

    private loadData() {
        Http.get(Configs.App.DOMAIN_CONFIG['GodOfFortune_GetTransactionLog'], {
            "CurrencyID": Configs.Login.CurrencyID,
            "type": 1,
            "Page": 1,
            "PageSize": 100,
        }, (status, response) => {
            if (status === 200) {
                this._dataLoaded = response["d"] as any[];
                if(this._dataLoaded.length === 0) return;
                this.displayData(1);
            }
        })
    }
    
    private displayData(page: number) {
        this.itemContainer.removeAllChildren();
        this.pagination.updatePagination(Math.ceil(this._dataLoaded.length / 10), page);

        let result = this._dataLoaded.slice((page - 1) * 10, page * 10);
        for (let i = 0; i < result.length; i++) {
            let itemRow = cc.instantiate(this.itemTemplate);
            itemRow.active = true;
            this.itemContainer.addChild(itemRow);

            let winLineLength = result[i].prizeData ? result[i].prizeData.split(";").length : 0;

            itemRow.children[0].getComponent(cc.Label).string = result[i].spinID;
            itemRow.children[1].getComponent(cc.Label).string = Utils.formatDatetime(result[i].createTime, "dd-MM-yyyy HH:mm:ss");
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumberMin(result[i].betValue);
            itemRow.children[3].getComponent(cc.Label).string = Utils.formatNumber(result[i].totalBetValue);
            itemRow.children[4].getComponent(cc.Label).string = winLineLength.toString() + " " + App.instance.getTextLang('oc7');
            itemRow.children[5].getComponent(cc.Label).string = Utils.formatNumber(result[i].paylinePrizeValue);

            itemRow.getComponent(cc.Sprite).spriteFrame = (i % 2 === 0) ? this.item1 : this.item2;
        }
    }




}
