import CaiShenPopupLSC from "./CaiShen.PopupLSC";
import SettingButton from "./CaiShen.SettingButton";
import CaiShenPopupGuide from "./CaiShen.PopupGuide";
import App from "../../Lobby/LobbyScript/Script/common/App";
import CaiShenPopupLSH from "./CaiShen.PopupLSH";
import Configs from "../../Lobby/MoveScript/Configs";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Http from "../../Lobby/MoveScript/Http";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";
import ToastMessage from "./ToastMessage";
import SlotMachine from "./SlotMachine";
import CaiShenPopupBonus from "./CaiShen.PopupBonus";
import CaiShenSignalRClient from "../../Lobby/LobbyScript/Script/networks/CaiShenSignalRClient";
import { InfoView } from "../../Lobby/Slot/InfoView";
import { EffectCreator, EnumEffect } from "../../Lobby/Slot/SlotConfig";

const { ccclass, property, menu } = cc._decorator;

export interface BonusListener {
    selectPiggy?(piggyIndex: number, randoms: number[]): void;
    quickPlayBonus?(): void;
}

@ccclass
@menu("CaiShen/Controller")
export default class CaiShenController extends cc.Component {
    private static sharedInstance? : CaiShenController;
    public static getInstance() {
        return this.sharedInstance || (this.sharedInstance = new this());
    }
    @property(CaiShenPopupGuide) popupGuide: CaiShenPopupGuide = null;
    @property(CaiShenPopupBonus) popupBonus: CaiShenPopupBonus = null;
    @property(CaiShenPopupLSC) popupLSC: CaiShenPopupLSC = null;
    @property(CaiShenPopupLSH) popupLSH: CaiShenPopupLSH = null;

    @property(cc.Node) gameUI: cc.Node = null;
    @property(cc.Node) bar: cc.Node = null;
    @property(cc.Node) topLayer: cc.Node = null;
    @property(cc.Node) autoBar: cc.Node = null;

    @property(cc.Label) lblJackpotAll: cc.Label = null;
    @property(cc.Label) lblSession: cc.Label = null;
    @property(cc.Label) lblJackpot: cc.Label = null;
    @property(cc.Label) lblBetValue: cc.Label = null;
    @property(cc.Label) lblWinValue: cc.Label = null;
    @property(cc.Label) lblTotalBetValue: cc.Label = null;
    @property(cc.Label) lblPayLinePrizeValue: cc.Label = null;
    @property(cc.Label) lblTotalAutoSpin: cc.Label = null;

    @property(cc.Button) btnBetUp: cc.Button = null;
    @property(cc.Button) btnBetDown: cc.Button = null;
    @property(cc.Button) btnSpin: cc.Button = null;
    @property(cc.Button) btnStopAutoSpin: cc.Button = null;
    @property(cc.Toggle) toggleSetting: cc.Toggle = null;

    @property(ToastMessage) toastMessage: ToastMessage = null;
    @property(SlotMachine) slotMachine: SlotMachine = null;
    @property(InfoView) infoPanel: InfoView = null;

    @property(cc.Node) effectBigWin: cc.Node = null;
    @property(cc.Node) effectBonus: cc.Node = null;

    private latestSlotData: SlotGameData = null;

    private holdTimeout: any = null;
    private isHolding: boolean = false;

    private readonly API_ALL_JACKPOT_URL: string = 'https://slot-alpha.bavenoth.com/fortune/api/Game/JackpotGetAll';

    private _isSpinning: boolean = false;
    private set isSpinning(value: boolean) {
        this._isSpinning = value;
        this.btnBetDown.interactable = !value;
        this.btnBetUp.interactable = !value;
        // this.settingButton.setInteractable(!value);
        this.btnSpin.interactable = !value;
    }
    private get isSpinning(): boolean {
        return this._isSpinning;
    }

    private _roomID: number = 1;
    private set roomID(roomID: number) {
        this._roomID = roomID;
        this.changeRoom(roomID);
    }
    public get roomID(): number {
        return this._roomID;
    }

    private _currentAutoSpin: number = 0;
    private set currentAutoSpin(value: number) {
        this._currentAutoSpin = value;
        this.lblTotalAutoSpin.string = Utils.formatNumber(value);
        this.btnStopAutoSpin.node.active = value > 0;
        // this.btnSpin.node.active = value <= 0;
    }
    private get currentAutoSpin() {
        return this._currentAutoSpin;
    }

    private _effectCreator: EffectCreator = null;

    protected onLoad() {
        window["CaiShenController"] = this;
        CaiShenController.sharedInstance = this;
        this.initListeners();
        this.initEffects();
        this.initHubs();
        this.slotMachine.setTurbo(false);
        this.slotMachine.initializeReels();
    }

    start() {
        this.currentAutoSpin = 0;
        this.roomID = 1;
        this.infoPanel.setInfo(Configs.Login.Nickname, Configs.Login.Avatar, Configs.Login.GoldCoin);
    }


    private initListeners() {
        this.popupBonus.listener = this;

        this.btnSpin.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
        this.btnSpin.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);

        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            this.infoPanel.setInfo(Configs.Login.Nickname, Configs.Login.Avatar, Configs.Login.GoldCoin);
        }, this);

        [10, 50, 200, 1000, 5000].forEach((value, index) => {
            this.autoBar.children[index].on('click', () => {
                this.autoBar.active = false;
                this.currentAutoSpin = value;
                this.onSpin();
            })
        })
    }

    private initHubs() {
        CaiShenSignalRClient.getInstance().receive('UpdateJackPot', (data: number) => {
            cc.log("UpdateJackPot", data);
            Tween.numberTo(this.lblJackpotAll, data, 0.3);
        })

        CaiShenSignalRClient.getInstance().receive('MessageError', (data) => {
            this.toastMessage.showMessage(App.instance.getTextLang(`me${data["c"]}`));
        })

        CaiShenSignalRClient.getInstance().receive('JoinGame', (data) => {
            cc.log("JoinGame", data);
            this.latestSlotData = data;
            this.lblSession.string = ``;
            this.lblBetValue.string = Utils.formatNumber(data.BetValue);
            this.lblTotalBetValue.string = Utils.formatNumber(data.BetValue * 243);
            this.updateRoomJackpot(data.SlotInfo.RoomID);
            this.resumeGame(data.SlotInfo.GameStatus);
        })

        CaiShenSignalRClient.getInstance().receive('ResultSpin', async (data) => {
            cc.log("ResultSpin", data);
            this.latestSlotData = data;
            this.isSpinning = true;
            this.slotMachine.resetAllItems();
            this.node.stopAllActions();
            this.lblSession.string = `#${data.SpinData.SpinID}`;
            await this.slotMachine.startSpin(data.SpinData.SlotsData);
            this.onSpinCompleted();
        })

        CaiShenSignalRClient.getInstance().receive('ResultAccumulate', (data) => {
            cc.log("ResultAccumulate", data);
        })

        CaiShenSignalRClient.getInstance().receive('resultBonusGame', (data) => {
            cc.log("resultBonusGame", data.BonusGame);
            this.popupBonus.handleRespone(data.BonusGame);
        })
    }

    private initEffects() {
        this._effectCreator = {
            [EnumEffect.BIGWIN]: {
                condition: () => {
                    let payLinePrize = this.getPayLinePrize();
                    let CurrentBetValue = this.latestSlotData.BetValue;
                    return payLinePrize >= 85 * CurrentBetValue;
                },
                effect: async () => {
                    this.effectBigWin.active = true;
                    await new Promise<void>(resolve => {
                        cc.tween(this.effectBigWin)
                            .set({ scale: 0 })
                            .to(0.3, { scale: 1 }, { easing: cc.easing.backOut })
                            .delay(7)
                            .call(() => {
                                this.effectBigWin.active = false;
                                resolve();
                            })
                            .start();
                    });
                }
            },
            [EnumEffect.BONUS]: {
                condition: () => {
                    return this.latestSlotData.SpinData.GameStatus === 3;
                },
                effect: async () => {
                    this.effectBonus.active = true;
                    await new Promise<void>(resolve => {
                        cc.tween(this.effectBonus.getChildByName('fx'))
                            .set({ scale: 0 })
                            .to(0.3, { scale: 1 }, { easing: cc.easing.backOut })
                            .delay(2)
                            .call(() => {
                                this.effectBonus.active = false;
                                this.gameUI.children[0].active = false;
                                resolve();
                            })
                            .start();
                    });
                    const betValue = this.latestSlotData.BetValue;
                    this.popupBonus.onFinishedProcess = () => {
                        this.gameUI.children[0].active = true;
                        this.popupBonus.dismiss();
                    }
                    this.popupBonus.showBonus(this.latestSlotData.SpinData.BonusGameData, betValue);
                }
            }
        }
    }

    resumeGame(status: number) {
        cc.log("KHOA TRAN - current game status: ", status);
        //TODO
        // let gameStatus = this.latestSlotData.SlotInfo.GameStatus;
        // if (gameStatus === 1) {

        // }
        // else if (gameStatus === 3) {
        //     //TODO
        //     //this.popupBonus.loadBonusGameData(this.latestSlotData.SpinData.BonusGameData);
        // }
    }

    private parsePositionData(positionData: string): { payLines: number[][], commonPayLines: number[] } {
        const payLines = positionData.split(";").map(row => row.split(",").map(Number));
        const commonPayLines = Array.from(new Set(payLines.flat()));
        return { payLines, commonPayLines };
    }

    private onSpinCompleted() {
        const payLinePrize = this.getPayLinePrize();

        this.lblWinValue.string = '0';

        if (this.currentAutoSpin > 0) {
            this.currentAutoSpin--;
        }

        const { payLines, commonPayLines } = this.parsePositionData(this.latestSlotData.SpinData.PositionData);
        let delay = 0.5;

        if (payLinePrize > 0) {
            if (this._effectCreator[EnumEffect.BIGWIN].condition()) {
                delay = 0.1;
                this.showTextPayline(payLinePrize, 6);
            } else {
                delay = 1;
                this.showTextPayline(payLinePrize, 1);
            }
            this.slotMachine.highlightItems(commonPayLines);
        }

        this.node.runAction(
            cc.sequence(
                cc.delayTime(delay),
                cc.callFunc(async () => {
                    this.showEffects(async () => {
                        this.slotMachine.resetAllItems();
                        this.isSpinning = false;
                        if (this.currentAutoSpin > 0) {
                            this.onSpin();
                            return;
                        }
                        if (payLinePrize > 0) {
                            for (let sequence of payLines) {
                                await this.slotMachine.highlightItems(sequence);
                                this.slotMachine.resetAllItems();
                            }
                        }
                    })
                })
            )
        )
    }

    private showEffects(finishCallback: Function) {
        let awaitable: Function[] = [];

        if (this._effectCreator[EnumEffect.BIGWIN].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BIGWIN].effect);
        }
        if (this._effectCreator[EnumEffect.BONUS].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BONUS].effect);
        }

        (async () => {
            for (const aw of awaitable) {
                await aw();
            }
        })().then(() => {
            this.updateGoldCoin();
            finishCallback();
        });
    }

    updateGoldCoin() {
        Configs.Login.GoldCoin = this.latestSlotData.Account.GoldBalance;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    getPayLinePrize() {
        return this.latestSlotData.SpinData.PayLinePrizeValue;
    }

    showTextPayline(prize: number, duration: number) {
        this.lblPayLinePrizeValue.string = '';
        Tween.numberTo(this.lblPayLinePrizeValue, prize, duration);
        Tween.numberTo(this.lblWinValue, prize, duration);
        this.scheduleOnce(() => {
            this.lblPayLinePrizeValue.string = '';
        }, duration + 1);
    }

    quickPlayBonus() {
        CaiShenSignalRClient.getInstance().send(
            'PlayBonusGame', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.roomID,
                "IsFinish": 1
            }],
            (data) => {
                if (data.c < 0) {
                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                }
            }
        );
    }

    selectPiggy(piggyIndex: number, randoms: number[]) {
        cc.log("KHOA TRAN - selectPiggy", piggyIndex, randoms);
        //return ;
        CaiShenSignalRClient.getInstance().send(
            'PlayBonusGame', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.roomID,
                "ItemID": piggyIndex,
                "ItemRandom": randoms.join(","),
                "IsFinish": 0,
            }],
            (data) => {
                if (data.c < 0) {
                    App.instance.showToast(App.instance.getTextLang(`me${data.c}`));
                }
            }
        );
    }

    private updateRoomJackpot(roomID: number) {
        let jackpotList: JackpotInfo[] = [];
        Http.get(this.API_ALL_JACKPOT_URL, {
            "CurrencyID": Configs.Login.CurrencyID,
        }, (status, response) => {
            if (status === 200) {
                jackpotList = response["d"];
                let room = jackpotList.find(item => item.roomID === roomID);
                let result = room ? room.jackpotFund : 0;
                Tween.numberTo(this.lblJackpot, result, 0.3);
            }
        })
    }


    private changeRoom(roomID: number) {
        CaiShenSignalRClient.getInstance().send(
            'PlayNow', [{ "CurrencyID": Configs.Login.CurrencyID, "RoomID": roomID }],
            (data) => {
            }
        )
    }

    private _spin() {
        CaiShenSignalRClient.getInstance().send(
            'Spin', [{
                "RoomID": this.roomID,
                "CurrencyID": Configs.Login.CurrencyID,
            }],
            (data) => {
                if (data.c < 0) {
                    this.isSpinning = false;
                    this.currentAutoSpin = 0;
                }
            }
        )
    }

    onSpin() {
        this.isSpinning = true;
        if (false) {
            // TODO trial mode
        }
        this._spin();
    }

    onTouchStart() {
        if (this.isSpinning) {
            return;
        }
        this.isHolding = false;

        this.holdTimeout = setTimeout(() => {
            this.isHolding = true;
            this.autoBar.active = true;
        }, 800);
    }

    onTouchEnd() {
        if (this.isSpinning) {
            return;
        }
        clearTimeout(this.holdTimeout);

        if (!this.isHolding) {
            this.onSpin(); // gọi nếu nhấn thả trước 2s
        }
    }

    onTouchCancel() {
        if (this.isSpinning) {
            return;
        }
        clearTimeout(this.holdTimeout);
    }

    actBetDecrease() {
        this.roomID = this.roomID > 1 ? this.roomID - 1 : 8;
    }

    actBetIncrease() {
        this.roomID = this.roomID < 8 ? this.roomID + 1 : 1;
    }

    onClickTurbo(target: cc.Toggle, eventData: string) {
        this.slotMachine.setTurbo(target.isChecked);
    }

    onClickStopAutoSpin() {
        this.currentAutoSpin = 0;
    }

    backToLobby() {
        CaiShenSignalRClient.getInstance().dontReceive();
        App.instance.gotoLobby();
    }

    actHistory() {
        this.toggleSetting.isChecked = false;
        this.popupLSC.show();
    }

    actWinners() {
        this.toggleSetting.isChecked = false;
        this.popupLSH.show();
    }

    actGuide() {
        if (this.isSpinning) return;
        this.toggleSetting.isChecked = false;
        this.popupGuide.show();
        this.popupGuide.enableButtonBack(true);
    }

    actBackGuideUI() {
        //active all child node of game
        this.gameUI.children.map(c => c.active = false);
        this.gameUI.children[0].active = true;
        this.popupGuide.enableButtonBack(false);
    }

    actHidden() {
        this.toggleSetting.isChecked = false;
        if (this.currentAutoSpin > 0) {
            App.instance.showConfirmDialog(
                App.instance.getTextLang("sl74"),
                () => {
                    this.node.parent.setPosition(cc.v3(10000, 0, 0));
                },
                true
            );
        }
        else {
            App.instance.ShowAlertDialog(App.instance.getTextLang("sl90"));
        }
    }

}

interface JackpotInfo {
    currencyID: number;
    roomID: number;
    jackpotFund: number;
    multiplier: number
}

interface SlotGameData {
    SlotInfo: any;
    SpinData: any; // You can define a type for this if needed
    BonusGame: any;
    PointInfo: any;
    Account: any;
    PortalID: number;
    CurrencyID: number;
    AccountID: number;
    AutoSpin: boolean;
    TotalAutoSpin: number;
    TotalLine: number;
    BetValue: number;
    AutoRoomId: number;
    moneyType: number;
    Lines: string;
    Turbo: boolean;
    RoomID: number;
    ConnectionStatus: number;
}

