import Tween from "../../Lobby/LobbyScript/Script/common/Tween";

const { ccclass, property } = cc._decorator;

export enum Mode {
    BREAK,
    SWAP,
    REVEAL,
}


@ccclass
export default class PiggyItem extends cc.Component {
    @property(cc.Button)
    piggyButton: cc.Button = null;

    @property(cc.Sprite)
    piggySprite: cc.Sprite = null;

    @property(cc.Label)
    label: cc.Label = null;

    @property(cc.Label)
    lblButton: cc.Label = null;

    @property(cc.Label)
    lblPrize: cc.Label = null;

    public itemID: number;

    onClickCallback: Function = null;

    init(itemID: number, mode: Mode) {
        this.itemID = itemID;
        this.piggySprite.node.getComponent(cc.Animation).play('pig_close');
        this.label.string = itemID.toString();
        if(mode === Mode.BREAK){
            cc.log("HEo đập");
            this.lblButton.string = 'Đập'
            this.lblPrize.string = '';
            this.piggyButton.node.active = true;
            this.piggyButton.interactable = true;
        }
        else if(mode === Mode.SWAP){
            cc.log("HEo đổi");
            this.lblButton.string = 'Đổi'
            this.lblPrize.string = '';
            this.piggyButton.node.active = true;
            this.piggyButton.interactable = true;
        }
    }

    onClick() {
        if (this.onClickCallback) this.onClickCallback(this.itemID);
    }
    revealValue(value: number) {
        this.piggyButton.node.active = false;
        this.lblPrize.node.active = true;
        this.piggySprite.node.getComponent(cc.Animation).play('pig_open');
        return new Promise<void>((resolve) => {
            this.scheduleOnce(()=>{
                Tween.numberTo(this.lblPrize, value, 0.5);
            },0.25)
            this.scheduleOnce(() => {
                resolve();
            }, 1)
        })
    }
}
