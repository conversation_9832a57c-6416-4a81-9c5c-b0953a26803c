import { ItemData } from "./CaiShen.PopupBonus";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";

const { ccclass, property } = cc._decorator;

export enum Mode {
    BREAK,
    SWAP,
    REVEAL,
}


@ccclass
export default class PiggyItem extends cc.Component {
    @property(cc.Button)
    piggyButton: cc.Button = null;

    @property(cc.Sprite)
    piggySprite: cc.Sprite = null;

    @property(cc.Label)
    label: cc.Label = null;

    @property(cc.Label)
    lblButton: cc.Label = null;

    @property(cc.Label)
    lblPrize: cc.Label = null;

    public data: ItemData;

    onClickCallback: Function = null;
    private mode: Mode;

    init(data: ItemData, mode: Mode) {
        cc.log("init", data);
        this.data = data;
        this.piggySprite.node.getComponent(cc.Animation).play('pig_close');
        this.label.string = data.ItemID.toString();
        if(mode === Mode.BREAK){
            this.lblButton.string = 'Đập'
            this.lblPrize.string = '';
            this.piggyButton.node.active = true;
            this.piggyButton.interactable = true;
        }
        if(mode === Mode.SWAP){
            this.lblButton.string = 'Đổi'
            this.lblPrize.string = '';
            this.piggyButton.node.active = true;
            this.piggyButton.interactable = true;
        }
        if(mode === Mode.REVEAL){

        }
    }

    onClick() {
        if (this.onClickCallback) this.onClickCallback(this.data);
    }

    revealValue() {
        this.piggyButton.node.active = false;
        this.lblPrize.node.active = true;
        this.lblPrize.string = Utils.formatNumber(this.data.PrizeValue);
        this.piggySprite.node.getComponent(cc.Animation).play('pig_open');
    }
}
