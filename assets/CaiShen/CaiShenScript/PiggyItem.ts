const { ccclass, property } = cc._decorator;

@ccclass
export default class Piggy<PERSON>tem extends cc.Component {
    @property(cc.Button)
    piggyButton: cc.Button = null;

    @property(cc.Sprite)
    piggySprite: cc.Sprite = null;

    @property(cc.Label)
    label: cc.Label = null;

    public index: number = 0;
    public isSelected: boolean = false;

    private onClickCallback: (index: number) => void = null;

    onLoad() {
        this.piggyButton.node.on('click', this.onClick, this);
    }

    init(index: number, onClickCallback: (index: number) => void) {
        this.index = index;
        this.onClickCallback = onClickCallback;
        this.label.string = "";
        this.isSelected = false;
        this.reset();
    }

    onClick() {
        if (this.isSelected) return;
        this.isSelected = true;
        this.piggySprite.node.color = cc.Color.GRAY;
        if (this.onClickCallback) this.onClickCallback(this.index);
    }

    revealValue(value: number) {
        this.label.string = value.toString();
        this.piggySprite.node.color = cc.Color.YELLOW;
    }

    reset() {
        this.isSelected = false;
        this.piggySprite.node.color = cc.Color.WHITE;
        this.label.string = "";
    }
}
