// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:

import App from "../../Lobby/LobbyScript/Script/common/App";
import LanguageMananger from "../../Lobby/LobbyScript/Script/common/Language.LanguageManager";
import Configs from "../../Lobby/MoveScript/Configs";
import Http from "../../Lobby/MoveScript/Http";
import MainPageController from "./MainPageController";
import { BalanceFilter, BetTicket, Deposit, Game, Line, LiveMatchInfo, MarketData, SPORT_ACTION, Tournament, UserProfile } from "./Sport.const";
import { SportNetwork } from "./Sport.NetworkClient";
import { SportDataService } from "./SportData";
import { convertObjectToArray, getTimestampFromDateBrfore } from "./Ultils";

//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html
const { ccclass, property } = cc._decorator;

@ccclass
export default class SportController extends cc.Component {
    public static Instance: SportController = null

    @property(cc.Label)
    private UserNameLabel: cc.Label = null;

    @property(cc.Label)
    private MoneyLabel: cc.Label = null;

    @property(MainPageController)
    private MainPage: MainPageController = null;
    private onStartCallBack: () => void;

    private defaultTime: number = 60;
    private currentTime: number = 60;

    private currentSportID: number = 1;
    private verticle = true;

    setOnStartCallBack(cb: () => void) {
        this.onStartCallBack = cb;
    }

    callStartCallBack() {
        this.onStartCallBack && this.onStartCallBack();
    }

    public get CurrentSportID(): number { return this.currentSportID };
    public set CurrentSportID(v: number) {
        this.currentSportID = v
    };

    protected onLoad(): void {
        SportController.Instance = this;

        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['SportGetAuthenToken'], {
            "portalID": Configs.Login.PortalID
        }, (status, data) => {
            if (data["d"]) {
                App.instance.showLoading(false);
                SportNetwork.setToken(data["d"], (data: any) => { this.setHeaderData(data) });
            }
        })

        this.getFreeBetForBetSlip();
        this.rotateScreenAndroid();
        this.verticle = window.innerWidth < window.innerHeight;
        window.addEventListener('resize', this.checkRotation.bind(this));
    }

    protected onDestroy(): void {
        window.removeEventListener('resize', this.checkRotation.bind(this));
    }

    checkRotation() {
        if (Configs.App.getPlatformName() === 'web') {
            const game = this.node.getChildByName('SportContent');
            if (this.verticle) {
                if (window.innerWidth > window.innerHeight) {
                    const designSize = cc.view.getDesignResolutionSize();
                    const height = designSize.height;
                    const scale = (game.height / height) / 1.75
                    this.node.setScale(scale, scale, 0);

                } else {
                    const scale = 1
                    this.node.setScale(scale, scale, 0);

                }
            } else {
                if (window.innerWidth > window.innerHeight) {
                    const scale = 1
                    this.node.setScale(scale, scale, 0);

                } else {
                    const designSize = cc.view.getDesignResolutionSize();
                    const height = designSize.height;
                    const scale = (game.height / height) * 1.75
                    this.node.setScale(scale, scale, 0);

                }
            }
        }
    }

    getLiveMatch(cb: () => void) {
        const url = Configs.App.DOMAIN_CONFIG['SportGLiveGetMatch'] + `${this.currentSportID}`;
        App.instance.showLoading(true);
        Http.get(url, {}, (status, data) => {
            App.instance.showLoading(false);
            if (data && data.Match) {
                const currentType = SportDataService.getSportByID(this.CurrentSportID).name.toUpperCase();
                const liveMatchs: LiveMatchInfo[] = data.Match.filter(e => e.Type === currentType);
                SportDataService.LiveMatchs = liveMatchs;
                cb && cb();
            }

        })
    }

    getLiveUrl(matchId: string, cb: (url: string) => void) {
        App.instance.showLoading(true);
        const url = Configs.App.DOMAIN_CONFIG['SportGLiveGetLink'] + matchId + '&lang=' + LanguageMananger.instance.languageCode;

        SportNetwork.getApi(url)
            .then(data => {
                App.instance.showLoading(false);
                if (data && data.H5LINKROW && data.H5LINKROW.length)
                    cb && cb(data.H5LINKROW);
            })
            .catch(err => {
                cc.error('Something went wrong:', err);
            });

    }

    getSportStatics(matchID: string, cb: () => void) {
        const url = Configs.App.DOMAIN_CONFIG['SportGetStatistic'] + matchID;
        App.instance.showLoading(true);

        SportNetwork.getApi(url)
            .then(data => {
                App.instance.showLoading(false);
                if (data) {
                    cc.log(data);
                }
            })
            .catch(err => {
                cc.error('Something went wrong:', err);
            });
    }

    private rotateScreenAndroid() {
        if (Configs.App.getPlatformName() === 'android') {
            let widget = this.node.getChildByName('SportContent').getComponent(cc.Widget)
            if (widget) {
                widget.enabled = false;
            }

            this.node.angle = 90;

            this.scheduleOnce(() => {
                if (widget) {
                    widget.enabled = true;
                    widget.updateAlignment();
                }
            }, 1);
        } else {
            const game = this.node.getChildByName('SportContent');
            if (game) {
                const widget = game.getComponent(cc.Widget);
                if (widget) {
                    widget.top = null;
                    widget.bottom = null;
                    widget.isAlignTop = false;
                    widget.isAlignBottom = false;
                    widget.enabled = false; // Tắt hẳn nếu không cần dùng nữa
                }

                const designSize = cc.view.getDesignResolutionSize();
                const height = designSize.height;
                const scale = height / game.height;

                game.scaleX = scale;
                game.scaleY = scale;
            }

        }
    }

    reconnect() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['SportGetAuthenToken'], {
            "portalID": Configs.Login.PortalID
        }, (status, data) => {
            if (data["d"]) {
                App.instance.showLoading(false);
                SportNetwork.setToken(data["d"], (data: any) => { this.setHeaderData(data) });
            }
        })

        this.getFreeBetForBetSlip();
    }

    setHeaderData(data: UserProfile) {
        if (data) {
            SportDataService.UserData = data;
            // App.instance.showLoading(false);
            if (this.UserNameLabel) {
                this.UserNameLabel.string = data?.nick_name.split('-')[1];
            }
            this.sendGetUserLimit();
            this.scheduleOnce(() => {
                this.sendGetBalance();
            }, 0.25)

        }
    }

    sendGetBalance() {
        SportNetwork.sendMessage(SPORT_ACTION.GET_BALANCE, {}, (res: any) => {
            if (res.data.profile) {
                const balance = res.data.profile[`${SportDataService.UserData.user_id}`].balance;
                if (this.MoneyLabel) {
                    this.MoneyLabel.string = `${balance}`
                }
            }
        });
    }

    sendGetUserLimit() {
        SportNetwork.sendMessage(SPORT_ACTION.GET_CLIENT_SPORT_BET_LIMIT, {

        }, (res: any) => {
            SportDataService.DepositLimits = res.details;
            this.getPartnerConfig();
        });
    }

    sendGetPlayerPastBonuses(from_date: number, to_date: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_player_past_bonuses, {
            from_date: from_date,
            to_date: to_date
        }, null);
    }

    deposit(deposit: Deposit) {
        SportNetwork.sendMessage(SPORT_ACTION.get_player_past_bonuses, {
            amount: deposit.amount,
            currency: deposit.currency,
            service: deposit.service,
            payer: deposit.payer
        }, null);
    }

    sendGetDeposit(from_date: number, to_date: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_deposits, {
            from_date: from_date,
            to_date: to_date
        }, null);
    }

    sendWithDraw(amout: number, currency: string, service: string, payee: { email: string, name: string }) {
        SportNetwork.sendMessage(SPORT_ACTION.withdraw, {
            amout: amout,
            currency: currency,
            service: service,
            payee: payee
        }, null);
    }

    sendGetWithDraw(from_date: number, to_date: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_withdrawals, {
            from_date: from_date,
            to_date: to_date
        }, null);
    }



    sendCancelDraw(id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.withdraw_cancel, {
            id: id
        }, null);
    }

    setClientSportBetLimit(sport_max_daily_bet: number, sport_max_weekly_bet: number, sport_max_monthly_bet: number, sport_max_single_bet: number) {
        SportNetwork.sendMessage(SPORT_ACTION.SET_CLIENT_SPORT_BET_LIMIT, {
            sport_max_daily_bet: sport_max_daily_bet,
            sport_max_weekly_bet: sport_max_weekly_bet,
            sport_max_monthly_bet: sport_max_monthly_bet,
            sport_max_single_bet: sport_max_single_bet
        }, null);
    }

    sendGetBalanceFilter(data: BalanceFilter) {
        SportNetwork.sendMessage(SPORT_ACTION.balance_history, {
            where: data
        }, null);
    }

    GetHistory(startTime: number, endTime?: number, cb?: (data: any[]) => void) {
        const endTimeNow = Math.floor(Date.now() / 1000);
        SportNetwork.sendMessage(SPORT_ACTION.BET_HISTORY, {
            where: {
                game: {
                    start_ts: startTime ? {
                        "@gte": startTime,
                        "@lte": endTime ? endTime : endTimeNow,
                    } : {
                        "@gte": getTimestampFromDateBrfore(0),
                        "@lte": endTimeNow
                    }
                }
            }
        }, (rs: any) => {
            if (rs.bets) {
                cb && cb(rs.bets);
            }

        });
    }


    getChangeDepositLimit() {
        SportNetwork.sendMessage(SPORT_ACTION.get_deposit_change_limit_request, {

        }, (res: any) => {
            if (res.details) {
                SportDataService.ChangeLimits = res.details
            }
        });
    }

    getBounusDetails(cb: (data: any[]) => void) {
        SportNetwork.sendMessage(SPORT_ACTION.get_bonus_details, {
            free_bonuses: true
        }, (res: any) => {
            if (res.bonuses) {
                cb && cb(res.bonuses);
            }
        });
    }

    getFreeBetForBetSlip() {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['SportGetFreeTicket'], {
            'CurrencyID': Configs.Login.CurrencyID
        }, (status, data) => {
            App.instance.showLoading(false);
            if (data['d'] && data['d'].length) {
                SportDataService.FreeTicketCount = data['d'].length;
            }

        })
    }
    getFreeBetHistory(cb: (list: any[]) => void) {
        App.instance.showLoading(true);
        Http.get(Configs.App.DOMAIN_CONFIG['SportGetFreeTicketHistory'], {

        }, (status, data) => {
            App.instance.showLoading(false);
            if (data['d'] && data['d'].length) {
                console.log(data['d'])
                cb && cb(data['d']);
            }

        })
    }

    claimBonuses(bonuses_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.claim_bonus, {
            bonus_id: bonuses_id
        }, (res: any) => {
            if (res) {

            }
        });
    }

    getClientClaimableDepositBonuses() {
        SportNetwork.sendMessage(SPORT_ACTION.get_client_claimable_deposit_bonuses, {

        }, (res: any) => {
            if (res) {

            }
        });
    }

    cancelBonus(bonus_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.cancel_bonus, {
            bonus_id: bonus_id
        }, (res: any) => {
            if (res) {

            }
        });
    }

    getSportBonusesRules() {
        SportNetwork.sendMessage(SPORT_ACTION.get_sport_bonus_rules, {

        }, (res: any) => {
            if (res.details) {
                SportDataService.BonusRules = res.details;
            }
        });
    }

    getTournamentList(tournament: Tournament) {
        SportNetwork.sendMessage(SPORT_ACTION.get_tournament_list, tournament, (res: any) => {
            // List TournamentInfo interface
        })
    }

    getTournament(tournament: Tournament) {
        SportNetwork.sendMessage(SPORT_ACTION.get_tournament, tournament, (res: any) => {
            if (res.data && res.data.length)
                // List TournamentInfo interface
                SportDataService.Tournaments = res.data
        })
    }

    getTopPlayerList(game_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_top_player_list, {
            game_id: game_id
        }, (res: any) => {
            if (res.result) {
                // TopPlayerWinList interface
            }
        });
    }

    joinTournament(tournament_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.join_to_tournament, {
            tournament_id: tournament_id
        }, (res: any) => {
            if (res.result) {
                // return 
                // "TournamentId": 1,
                // "PlayerId": 2,
                // "Amount": 1.0
            }
        });
    }

    checkTournamentGame(game_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.check_tournament_game, {
            game_id: game_id
        }, (res: any) => {
            if (res.result) {

            }
        });
    }

    getTournamentStats(tournament: Tournament) {
        SportNetwork.sendMessage(SPORT_ACTION.get_tournament_stats, tournament, (res: any) => {
            if (res.result) {

            }
        });
    }

    getResults(from_date: number, to_date: number, sport_type: number, sport_id: number, competition_id: number, cb: (data: Game[]) => void) {
        const payload: any = {
            from_date: from_date,
            to_date: to_date,
            sport_type: sport_type,
            sport_id: sport_id,
            live: 0,
            is_date_ts: 1,
        };

        if (competition_id) {
            payload.competition_id = competition_id;
        }

        SportNetwork.sendMessage(SPORT_ACTION.get_result_games, payload, (res: any) => {
            if (res.games.game) {
                const list = res.games.game as Game[];
                cb && cb(list);
            }
        });
    }


    getResultGame(game_id: number, cb: (list: Line[]) => void) {
        SportNetwork.sendMessage(SPORT_ACTION.get_results, {
            game_id: game_id
        }, (res: any) => {
            if (res.lines.line) {
                // return Line interface
                const list = res.lines.line as Line[];
                cb && cb(list);
            }
        });
    }

    getPartnerConfig() {
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: "partner.config",
            what: { partner: [] }
        }, (res: any) => {
            if (res.data.partner) {
                SportDataService.PartnerConfig = res.data.partner["1560"];
                this.getSportBetting(false);
            }
        });
    }


    cashOut(bet_id: number, price: number, mode: number, partial_price: number) {
        SportNetwork.sendMessage(SPORT_ACTION.cashout, {
            bet_id: bet_id,
            price: price,
            mdoe: mode,
            partial_price: partial_price
        }, (res: any) => {
            if (res.details) {

            }
        });
    }

    getCashOutAmount(cb: (data: any[]) => void) {
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: "notifications",
            what: { user: [] },
            subscribe: true
        }, (res: any) => {
            let suubid = res.subid;
            const list = convertObjectToArray(res.data[`${suubid}`]);
            const result = Object.entries(list).map(([id, value]) => ({
                id,
                ...value
            }));
            cb && cb(result);
        });
    }

    setBetAutoCashOut(bet_id: number, min_amount: number, partial_amount: number) {
        SportNetwork.sendMessage(SPORT_ACTION.set_bet_auto_cashout, {
            bet_id: bet_id,
            min_amount: min_amount,
            partial_amount: partial_amount
        }, (res: any) => {
            if (res.details) {

            }
        });
    }

    getBetAutoCashOut(bet_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_bet_auto_cashout, {
            bet_id: bet_id,
        }, (res: any) => {
            if (res.details) {

            }
        });
    }

    cancelBetAutoCashOut(bet_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.cancel_bet_auto_cashout, {
            bet_id: bet_id,
        }, (res: any) => {
            if (res.details) {

            }
        });
    }

    checkBetStatus(ticket_number: number, g_recaptcha_response: string) {
        SportNetwork.sendMessage(SPORT_ACTION.check_bet_status, {
            ticket_number: ticket_number,
            g_recaptcha_response: g_recaptcha_response
        }, (res: any) => {
            if (res.details) {
                // return bet data
            }
        });
    }

    sendSMSToPhoneNumber(action_type: number, phone_number: string) {
        SportNetwork.sendMessage(SPORT_ACTION.cancel_bet_auto_cashout, {
            action_type: action_type,
            phone_number: phone_number
        }, (res: any) => {
            if (res.details) {

            }
        });
    }

    getPartnerBank(region_id: number, country_code: string) {
        SportNetwork.sendMessage(SPORT_ACTION.get_partner_banks, {
            region_id: region_id,
            country_code: country_code
        }, (res: any) => {
            if (res.details) {
                // return array list Bank
            }
        });
    }

    getMatchScores(match_id_list: number[]) {
        SportNetwork.sendMessage(SPORT_ACTION.get_match_scores, {
            match_id_list: match_id_list
        }, (res: any) => {
            if (res.details) {
                // return array list Match Score
            }
        });
    }

    videoUrl(video_id: string, provider: number) {
        SportNetwork.sendMessage(SPORT_ACTION.video_url, {
            video_id: video_id,
            provider: provider
        }, (res: any) => {
            if (res) {
                // return array list Match Score

            }
        });
    }

    doBet(type: number, amount: number, bets: BetTicket[], odd_type: number, bonus_id: number, cb: () => void) {
        let payload: any = {
            type: type,
            mode: 0,
            amount: Number.parseFloat(amount.toString()),
            bets: bets,
            odd_type: odd_type,
            uid: bets[0].event_id
        }
        if (bonus_id) {
            payload.bonus_id = bonus_id;
        }
        SportNetwork.sendMessage(SPORT_ACTION.DO_BET, payload, (res: any) => {
            if (res.result === "OK") {
                cb && cb();
                this.sendGetBalance();
            } else {
                App.instance.showToast(App.instance.getTextLang("txt_bet_error2"));
            }
        });
    }

    bookBet(type: number, amount: number, bets: BetTicket[], cb: () => void) {
        SportNetwork.sendMessage(SPORT_ACTION.book_bet, {
            type: type,
            source: "1",
            amount: amount,
            bets: bets
        }, (res: any) => {
            if (res.details.result) {
                cb && cb();
            }
        });
    }

    getEventsByBookingId(booking_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_events_by_booking_id, {
            booking_id: booking_id
        }, (res: any) => {
            if (res.details) {
                // return BookBetData

            }
        });
    }

    getBoostedSelections(match_ids: number[], cb: (data: MarketData[]) => void) {
        SportNetwork.sendMessage(SPORT_ACTION.get_boosted_selections, {
            match_ids: match_ids
        }, (res: any) => {
            if (res.details) {
                // return List Market
                const list = convertObjectToArray(res.details);
                cb && cb(list);
            }
        });
    }

    getMarketType() {
        SportNetwork.sendMessage(SPORT_ACTION.get_market_type, {

        }, (res: any) => {
            if (res.details) {
                // return BettingMarket
            }
        });
    }

    getBetBuildermarkets(game_id: number) {
        SportNetwork.sendMessage(SPORT_ACTION.get_bet_builder_markets, {
            game_id: game_id
        }, (res: any) => {
            if (res.details) {
                // return BuilderMarket
            }
        });
    }

    betBuilderCalculate(selection_ids: number[]) {
        SportNetwork.sendMessage(SPORT_ACTION.bet_builder_calculate, {
            selection_ids: selection_ids
        }, (res: any) => {
            if (res.details) {
                // return BuilderMarket
                console.log(res.details.Odds)
            }
        });
    }

    unsubscribe(subid: string) {
        SportNetwork.sendMessage(SPORT_ACTION.unsubscribe, {
            subid: subid
        }, null);
    }

    unsubscribe_bulk(subids: string[]) {
        SportNetwork.sendMessage(SPORT_ACTION.unsubscribe_bulk, {
            subids: subids
        }, null);
    }


    getSportBetting(subscribe: boolean, matchType: number = 1) {
        const { start, end } = this.getStartAndEndUnix(7);
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: 'betting',
            what: {
                sport: [],
                game: []

            },
            where: {
                market: {
                    "@or": [
                        { display_key: "WINNER" },
                        {
                            display_key: "TOTALS",
                            main_order: {
                                "@in": [1]
                            }
                        },
                        {
                            display_key: "HANDICAP",
                            main_order: {
                                "@in": [1]
                            }
                        }
                    ]
                },
                game: {
                    type: {
                        "@in": [matchType]
                    },

                    start_ts: {
                        "@gte": start,
                        "@lte": end
                    }
                }
            },
            subscribe: subscribe
        }, (res: any) => {
            SportDataService.ListSport = convertObjectToArray(res.data.sport);
            this.getGame(false, null, () => {
                this.onStartCallBack && this.onStartCallBack();
            }, matchType);
        });
    }


    getGame(subscribe: boolean, startTime?: number, cb?: () => void, matchType: number = 1) {
        const { start, end } = this.getStartAndEndUnix(7);
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: 'betting',
            what: {
                game: []
                , event: [], competition: ["name", "id"], region: ["name"]
            },
            where: {
                market: {
                    "@or": [
                        { display_key: "WINNER" },
                        {
                            display_key: "TOTALS",
                            main_order: {
                                "@in": [1]
                            }
                        },
                        {
                            display_key: "HANDICAP",
                            main_order: {
                                "@in": [1]
                            }
                        }
                    ]
                },
                sport: {
                    id: this.currentSportID
                },
                game: {
                    type: {
                        "@in": [matchType]
                    },

                    start_ts: startTime ? {
                        "@gte": startTime,
                    } : {
                        "@gte": start,
                        "@lte": end
                    }
                }
            },
            subscribe: subscribe
        }, (res: any) => {
            SportDataService.ListRegion = convertObjectToArray(res.data.region);
            let competitions = []
            SportDataService.ListRegion.forEach(e => {
                let list = convertObjectToArray(e.competition);
                list?.forEach(c => {
                    c.region = e.name;
                    competitions.push(c);
                });
            })
            SportDataService.ListCompetition = competitions;
            cb && cb();
        });
    }

    getGameFilter(listID: number[], startTime?: number) {
        const { start, end } = this.getStartAndEndUnix(7);
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: 'betting',
            what: {
                game: ["id", "team1_name", "team2_name", "team1_reg_name", "team2_reg_name", "start_ts", "type", "is_blocked", "markets_count", "strong_team", "is_neutral_venue", "is_stat_available", "is_itf", "game_info", "info", "text_info", "stats", "video_provider", "last_event"]
                , event: [], competition: ["name", "id"], region: ["name"]
            },
            where: {
                market: {
                    "@or": [
                        { display_key: "WINNER" },
                        {
                            display_key: "TOTALS",
                            main_order: {
                                "@in": [1]
                            }
                        },
                        {
                            display_key: "HANDICAP",
                            main_order: {
                                "@in": [1]
                            }
                        }
                    ]
                },
                sport: {
                    id: this.currentSportID
                },
                competition: {
                    id: {
                        "@in": listID
                    }
                },
                game: {
                    type: {
                        "@in": [1]
                    },

                    start_ts: startTime ? {
                        "@gte": startTime,
                    } : {
                        "@gte": start,
                        "@lte": end
                    }
                }
            },
            subscribe: false
        }, (res: any) => {
            SportDataService.ListRegion = convertObjectToArray(res.data.region);
            let competitions = []
            SportDataService.ListRegion.forEach(e => {
                let list = convertObjectToArray(e.competition);
                list?.forEach(c => {
                    c.region = e.name;
                    competitions.push(c);
                });
            })
            SportDataService.ListCompetition = competitions;
            this.onStartCallBack && this.onStartCallBack();
        });
    }

    getEventInfo(gameId: number, cb: (data: any[]) => void) {
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: 'betting',
            what: {
                market: ["display_key", "name"],
                event: []
            },
            where: {
                market: {
                    "@or": [
                        { display_key: "WINNER" },
                        { display_key: "TOTALS" },
                        { display_key: "HANDICAP" },
                    ]
                },
                sport: {
                    id: this.currentSportID
                },
                game: {
                    id: gameId
                }
            },
            subscribe: false
        }, (res: any) => {
            if (res.data.market) {
                const list = convertObjectToArray(res.data.market);
                cb && cb(list);
            }
        });
    }

    getCompetition(sportID: number, cb: (data: any[]) => void) {
        const { start, end } = this.getStartAndEndUnix(2);
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: 'betting',
            what: {
                competition: []
            },
            where: {
                sport: {
                    id: sportID
                },
                game: {
                    start_ts: {
                        "@gte": start,
                        "@lte": end
                    }
                }
            },
            subscribe: false
        }, (res: any) => {
            if (res.data.competition) {
                const list = convertObjectToArray(res.data.competition);
                cb && cb(list);
            }
        });
    }



    getSearchCompetition(cb?: (data: any[]) => void) {
        ;
        SportNetwork.sendMessage(SPORT_ACTION.get, {
            source: 'betting',
            what: {
                competition: [],
                game: []
            },
            subscribe: true
        }, (res: any) => {
            if (res.data.competition) {
                const list = convertObjectToArray(res.data.competition);
                cb && cb(list);
            }
        });
    }

    getStartAndEndUnix(range: number): { start: number; end: number } {
        const now = new Date();

        const year = now.getUTCFullYear();
        const month = now.getUTCMonth();
        const day = now.getUTCDate();

        const start = new Date(Date.UTC(year, month, day, 0, 0, 0));


        const end = new Date(start);
        end.setUTCDate(end.getUTCDate() + range);


        const startUnix = Math.floor(start.getTime() / 1000);
        const endUnix = Math.floor(end.getTime() / 1000);

        return { start: startUnix, end: endUnix };
    }

    onChangeSport(id: number, cb: () => void) {
        this.currentSportID = id;
        this.getGame(false, null, cb);
    }

    protected update(dt: number): void {
        if (this.currentTime > 0) {
            this.currentTime -= dt;
        } else {
            this.currentTime = this.defaultTime;
            this.sendGetBalance();
        }
    }

    backToLobby() {
        this.node.removeFromParent();
        App.instance.gotoLobby();
    }


}
