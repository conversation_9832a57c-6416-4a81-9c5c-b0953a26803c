import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import OceanSignalRClient from "../../Lobby/LobbyScript/Script/networks/OceanSignalRClient";
import { EffectCreator, EnumEffect, SlotPlayerResponse } from "../../Lobby/Slot/SlotConfig";
import { OceanGuide } from "./OceanGuide";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";
import { OceanPopupLSC } from "./OceanPopupLSC";
import { OceanPopupLSH } from "./OceanPopupLSH";
import { OceanSelectLine } from "./OceanSelectLine";
import OceanSlotMachine from "./SlotMachine/OceanSlotMachine";
import { InfoView } from "../../Lobby/Slot/InfoView";
import Configs from "../../Lobby/MoveScript/Configs";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import { PopupFreeSpin } from "./PopupFreeSpin";
import { OceanBonus } from "./OceanBonus";
import Http from "../../Lobby/MoveScript/Http";
import OceanTrial from "./OceanTrial";
import AudioManager, { AUDIO_CLIP } from "./AudioManager";
import { SlotIconEvent } from "../../Lobby/Common/SlotIconEvent";


export interface IOceanObserver {
    onChangeRoom(sender: OceanController, roomID: number): void;
}

const { ccclass, property, menu } = cc._decorator;

@ccclass("Ocean/OceanController")
export class OceanController extends cc.Component {
    private static sharedInstance?: OceanController;
    private observers: { [index: string]: IOceanObserver } = {};

    public static getInstance(): OceanController {
        return this.sharedInstance || (this.sharedInstance = new this());
    }

    public addObserver(key: string, observer: IOceanObserver) {
        this.observers[key] = observer;
    }

    public removeObserver(key: string) {
        delete this.observers[key];
    }

    public async dispatch(callback: (observer: IOceanObserver) => Promise<void>) {
        await Promise.all(Object.keys(this.observers).map(key => callback(this.observers[key])));
    }

    @property(OceanSlotMachine) slotMachine: OceanSlotMachine = null;

    @property(OceanPopupLSC) popupLSC: OceanPopupLSC = null;
    @property(OceanPopupLSH) popupLSH: OceanPopupLSH = null;
    @property(OceanGuide) popupGuide: OceanGuide = null;
    @property(OceanBonus) popupBonus: OceanBonus = null;
    @property(OceanSelectLine) popupSelectLine: OceanSelectLine = null;

    @property(cc.Node) mainGame: cc.Node = null;

    @property(cc.Node) footer: cc.Node = null;
    @property(cc.Node) back: cc.Node = null;

    @property(cc.Node) effectBigWin: cc.Node = null;
    @property(cc.Label) lblBigWinPrize: cc.Label = null;

    @property(cc.Node) effectJackpot: cc.Node = null;
    @property(cc.Label) lblJackpotPrize: cc.Label = null;
    @property(cc.Label) lblJackpotNum: cc.Label = null;

    @property(cc.Node) effectBonus: cc.Node = null;

    @property(cc.Node) effectFreeSpin: cc.Node = null;
    @property(cc.Label) lblFxFreeSpin: cc.Label = null;

    @property(cc.Label) lblTotalSelectedLine: cc.Label = null;
    @property(cc.Label) lblBet: cc.Label = null;
    @property(cc.Label) lblTotalAutoSpin: cc.Label = null;
    @property(cc.Label) lblFreeSpinCount: cc.Label = null;
    @property(cc.Label) lblTotalBet: cc.Label = null;
    @property(cc.Label) lblWinCash: cc.Label = null;
    @property(cc.Label) lblPayLine: cc.Label = null;
    @property(cc.Label) lblSession: cc.Label = null;
    @property(cc.Label) lblJackpotFund: cc.Label = null;
    @property(cc.Label) lblTrialBalance: cc.Label = null;
    @property(cc.Label) lblFreeTicket: cc.Label = null;

    @property(cc.Button) btnSpin: cc.Button = null;
    @property(cc.Button) btnSelectLine: cc.Button = null;
    @property(cc.Button) btnStopAutoSpin: cc.Button = null;
    @property(cc.Toggle) toggleAutoSpin: cc.Toggle = null;
    @property(cc.Toggle) toggleTurbo: cc.Toggle = null;
    @property(cc.Toggle) setting: cc.Toggle = null;
    @property(cc.Toggle) toggleTrial: cc.Toggle = null;
    @property(cc.Toggle) toggleMusic: cc.Toggle = null;
    @property(cc.Toggle) toggleSound: cc.Toggle = null;

    @property(InfoView) infor: InfoView = null;
    @property(cc.Animation) floatText: cc.Animation = null;
    @property(PopupFreeSpin) popupFreeSpin: PopupFreeSpin = null;
    // Jackpot multiplier
    @property(SlotIconEvent) eventIcon: SlotIconEvent = null;

    @property(AudioManager) audioManager: AudioManager = null;

    private _effectCreator: EffectCreator = null;
    private _totalFreeSpinPrize: number = 0;
    private oceanPlayerResponse: SlotPlayerResponse = null;

    private _roomID = 1;
    public get roomID() {
        return this._roomID;
    }
    public set roomID(value: number) {
        this._roomID = value;
        this.dispatch(async (observer: IOceanObserver) => {
            observer.onChangeRoom(this, value);
        });
        OceanSignalRClient.getInstance().send(
            'PlayNow', [{ "CurrencyID": Configs.Login.CurrencyID, "RoomID": value }],
            (data) => {
            }
        )
    }

    /**
     * Auto spin
     */
    private _currentAutoSpin: number = 0;
    public get currentAutoSpin(): number {
        return this._currentAutoSpin;
    }
    public set currentAutoSpin(value: number) {
        let isAuto = value > 0;
        this._currentAutoSpin = value;
        this.lblTotalAutoSpin.string = Utils.formatNumber(value);
        this.btnStopAutoSpin.node.active = isAuto;
        this.toggleAutoSpin.node.getChildByName("Dung").active = isAuto;
        this.toggleAutoSpin.node.getChildByName("Quay").active = !isAuto;
    }

    /**
     * Free spin
     */
    private _totalFreeSpin: number = 0;
    public get totalFreeSpin(): number {
        return this._totalFreeSpin;
    }
    public set totalFreeSpin(value: number) {
        this._totalFreeSpin = value;
        this.lblFreeSpinCount.string = value.toString();
        this.lblFreeSpinCount.node.parent.active = value > 0;
    }

    /**
     * Turbo
     */
    private _isTurbo: boolean = false;
    public get isTurbo(): boolean {
        return this._isTurbo;
    }
    public set isTurbo(value: boolean) {
        this._isTurbo = value;
        this.slotMachine.setTurbo(value);
    }

    /**
     * Is spinning
     */
    private _isSpinning: boolean = false;
    public get isSpinning(): boolean {
        return this._isSpinning;
    }
    public set isSpinning(value: boolean) {
        this._isSpinning = value;
        this.btnSpin.interactable = !value;
        this.btnSelectLine.interactable = !value;
        this.toggleAutoSpin.interactable = !value;
        this.toggleAutoSpin.isChecked = false;
        if (value) {
            this.stopAllEffect();
        }
    }

    private isTrial: boolean = false;
    private _trialBalance: number = 0;
    public get trialBalance(): number {
        return this._trialBalance;
    }
    public set trialBalance(value: number) {
        this._trialBalance = value;
        this.lblTrialBalance.string = Utils.formatNumber(value);
    }

    private _freeTicket = 0;
    public get freeTicket(): number {
        return this._freeTicket;
    }
    public set freeTicket(value: number) {
        this._freeTicket = value;
        this.lblFreeTicket.string = value.toString();
        this.lblFreeTicket.node.parent.active = value > 0;
    }

    onLoad() {
        OceanController.sharedInstance = this;
        this.initListeners();
        this.initEffects();
        this.initHubs();
        this.slotMachine.setTurbo(false);
        this.slotMachine.initializeReels();
    }

    start() {
        this.currentAutoSpin = 0;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        this.roomID = 1;
    }

    protected onEnable(): void {
        this.toggleMusic.isChecked = !this.audioManager.isMuteMusic;
        this.toggleSound.isChecked = !this.audioManager.isMuteEffect;
    }

    private initListeners() {
        this.toggleTurbo.node.on('click', () => this.isTurbo = !this.isTurbo);
        [10, 20, 50, 100, 500, 1000, 2000].forEach((value, index) => {
            this.toggleAutoSpin.checkMark.node.children[index].on('click', () => {
                this.audioManager.playEffect(AUDIO_CLIP.CLICK);
                this.toggleAutoSpin.isChecked = false;
                if (this.isTrial) {
                    this.showFloatText("me35");
                    return;
                }
                this.currentAutoSpin = value;
                this.onClickSpin();
            })
        })

        this.popupSelectLine.setOnSelectedChanged((selectedLines: number[]) => {
            const lineCount = selectedLines.length;
            this.lblTotalSelectedLine.string = lineCount.toString();
            this.lblTotalBet.string = Utils.formatNumber(lineCount * this.oceanPlayerResponse.BetValue);
        });

        BroadcastReceiver.register(BroadcastReceiver.USER_UPDATE_COIN, () => {
            this.infor.setInfo(Configs.Login.Nickname, Configs.Login.Avatar, Configs.Login.GoldCoin);
        }, this);

    }

    private initEffects() {
        this.effectBigWin.active = false;
        this.effectJackpot.active = false;
        this.effectBonus.active = false;
        this.effectFreeSpin.active = false;

        this._effectCreator = {
            [EnumEffect.BIGWIN]: {
                condition: () => {
                    return this.getPayLinePrize() >= 85 * this.oceanPlayerResponse.BetValue;
                },
                effect: async () => {
                    this.effectBigWin.active = true;
                    this.lblBigWinPrize.string = '0';
                    Tween.numberTo(this.lblBigWinPrize, this.getPayLinePrize(), 6);
                    this.audioManager.playEffect(AUDIO_CLIP.FX_BIGWIN);
                    this.audioManager.playEffect(AUDIO_CLIP.COUNTER, true);
                    this.scheduleOnce(() => {
                        this.audioManager.stopEffect(AUDIO_CLIP.COUNTER);
                    }, 6);
                    await this.playEffectAnimation(this.effectBigWin, 8);
                }
            },
            [EnumEffect.JACKPOT]: {
                condition: () => {
                    return this.oceanPlayerResponse.SpinData.IsJackpot;
                },
                effect: async () => {
                    this.effectJackpot.active = true;
                    this.lblJackpotPrize.string = '0';
                    this.lblJackpotNum.string = this.oceanPlayerResponse.SpinData.JackpotNum.toString();
                    Tween.numberTo(this.lblJackpotPrize, this.getPayLinePrize(), 8);
                    this.audioManager.playEffect(AUDIO_CLIP.JACKPOT);
                    this.audioManager.playEffect(AUDIO_CLIP.COUNTER, true);
                    this.scheduleOnce(() => {
                        this.audioManager.stopEffect(AUDIO_CLIP.COUNTER);
                    }, 8);
                    await this.playEffectAnimation(this.effectJackpot, 12);
                }
            },
            [EnumEffect.BONUS]: {
                condition: () => {
                    return this.oceanPlayerResponse.SpinData.BonusGameData !== "";
                },
                effect: async () => {
                    this.effectBonus.active = true;
                    this.audioManager.playEffect(AUDIO_CLIP.FX_BONUS);
                    await this.playEffectAnimation(this.effectBonus, 2);
                    await this.showBonusGame();
                }
            },
            [EnumEffect.FREESPIN]: {
                condition: () => {
                    return this.oceanPlayerResponse.SpinData.IsFreeSpin;
                },
                effect: async () => {
                    this.effectFreeSpin.active = true;
                    await this.playEffectAnimation(this.effectFreeSpin, 3);
                }
            }
        }
    }

    private initHubs() {
        OceanSignalRClient.getInstance().receive("UpdateJackPot", (data: number) => {
            cc.log("UpdateJackpot", data);
            Tween.numberTo(this.lblJackpotFund, data, 0.5);
        })
        OceanSignalRClient.getInstance().receive("MessageError", (data: number) => {
            cc.log("MessageError", data);
            this.showFloatText(`me${data}`);
        })
        OceanSignalRClient.getInstance().receive("JoinGame", (data: SlotPlayerResponse) => {
            cc.log("JoinGame", data);
            this.oceanPlayerResponse = data;
            this.lblSession.string = '';

            const lineCount = this.popupSelectLine.getSelectedLines().length;
            this.lblBet.string = Utils.formatNumber(data.BetValue);
            this.lblTotalBet.string = Utils.formatNumber(lineCount * data.BetValue);
            this.totalFreeSpin = data.SlotInfo.FreeSpins;
            this.updateMultiplierIcon();
            this.checkFreeTicket();

            if (this.totalFreeSpin > 0) {
                this.lblFxFreeSpin.string = data.SlotInfo.FreeSpins.toString();
                this._effectCreator[EnumEffect.FREESPIN].effect().then(() => {
                    this.onClickSpin();
                })
            }
        })
        OceanSignalRClient.getInstance().receive("ResultSpin", (data: any) => {
            //cc.log("ResultSpin", data);
            this.oceanPlayerResponse = data;
            this.lblSession.string = `#${data.SpinData.SpinID}`;

            this.slotMachine.resetAllItems();
            this.node.stopAllActions();
            this.audioManager.stopAllEffect();
            this.updateBalance();
            // this.isSpinning = true;
            this.audioManager.playEffect(AUDIO_CLIP.REEL_SPIN);
            this.slotMachine.startSpin(data.SpinData.SlotsData).then(() => {
                this.onSpinComplete();
            })
        })
        OceanSignalRClient.getInstance().receive("resultBonusGame", (data: any) => {
            cc.log("resultBonusGame", data);
            Configs.Login.GoldCoin = parseInt(data.Account.GoldBalance);
            BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
        })
    }

    /**
     * 
     * @returns Update gold balance for real game
     */
    private updateBalance() {
        if (this.isTrial) return;
        const gold = this.oceanPlayerResponse.Account.GoldBalance;
        if (gold < 0) return; // gold < 0, don't update balance
        Configs.Login.GoldCoin = gold;
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);
    }

    private _spin() {
        OceanSignalRClient.getInstance().send(
            'Spin', [{
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.roomID,
                "Lines": this.popupSelectLine.getSelectedLines().join(",")
            }],
            (data) => {
                if (data.c < 0) {
                    this.currentAutoSpin = 0;
                    this.isSpinning = false;
                    this.showFloatText(`me${data.c}`);
                }
            }
        )
    }

    private _spinWithTicket() {
        OceanSignalRClient.getInstance().send(
            'SpinForTicket', [{ 
                "CurrencyID": Configs.Login.CurrencyID,
                "RoomID": this.roomID,
                "Lines": this.popupSelectLine.getSelectedLines().join(",")
            }],
            (data) => {
                if (data.c < 0) {
                    this.currentAutoSpin = 0;
                    this.isSpinning = false;
                    this.showFloatText(`me${data.c}`);
                }
            }
        )
    }

    private finishBonusGame() {
        OceanSignalRClient.getInstance().send(
            'PlayBonusGame', [{
                "RoomID": this.roomID,
                "CurrencyID": Configs.Login.CurrencyID,
            }],
            (data) => {
                if (data.c < 0) {
                    this.showFloatText(`me${data.c}`);
                }
            }
        )
    }

    private onClickSpin() {
        this.isSpinning = true;
        if (this.isTrial) {
            this.lblSession.string = '';
            this.slotMachine.resetAllItems();
            this.node.stopAllActions();
            this.audioManager.stopAllEffect();
            let rIdx = Utils.randomRangeInt(0, OceanTrial.Result.length);
            this.oceanPlayerResponse.SpinData = OceanTrial.Result[rIdx];
            this.trialBalance -= 2000;
            this.audioManager.playEffect(AUDIO_CLIP.REEL_SPIN);
            this.slotMachine.startSpin(this.oceanPlayerResponse.SpinData.SlotsData).then(() => {
                this.onSpinComplete();
                this.trialBalance += this.getPayLinePrize();
            })
            return;
        }
        if (this.freeTicket > 0 && this.totalFreeSpin <= 0) {
            this._spinWithTicket();
        }
        else {
            this._spin();
        }
    }

    private onClickStopAutoSpin() {
        this.currentAutoSpin = 0;
    }

    private onToggleTrial() {
        if (this.isSpinning) return;
        this.isTrial = this.toggleTrial.isChecked;
        if (this.isTrial) {
            this.lblBet.string = '100';
            this.lblTotalSelectedLine.string = '20';
            this.lblTotalBet.string = '2.000';
            this.trialBalance = 10_000_000;
        }
        else {
            const betValue = this.oceanPlayerResponse.BetValue;
            const lineCount = this.popupSelectLine.getSelectedLines().length;
            this.lblBet.string = Utils.formatNumber(betValue);
            this.lblTotalSelectedLine.string = lineCount.toString();
            this.lblTotalBet.string = Utils.formatNumber(lineCount * betValue);
        }
        this.lblTrialBalance.node.parent.active = this.isTrial;
    }

    private onSpinComplete() {
        this.audioManager.stopEffect(AUDIO_CLIP.REEL_SPIN);
        const payLinePrize = this.getPayLinePrize();
        let isFreeSpinCompleted = false;
        this.lblWinCash.string = '0';
        const { payLines, commonPayLines } = this.parsePositionData(this.oceanPlayerResponse.SpinData.PositionData);
        const prizeData = this.parsePrizeData(this.oceanPlayerResponse.SpinData.PrizesData);
        let delay = 0.5;

        if (this.totalFreeSpin > 0) {
            this._totalFreeSpinPrize += payLinePrize;
            this.totalFreeSpin--;
            if (this.totalFreeSpin === 0) {
                isFreeSpinCompleted = true;
            }

        }
        else {
            if (this.freeTicket > 0) {
                this.freeTicket--;
            }
            if (this.currentAutoSpin > 0) {
                this.currentAutoSpin--;
            }
        }

        if (payLinePrize > 0) {
            delay = 1.5;
            this.showPayLinePrize(payLinePrize);
            this.slotMachine.highlightItems(commonPayLines).then();
            Tween.numberTo(this.lblWinCash, payLinePrize, 0.5);
        }

        this.node.runAction(
            cc.sequence(
                cc.delayTime(delay),
                cc.callFunc(() => {
                    this.showEffects(async () => {
                        this.isSpinning = false;
                        if (this.totalFreeSpin > 0) {
                            this.onClickSpin();
                            return;
                        }
                        if (isFreeSpinCompleted) {
                            await this.showPopupRewardFreeSpin();
                            this._totalFreeSpinPrize = 0;
                        }
                        if (this.currentAutoSpin > 0) {
                            this.onClickSpin();
                            return;
                        }
                        if (payLinePrize > 0) {
                            for (let i = 0; i < payLines.length; i++) {
                                await this.slotMachine.highlightItems(payLines[i]);
                            }
                        }
                    })
                })
            )
        )
    }

    private showEffects(finishCallback: Function) {
        let awaitable: Function[] = [];

        // Check and add jackpot or win effects (mutually exclusive)
        if (this._effectCreator[EnumEffect.JACKPOT].condition()) {
            this.currentAutoSpin = 0;
            awaitable.push(this._effectCreator[EnumEffect.JACKPOT].effect);
        } else if (this._effectCreator[EnumEffect.BIGWIN].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BIGWIN].effect);
        }

        if (this._effectCreator[EnumEffect.BONUS].condition()) {
            awaitable.push(this._effectCreator[EnumEffect.BONUS].effect);
        }

        if (this._effectCreator[EnumEffect.FREESPIN].condition()) {
            const newFreeSpin = this.oceanPlayerResponse.SpinData.TotalFreeSpin;
            if (this.totalFreeSpin <= 0) {
                this.lblFxFreeSpin.string = newFreeSpin.toString();
                awaitable.push(this._effectCreator[EnumEffect.FREESPIN].effect);
            }
            this.totalFreeSpin += newFreeSpin;
        }

        // Execute all effects in sequence
        (async () => {
            for (const aw of awaitable) {
                await aw();
            }
        })().then(() => {
            finishCallback();
        });
    }

    stopAllEffect() {
        cc.Tween.stopAllByTarget(this.effectJackpot);
        cc.Tween.stopAllByTarget(this.effectBigWin);
        cc.Tween.stopAllByTarget(this.effectFreeSpin);
        cc.Tween.stopAllByTarget(this.effectBonus);
        this.effectJackpot.active = false;
        this.effectBigWin.active = false;
        this.effectFreeSpin.active = false;
        this.effectBonus.active = false;
    }

    private parsePositionData(positionData: string): { payLines: number[][], commonPayLines: number[] } {
        const payLines = positionData.split(";").map(row => row.split(",").map(Number));
        const commonPayLines = Array.from(new Set(payLines.flat()));
        return { payLines, commonPayLines };
    }

    private parsePrizeData(prizeData: string): string[] {
        return prizeData.split(";");
    }

    private getPayLinePrize(): number {
        return this.oceanPlayerResponse.SpinData.PayLinePrizeValue;
    }

    private showPayLinePrize(prize: number) {
        if (this._effectCreator[EnumEffect.BIGWIN].condition()
            || this._effectCreator[EnumEffect.JACKPOT].condition()) return;
        this.audioManager.playEffect(AUDIO_CLIP.WIN);
        this.lblPayLine.string = '';
        Tween.numberTo(this.lblPayLine, prize, 0.75);
        this.lblPayLine.node.parent.active = true;
        this.scheduleOnce(() => {
            this.lblPayLine.node.parent.active = false;
        }, 1.5);
    }

    private async playEffectAnimation(effectNode: cc.Node, duration: number) {
        effectNode.stopAllActions();
        effectNode.active = true;
        effectNode.opacity = 0;
        effectNode.scale = 0.5;
        cc.tween(effectNode)
            .to(0.5, { opacity: 255, scale: 1 }, { easing: 'backOut' })
            .start();
        await new Promise<void>(resolve => {
            this.scheduleOnce(() => {
                cc.tween(effectNode)
                    .to(0.2, { opacity: 0 })
                    .call(() => {
                        effectNode.active = false;
                        resolve();
                    })
                    .start();
            }, duration);
        });
    }

    // show popup reward free spin
    private showPopupRewardFreeSpin() {
        return this.popupFreeSpin.showPopup(this._totalFreeSpinPrize);
    }

    async showBonusGame() {
        await new Promise<void>(resolve => {
            this.popupBonus.onFinished = () => {
                
                this.finishBonusGame();
                resolve();
            }
            this.audioManager.playEffect(AUDIO_CLIP.ENTER_BONUS);
            this.popupBonus.showBonus(this.oceanPlayerResponse.SpinData.BonusGameData, this.oceanPlayerResponse.SpinData.StartBonus);
        });
    }

    updateMultiplierIcon() {
        this.getRoomMultiplier(this.roomID).then((multiplier) => {
            this.eventIcon.node.active = multiplier > 0;
            this.eventIcon.setIcon(multiplier, 0);
        });
    }

    async getRoomMultiplier(roomID: number): Promise<number> {
        return new Promise((resolve) => {
            Http.get(
                Configs.App.DOMAIN_CONFIG['GetListJackpot'],
                { CurrencyID: Configs.Login.CurrencyID },
                (status, res) => {
                    // Nếu request không thành công hoặc không có dữ liệu
                    if (status !== 200 || !res?.d) {
                        resolve(0);
                        return;
                    }

                    // Tìm item đầu tiên thỏa mãn điều kiện
                    const item = res.d.find(item =>
                        item.gameID === Configs.GameId88.Ocean &&
                        item.roomID === roomID &&
                        item.nextJackpot === 0
                    );

                    // Trả về multiplier nếu tìm thấy, ngược lại trả về 0
                    resolve(item ? item.multiplier : 0);
                }
            );
        });
    }

    checkFreeTicket() {
        Http.get(Configs.App.DOMAIN_CONFIG['GetAccountTicket'], { CurrencyID: Configs.Login.CurrencyID, GameID: Configs.GameId88.Ocean }, (status, res) => {
            if (status === 200) {
                const data = res.d.filter(item => item.roomID === this.roomID);
                const count = data.reduce((sum, item) => sum + item.balance, 0);
                this.freeTicket = count;
            }
        })
    }

    showSelectLine() {
        if (this.isTrial) {
            this.showFloatText("me35");
            return;
        }
        this.setting.isChecked = false;

        this.mainGame.active = false;
        this.footer.active = false;
        this.back.active = true;

        this.popupLSC.dismiss();
        this.popupLSH.dismiss();
        this.popupGuide.dismiss();
        this.popupSelectLine.show();
    }

    showGuide() {
        if (this.isSpinning) return;
        this.setting.isChecked = false;

        this.mainGame.active = false;
        this.footer.active = false;
        this.back.active = true;

        this.popupLSC.dismiss();
        this.popupLSH.dismiss();
        this.popupGuide.show();
        this.popupSelectLine.dismiss();

        this.popupGuide.onRoomChange(this.roomID);
    }

    showWinners() {
        if (this.isSpinning) return;
        this.setting.isChecked = false;

        this.mainGame.active = false;
        this.footer.active = false;
        this.back.active = true;

        this.popupLSC.dismiss();
        this.popupLSH.show();
        this.popupGuide.dismiss();
        this.popupSelectLine.dismiss();
    }

    showHistory() {
        if (this.isSpinning) return;
        this.setting.isChecked = false;

        this.mainGame.active = false;
        this.footer.active = false;
        this.back.active = true;

        this.popupLSC.show();
        this.popupLSH.dismiss();
        this.popupGuide.dismiss();
        this.popupSelectLine.dismiss();
    }

    onClickSound() {
        this.audioManager.muteEffect(!this.audioManager.isMuteEffect);
    }

    onClickMusic() {
        this.audioManager.muteMusic(!this.audioManager.isMuteMusic);
    }

    onClickBackButton() {
        this.mainGame.active = true;
        this.footer.active = true;
        this.back.active = false;

        this.popupLSC.dismiss();
        this.popupLSH.dismiss();
        this.popupGuide.dismiss();
        this.popupSelectLine.dismiss();
    }

    showFloatText(text: string) {
        this.floatText.node.active = true;
        this.floatText.node.getComponent(cc.Label).string = App.instance.getTextLang(text);
        this.floatText.play();
        this.floatText.on('finished', () => {
            this.floatText.node.active = false;
        })
    }


    private onClickRoomID(button: cc.Button, roomID: string) {
        const ID = parseInt(roomID);
        this.roomID = ID;
        this.popupSelectLine.chooseRoom(ID);
    }

    backToLobby() {
        OceanSignalRClient.getInstance().dontReceive();
        App.instance.bigSlotGame[Configs.GameId88.Ocean] = null;
        this.node.parent.removeFromParent();
        App.instance.gotoLobby();
    }

    actHidden() {
        this.setting.isChecked = false;
        if(this.currentAutoSpin > 0){ 
            App.instance.showConfirmDialog(
                App.instance.getTextLang("sl74"),
                () => {
                    this.node.parent.setPosition(cc.v3(10000, 0, 0));
                },
                true
            );
        }
        else {
            App.instance.ShowAlertDialog(App.instance.getTextLang("sl90"));
        }
    }

}
