import BundleControl from "../../Loading/src/BundleControl";
import ChatInGame from "../../Lobby/ChatInGame/ChatInGame";
import App from "../../Lobby/LobbyScript/Script/common/App";
import CardGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/CardGameSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import { BaCayDataService } from "./BaCayDataService";
import BaCayUIController from "./BaCayUIController";
import HUDController from "./HUDController";
import SlotItemController from "./SlotItemController";
import { convertObjectToArray, loadBundleBaCay } from "./BaCayUltils";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Label from "../../Lobby/LobbyScript/Script/common/Language.Label";

const { ccclass, property, menu } = cc._decorator;

@ccclass
@menu("BaCay/Controller")
export default class BaCayController extends cc.Component {
    public static Instance: BaCayController;

    @property(HUDController)
    HUD: HUDController;

    @property(SlotItemController)
    PlayerList: SlotItemController[] = [];

    @property(cc.Sprite)
    dealer: cc.Sprite;

    @property(cc.Node)
    Detail: cc.Node;

    @property(cc.Node)
    NoityPlayerCount: cc.Node;

    @property(cc.ParticleSystem)
    Effect: cc.ParticleSystem;

    spriteFramesDe: cc.SpriteFrame[] = [];
    spriteFramesNo: cc.SpriteFrame[] = [];

    private cards: cc.SpriteFrame[] = [];
    private currentCardsMatch: cc.SpriteFrame[] = [];
    private currentGame: number = 0;

    protected onLoad(): void {
        BaCayController.Instance = this;
    }

    private currentPlayersList: any[] = [];
    private currentPlayerData: any;
    Player: SlotItemController;
    private isBetting: boolean = false;
    private isPlaying: boolean = false;
    private isQuitting: boolean = false;
    private isShowCard: boolean = false;
    private countdown: number = 60;
    private _flagChceckOnilne: boolean = false;

    protected start(): void {
        loadBundleBaCay(() => {
            var bundle = cc.assetManager.getBundle("ba_cay");

            bundle.loadDir("res/normal", cc.Texture2D, (err, textures) => {
                if (err) {
                    console.error("Lỗi khi tải ảnh:", err);
                    return;
                }

                // Chuyển texture thành SpriteFrame
                this.spriteFramesNo = textures.map((texture: cc.Texture2D) => {
                    return new cc.SpriteFrame(texture);
                });

                // Gọi hàm phát animation
                this.playAnimationnormal();
            });

            bundle.loadDir("res/chiabai", cc.Texture2D, (err, textures) => {
                if (err) {
                    console.error("Lỗi khi tải ảnh:", err);
                    return;
                }

                // Chuyển texture thành SpriteFrame
                this.spriteFramesDe = textures.map((texture: cc.Texture2D) => {
                    return new cc.SpriteFrame(texture);
                });

            });

            bundle.loadDir("res/Card/", cc.SpriteFrame, (err, spriteFrames: cc.SpriteFrame[]) => {
                if (err) {
                    console.error("Failed to load atlas:", err);
                    return;
                }
                this.cards = spriteFrames
            });
        })

        const data = App.instance.DataPass[0];

        BaCayDataService.Name = data.Name;
        BaCayDataService.IsVip = data.MoneyType;
        BaCayDataService.Amount = data.MinBet;

        BaCayDataService.MinBet = data.MinBet;
        BaCayDataService.MaxBet = data.MaxBet;

        if (this.HUD)
            this.HUD.setInfoLabel(this.currentGame);

        this.onHandleJoin(App.instance.DataPass[0], App.instance.DataPass[1]);
        if (this.Detail) {
            this.Detail.active = true;
            const label = this.Detail.getChildByName('Label').getComponent(cc.Label);
            if (label) {
                label.string = App.instance.getTextLang('me7')
            }
        }
        cc.log('Init:', App.instance.DataPass[0]);

        CardGameSignalRClient.getInstance().receive('playerJoin', (data: any) => {
            this.playerJoin(data);
        });

        CardGameSignalRClient.getInstance().receive('startGame', (data: any) => {
            this.startGame(data);
            this.currentGame++;
            // cc.log('Start:', data);
            this.PlayerList.forEach(e => {
                e.reset();
            })

            this.HUD.setInfoLabel(this.currentGame);
            this.isBetting = false;
            this.currentCardsMatch = this.cards;


            if (this.Detail) {
                this.Detail.active = true;
                const label = this.Detail.getChildByName('Label').getComponent(cc.Label);
                if (label) {
                    label.string = App.instance.getTextLang('ca110')
                }
            }

            if (this.NoityPlayerCount) {
                this.NoityPlayerCount.active = data.CountPlayers <= 1;
            }
        });


        CardGameSignalRClient.getInstance().receive('startBettingTime', (data: any) => {
            this.playAnimationDealer();
            cc.log('=========>StartBetting: ', data);
            this.startGame(data);
            this.scheduleOnce(() => {

                this.PlayerList.forEach(e => {

                    if (e.Data && e.IsFlag && e.Data.id === this.currentPlayerData.AccountID) {
                        if (this.HUD) {
                            this.HUD.activeInput(true);
                        }
                        e.showCircleCountdown(data.BetStep);
                    }

                    if (e.Data && !e.IsFlag && e.Data.id === this.currentPlayerData.AccountID) {
                        if (this.HUD) {
                            this.HUD.activeBien(true, data.CountActivePlayer >= 2)
                        }
                    }

                }
                )
                this.showOtherPlayerBien();
            }

                , 0.5)


            if (data) {
                if (this.Detail) {
                    this.Detail.active = true;
                    const label = this.Detail.getChildByName('Label').getComponent(cc.Label);
                    if (label) {
                        label.string = App.instance.getTextLang('me25')
                    }
                }
            }
        });

        CardGameSignalRClient.getInstance().receive('startAnimationTime', (data: any) => {
            if (data) {
                if (this.HUD) {
                    this.HUD.activeInput(false);
                    this.HUD.activeBien(false);
                }
                this.isPlaying = true;

                this.getCardUnflip(data);
                this.scheduleOnce(() => {
                    if (this.isBetting) {
                        this.sendFlipCard();
                    }
                }, 1.0)
            }

        });

        CardGameSignalRClient.getInstance().receiveArray('updateBetting', (data: any, money: any) => {
            if (!data) return;

            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === data.AccountID) {
                    e.updateAmount(BaCayDataService.IsVip ? data.GoldBalance : data.CoinBalance);
                    e.showBet(money)
                }
            })
        });

        CardGameSignalRClient.getInstance().receiveArray('flipCards', (data: any, cards: any) => {
            if (!data) return;
            if (this.isShowCard) return;
            let sprites = [];
            if (this.Detail) {
                this.Detail.active = false;

            }
            this.isShowCard = true;
            if (cards.handCards && cards.handCards.length) {
                cards.handCards.forEach(e => {
                    let cardNum = e.cardNumber;
                    if (cardNum == 1) {
                        cardNum = 'A';
                    }
                    let spr = this.getCardByName(`${cardNum}`, e.cardSuite);

                    sprites.push(spr);
                })
            }
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === data) {
                    if (this.currentPlayerData.AccountID == data && cards.sum > 0) {
                        BaCayUIController.Instance.showOpenCard(sprites, () => {
                            e.showCard(cards.handCards, sprites, cards?.sum);
                        });
                    } else {
                        e.showCard(cards.handCards, sprites, cards?.sum);
                    }
                }
            })
        });


        CardGameSignalRClient.getInstance().receive('updateSession', (data: any) => {
            cc.log('End:', data);
            const newArr = convertObjectToArray(data.Players);
            const listPlayer = [
                ...newArr.filter(item => item.Account.NickName.split('[X]')[1] === Configs.Login.Nickname),
                ...newArr.filter(item => item.Account.NickName.split('[X]')[1] !== Configs.Login.Nickname)
            ];
            this.currentPlayersList = listPlayer;
            this.scheduleOnce(() => {
                this.currentPlayersList = listPlayer;
                if (listPlayer && listPlayer.length) {
                    this.PlayerList.forEach((e, i) => {

                        if (listPlayer[i]) {
                            e.setInfo(listPlayer[i].Account.AccountID, listPlayer[i].Account.NickName, BaCayDataService.IsVip ? listPlayer[i].Account.GoldBalance : listPlayer[i].Account.CoinBalance, listPlayer[i].Account.AvatarID, listPlayer[i].RoleInGame);
                            let sprites = [];
                            e.showWin(listPlayer[i].Hand.Rank === 1);
                            if (listPlayer[i].Hand.Rank === 1 &&
                                listPlayer[i].Account.AccountID === this.currentPlayerData.AccountID) {
                                this.HUD.showWinPopUp();
                                if (this.Effect) {
                                    this.Effect.node.active = true;
                                    this.Effect.resetSystem();
                                    this.scheduleOnce(() => {
                                        this.Effect.node.active = false;
                                    }, 6)
                                }

                            }

                            if (listPlayer[i].Hand.HandCards && listPlayer[i].Hand.HandCards.length) {

                                listPlayer[i].Hand.HandCards.forEach(e => {
                                    let cardNum = e.CardNumber;
                                    if (cardNum == 1) {
                                        cardNum = 'A';
                                    }
                                    let spr = this.getCardByName(`${cardNum}`, e.CardSuite);
                                    sprites.push(spr);
                                })
                                e.showCard(listPlayer[i].Hand.HandCards, sprites, listPlayer[i].Hand?.Sum)
                            }
                        }
                    })
                }
            })
            this.isPlaying = false;
            this.isShowCard = false;
            if (this.Detail) {
                this.Detail.active = true;
                const label = this.Detail.getChildByName('Label').getComponent(cc.Label);
                if (label) {
                    label.string = App.instance.getTextLang('me6')
                }
            }

            this.scheduleOnce(() => {
                this.PlayerList.forEach(e => {
                    e.reset();
                })
            }, 2)
            if (this.isQuitting) {
                this.sendExit();
                cc.log('QUIT');
            }
        });


        CardGameSignalRClient.getInstance().receiveArray('betOther', (fromAccount: any, toAccount: any, amount: any) => {
            if (toAccount !== this.currentPlayerData.AccountID) return;
            let name = '';
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === fromAccount) {
                    name = e.Data.name;
                }
            })
            BaCayUIController.Instance.showDialog(App.instance.getTextLang('txt_game_bacay_bet_request') + amount, () => {
                this.sendBetAccept(toAccount, true);
            }, () => {
                this.sendBetAccept(toAccount, false);
            })
        });


        CardGameSignalRClient.getInstance().receiveArray('feedChicken', (data: any, money: any) => {
            if (!data) return;
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === data) {
                    e.showChicken();
                }
            })
        });

        CardGameSignalRClient.getInstance().receive('playerLeave', (data: any) => {
            if (!data) return;
            cc.log('LEAVE: ', data);
            if (this.currentPlayerData.AccountID === data) {
                if (data && data.code == 11007) {
                    var stringaler = App.instance.getTextLang("me11007");
                    App.instance.ShowAlertDialog(Utils.formatString(stringaler, Utils.formatNumber(data.prms[0]), data.prms[0] == 1 ? App.instance.getTextLang("hi25") : App.instance.getTextLang("TLN_COIN")));
                }
                this.backToLobby();
                if (this.isPlaying) {
                    this.isQuitting = true;
                }
                else {
                    this.node.removeFromParent();
                    App.instance.gotoLobby();
                }

            }
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === data) {
                    if (this.isPlaying) {
                        e.showQuit();
                    }
                    else {
                        e.setEmpty();
                    }
                    App.instance.showToast(App.instance.getTextLang('me11004').replace('{0}', e.Data?.name || ''));
                }
            })
        });

        CardGameSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === accountId) {
                    e.showChatMsg(content);
                }
            })
        });

        CardGameSignalRClient.getInstance().receiveArray('changeOwner', (accountId: string) => {
            cc.log('Change Owner: ', accountId)
            if (this.currentPlayerData.AccountID === accountId) {
                BaCayUIController.Instance.showDialogOwner(App.instance.getTextLang('ca25'), () => {
                    this.sendBuyOwner();
                }, () => {
                    this.sendSellOwner(false);
                })
            }
            this.PlayerList.forEach(e => {
                if (e.Data && e.Data.id === accountId) {
                    e.showFlag();
                }
            })
        });

        CardGameSignalRClient.getInstance().receiveArray('askToSell', (timer) => {
            cc.log('Ask to sell:', timer);
        });

        CardGameSignalRClient.getInstance().receiveArray('askOtherToBuy', (timer, seller, minBet) => {
            cc.log('Timmer: ', timer);
            cc.log('Seller: ', seller);
            cc.log('Min Bet: ', minBet);
        });

        if (this.HUD) {
            this.HUD.setAllowBetAction((accept: boolean) => {
                this.PlayerList.forEach(e => {
                    if (e.Data && e.Data.id && e.Data.id != this.currentPlayerData.AccountID) {
                        this.sendBetAccept(e.Data.id, accept);
                        e.deActiveBien();
                    }
                })
            })

            this.HUD.setMinBetAll(() => {
                this.PlayerList.forEach(e => {
                    if (e.Data && e.Data.id && e.Data.id != this.currentPlayerData.AccountID) {
                        this.sendBetOther(e.Data.id, BaCayDataService.MinBet);
                        e.deActiveBien();
                    }
                })
            })

            this.HUD.setMaxBetAll(() => {
                this.PlayerList.forEach(e => {
                    if (e.Data && e.Data.id && e.Data.id != this.currentPlayerData.AccountID) {
                        this.sendBetOther(e.Data.id, BaCayDataService.MaxBet);
                        e.deActiveBien();
                    }
                })
            })

            this.HUD.setFeedChicken(() => {
                this.sendFeedChicken();
            })
        }
    }

    startGame(data: any) {
        const listPlayer = convertObjectToArray(data.Players);
        BaCayDataService.PlayerActive = listPlayer.length
        const newArr = [
            ...listPlayer.filter(item => item.Account.NickName.split('[X]')[1] === Configs.Login.Nickname),
            ...listPlayer.filter(item => item.Account.NickName.split('[X]')[1] !== Configs.Login.Nickname)
        ];
        this.currentPlayersList = newArr;
        this.scheduleOnce(() => {
            this.updatePlayerList(this.currentPlayersList);
        })


    }


    getCardUnflip(data) {
        const listPlayer = convertObjectToArray(data.Players);
        this.currentPlayersList = listPlayer;
        const flip = this.getCardByName('Flip', -1);

        if (listPlayer && listPlayer.length) {
            this.PlayerList.forEach((e, i) => {
                if (listPlayer[i]) {
                    e.deActiveBien();
                    const textures = [flip, flip, flip]
                    e.showCard([], textures, listPlayer[i]?.Hand?.sum || 0);
                }
            })
        }
    }

    sendSellOwner(result: boolean) {
        CardGameSignalRClient.getInstance().send('SellOwner', [
            result
        ], (data) => {

        });
    }

    sendBuyOwner() {
        CardGameSignalRClient.getInstance().send('BuyOwner', [

        ], (data) => {

        });
    }

    sendBetValue() {
        if (this.HUD.CurrentBetValue === 0) return;
        CardGameSignalRClient.getInstance().send('Bet', [
            this.HUD.CurrentBetValue
        ], (data) => {

            this.isBetting = true;
            this.HUD.onSendBetValue();
            if (this.Player) this.Player.showBet(this.HUD.CurrentBetValue);
        });
    }

    sendFlipCard() {
        CardGameSignalRClient.getInstance().send('Flip', [

        ], (data) => {

        });
    }



    showOtherPlayerBien() {
        this.PlayerList.forEach((e) => {
            if (e.Data && e.Data.id !== this.currentPlayerData.AccountID) {
                e.showBien([BaCayDataService.MinBet, BaCayDataService.MaxBet], (toAccount: string, amount: number) => {
                    this.sendBetOther(toAccount, amount);
                })
            }
        })
    }

    updatePlayerList(players: any[], viewer: boolean = false) {
        if (players && players.length) {
            this.PlayerList.forEach((e, i) => {
                if (players[i]) {
                    if (players[i].Account.OriginNickname === Configs.Login.Nickname) {
                        this.Player = e;
                        this.currentPlayerData = players[i].Account;
                    }
                    this.sendBetAccept(players[i].Account.AccountID, true);
                    e.setInfo(players[i].Account.AccountID, players[i].Account.NickName, BaCayDataService.IsVip ? players[i].Account.GoldBalance : players[i].Account.CoinBalance, players[i].Account.AvatarID, players[i].RoleInGame);
                    e.setViewer(viewer)
                } else {
                    e.setEmpty();
                }
            })
        }
    }

    onHandleJoin(data, extra) {
        this._flagChceckOnilne = true;
        const listPlayer = convertObjectToArray(data.Players);
        const newArr = [
            ...listPlayer.filter(item => item.Account.NickName.split('[X]')[1] === Configs.Login.Nickname),
            ...listPlayer.filter(item => item.Account.NickName.split('[X]')[1] !== Configs.Login.Nickname)
        ];

        this.currentPlayersList = newArr;
        this.scheduleOnce(() => {
            this.updatePlayerList(this.currentPlayersList, true);
        })

        if (this.NoityPlayerCount) {
            this.NoityPlayerCount.active = data.CountPlayers <= 1;
        }

    }

    playerJoin(data) {
        this.currentPlayersList.push(data);
        const listPlayer = convertObjectToArray(this.currentPlayersList);
        const newArr = [
            ...listPlayer.filter(item => item.Account.NickName.split('[X]')[1] === Configs.Login.Nickname),
            ...listPlayer.filter(item => item.Account.NickName.split('[X]')[1] !== Configs.Login.Nickname)
        ];
        this.currentPlayersList = newArr;
        this.scheduleOnce(() => {
            this.updatePlayerList(this.currentPlayersList);
        })

    }


    getCardByName(cardNum: string, cardSuite: number): cc.SpriteFrame {
        const cardSuiteTextMap: { [key: number]: string } = {
            9: "club",
            18: "heart",
            0: "spade",
            27: "diamond"
        };
        const cardName = cardNum === 'Flip' || cardSuiteTextMap[cardSuite] === undefined ? 'Flip' : `${cardSuiteTextMap[cardSuite]}-${cardNum}`
        for (let spr of this.currentCardsMatch) {
            if (spr.name === cardName) {
                return spr;
            }

        }
        return null;
    }

    playAnimationDealer() {
        var thiz = this;
        let frameIndex = 0;
        if (thiz.spriteFramesDe.length <= 0) return;
        const finishDealer = () => {
            cc.log("Animation completed!");
            thiz.playAnimationnormal();
        };
        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesDe[frameIndex];
            frameIndex = (frameIndex + 1) % thiz.spriteFramesDe.length;
            frameIndex++;
            if (frameIndex >= this.spriteFramesDe.length - 1) {
                // Gọi callback khi hoàn thành
                this.dealer.unscheduleAllCallbacks();
                finishDealer();
            }

        }, 1 / 30, this.spriteFramesDe.length - 1);

    }


    playAnimationnormal() {
        this.dealer.node.setPosition(new cc.Vec3(0, 130, 0));
        var thiz = this;
        let index = 0;
        let forward = true; // Biến kiểm soát hướng
        const frameRate = 1 / 30; // 100ms mỗi frame
        if (thiz.spriteFramesNo.length <= 0) return;
        // Hủy mọi schedule trước khi đặt mới
        this.dealer.unscheduleAllCallbacks();

        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesNo[index];

            // Nếu đến cuối mảng, đảo chiều
            if (index === thiz.spriteFramesNo.length - 1) {
                forward = false;
            }
            // Nếu đến đầu mảng, đổi hướng đi tiếp
            else if (index === 0) {
                forward = true;
            }

            // Cập nhật index theo hướng
            index += forward ? 1 : -1;
        }, frameRate, cc.macro.REPEAT_FOREVER);
        // 
        // let index = 0;
        // const frameRate = 0.3; // 100ms mỗi frame
        // this.dealer.unscheduleAllCallbacks(); 
        // // Lặp lại animation
        // this.dealer.schedule(() => {
        //     this.dealer.spriteFrame = thiz.spriteFramesNo[index];
        //     index = (index + 1) % thiz.spriteFramesNo.length; // Vòng lặp animation
        // },frameRate, cc.macro.REPEAT_FOREVER);
    }

    backToLobby() {
        if (this.isQuitting) return;
        this.sendLeave();

    }

    sendBetOther(toAccount: string, amount: number) {
        this.countdown = 60;
        cc.log('Send Bet Others: ', toAccount, amount);
        CardGameSignalRClient.getInstance().send('BetOthers', [
            toAccount,
            amount
        ], (data) => {
            cc.log('Bet Other Response', data);
            if (data) {
                this.PlayerList.forEach((e) => {
                    if (e.Data.id === toAccount) {
                        e.deActiveBien();
                    }
                })
            } else {
                App.instance.showToast(data ? App.instance.getTextLang("me0") : App.instance.getTextLang("txt_bet_error2"))
            }
        });
    }




    sendBetAccept(fromAccount: string, accepted: boolean) {
        this.countdown = 60;
        CardGameSignalRClient.getInstance().send('AcceptBet', [
            fromAccount,
            accepted
        ], (data) => {

        });
    }

    sendFeedChicken() {
        this.countdown = 60;
        CardGameSignalRClient.getInstance().send('FeedChicken', [

        ], (data) => {

        });
    }

    sendExit() {
        this.countdown = 60;
        CardGameSignalRClient.getInstance().send('ExitLobby', [

        ], (data) => {
            this.node.removeFromParent();
            App.instance.gotoLobby();
        });
    }

    sendLeave() {
        this.countdown = 60;
        CardGameSignalRClient.getInstance().send('LeaveGame', [

        ], (data) => {
            this.isQuitting = true;
        });
    }



    chatIngame: ChatInGame = null;
    actChat() {
        this.countdown = 60;
        App.instance.inactivityTimer = 0;
        if (this.chatIngame == null) {
            let cb = (prefab) => {
                this.chatIngame = cc.instantiate(prefab).getComponent("ChatInGame");
                this.HUD.node.addChild(this.chatIngame.node);
                this.chatIngame.show(Configs.GameId88.BaCay);

            };
            BundleControl.loadPrefabPopup("PrefabPopup/ChatInGame", cb);
        } else {
            this.chatIngame.show(Configs.GameId88.BaCay);
        }

    }

    protected update(dt: number): void {
        if (this._flagChceckOnilne) {
            this.countdown -= dt;
            if (this.countdown <= 0) {
                this._flagChceckOnilne = false;

                BaCayUIController.Instance.showDialogOK(App.instance.getTextLang('ca247'), () => {
                    this.backToLobby();
                })
            }
        }
    }

}
