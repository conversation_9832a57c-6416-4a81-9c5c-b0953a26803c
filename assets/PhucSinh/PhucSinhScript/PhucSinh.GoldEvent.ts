import { X2Info } from "./PhucSinh.Controller";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GoldEvent extends cc.Component {
    @property(cc.RichText)
    lblContent: cc.RichText = null;

    updateJackpotEvent(X2Info: X2Info) {
        this.node.active = this.shouldShowEvent();
        let lbl1 = '';
        let lbl2 = '';
        let lbl3 = '';
        if (X2Info.Status == 1) {
            lbl1 = X2Info.Jackpot_Waiting > 0 ? 'X2 sau' : 'Còn';
            lbl2 = (X2Info.Jackpot_Waiting || X2Info.JackpotX2_Remain).toString();
            lbl3 = X2Info.Jackpot_Waiting > 0 ? 'Jackpot' : '(X2)';
        }
        else {
            lbl1 = X2Info.Jackpot_Waiting > 0 ? 'X2 sau' : 'Còn';
            lbl2 = this.getTime(X2Info.NextEventDateTime);
            lbl3 = this.getDay(X2Info.NextEventDateTime);

        }
        this.lblContent.string = `<color=#C30000>${lbl1}</c>\n<color=#000000>${lbl2}</c>\n<color=#1400FF>${lbl3}</c>`;
    }

    shouldShowEvent(): boolean {
        // show iff Tuedays and Saturdays
        let day = new Date().getDay();
        return day == 2 || day == 6;
    }

    private getTime(z: string) {
        const date: Date = new Date(z);

        // Format to HH:mm
        const time: string = date.toLocaleTimeString("en-GB", {
            hour: "2-digit",
            minute: "2-digit",
        });
        return time;
    }

    private getDay(z: string) {
        const date: Date = new Date(z);
        // Format to dd/MM/yyyy
        const formattedDate: string = date.toLocaleDateString("en-GB");

        return formattedDate;   // ➤ Date: 30/06/2025
    }
}
