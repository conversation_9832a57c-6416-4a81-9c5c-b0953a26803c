import MiniGame from "../../Lobby/LobbyScript/MiniGame";
import { PopupGuide } from "./PhucSinh.PopupGuide";
import { PopupHistory } from "./PhucSinh.PopupHistory";
import { PopupHonors } from "./PhucSinh.PopupHonors";
import { PopupSelectLine } from "./PhucSinh.PopupSelectLine";
import App from "../../Lobby/LobbyScript/Script/common/App";
import MiniGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/MiniGameSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import BroadcastReceiver from "../../Lobby/LobbyScript/Script/common/BroadcastReceiver";
import Tween from "../../Lobby/LobbyScript/Script/common/Tween";
import LanguageLabel from "../../Lobby/LobbyScript/Script/common/Language.Label";

const { ccclass, property } = cc._decorator;

@ccclass
class LineReview {
    @property(cc.Node)
    dot: cc.Node = null;

    @property(cc.Node)
    line: cc.Node = null;

    start() {
        this.dot.on(cc.Node.EventType.MOUSE_ENTER, () => {
            this.line.active = true;
        });
        this.dot.on(cc.Node.EventType.MOUSE_LEAVE, () => {
            this.line.active = false;
        })
    }
}

@ccclass
export default class PhucSinhController extends MiniGame {
    @property(cc.Label)
    private lblJackpotValue: cc.Label = null;

    @property(cc.Prefab) private popupGuidePrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupHistoryPrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupHonorPrefab: cc.Prefab = null;
    @property(cc.Prefab) private popupSelectLinePrefab: cc.Prefab = null;

    @property(cc.Node)
    private popupContainer: cc.Node = null;

    @property(cc.Node) private dots: cc.Node = null;
    @property(cc.Node) private lines: cc.Node = null;

    @property([cc.Button])
    private betButtons: cc.Button[] = [];

    @property(cc.Toggle)
    private currencyButton: cc.Toggle = null;

    @property(cc.Button)
    private btnSelectLine: cc.Button = null;

    @property(cc.Button)
    private btnAutoSpin: cc.Button = null;

    @property(cc.Toggle)
    private btnTurbo: cc.Toggle = null;

    @property(cc.Button)
    private btnSpin: cc.Button = null;

    @property(cc.Label)
    private lblTotalLine: cc.Label = null;

    @property(LanguageLabel)
    lblButtonAuto: LanguageLabel = null;

    @property(cc.Node) effectNormalWin: cc.Node = null;
    @property(cc.Node) effectBigWin: cc.Node = null;
    @property(cc.Node) effectJackpot: cc.Node = null;
    @property(cc.Node) effectX2Win: cc.Node = null;

    @property(cc.Label) lblWinCash: cc.Label = null;
    @property(cc.Label) lblSession: cc.Label = null;

    @property(cc.Node) reels: cc.Node = null;

    @property(cc.Node) symbolTemplate: cc.Node = null;

    @property([cc.SpriteFrame]) symbol: cc.SpriteFrame[] = [];

    private popupGuide: PopupGuide = null;
    private popupHistory: PopupHistory = null;
    private popupHonor: PopupHonors = null;
    private popupSelectLine: PopupSelectLine = null;
    /**
     * 1-Gold, 2-Xu
     * @private
     */
    private _betType: number = 1;
    public get betType(): number {
        return this._betType;
    }
    public set betType(value: number) {
        this._betType = value;
        this.invokeGetJackpot();
        switch (this._betType) {
            case 1:
                this.setBetValue(this.betButtons[0], 100);
                this.setBetValue(this.betButtons[1], 1000);
                this.setBetValue(this.betButtons[2], 10000);
                break;
            case 2:
                this.setBetValue(this.betButtons[0], 1000);
                this.setBetValue(this.betButtons[1], 10000);
                this.setBetValue(this.betButtons[2], 100000);
                break;
        }
    }
    // from 1 to 20
    private currentSelectedLine: number[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];
    private itemHeight: number;

    private _isAuto: boolean = false;
    public get isAuto(): boolean {
        return this._isAuto;
    }
    public set isAuto(value: boolean) {
        this._isAuto = value;
        this.btnAutoSpin.node.getChildByName("Dung").active = value;
        this.btnAutoSpin.node.getChildByName("TuQuay").active = !value;
    }

    private _roomID: number = 1;
    public get roomID(): number {
        return this._roomID;
    }
    public set roomID(value: number) {
        this._roomID = value;
        this.invokeGetJackpot();
        this.betButtons.forEach((btn, i) => {
            btn.node.getChildByName("checkmark").active = (i === value - 1);
        })
    }

    private _isSpinning: boolean = false;
    public get isSpinning(): boolean {
        return this._isSpinning;
    }
    public set isSpinning(value: boolean) {
        this._isSpinning = value;
        this.updateButtonUI();
    }
    private _isTurbo: boolean = false;
    public get isTurbo(): boolean {
        return this._isTurbo;
    }
    public set isTurbo(value: boolean) {
        this._isTurbo = value;
    }

    private latestSpinResponse: any = null;

    onLoad() {
        super.onLoad();
        this.itemHeight = this.symbolTemplate.height;
        this.tableInit();

        this.registedListeners();
        this.registedHub();
    }

    start() {
        this.updateButtonUI();
        this.resetAllEffect();
        this.roomID = 1;
        this.betType = 1;
        this.isAuto = false;
    }

    private registedListeners() {
        this.betButtons[0].node.on('click', () => {
            this.roomID = 1;
        });
        this.betButtons[1].node.on('click', () => {
            this.roomID = 2;
        });
        this.betButtons[2].node.on('click', () => {
            this.roomID = 3;
        });

        this.btnTurbo.node.on('toggle', () => {
            this.isTurbo = this.btnTurbo.isChecked;
        })

        this.currencyButton.node.on('toggle', () => {
            //* 1-Gold, 2-Xu
            this.betType = this.currencyButton.isChecked ? 1 : 2;
        })

        let dotChild = this.dots.children;
        let lineChild = this.lines.children;
        for (let i = 0; i < dotChild.length; i++) {
            dotChild[i].on(cc.Node.EventType.MOUSE_ENTER, () => {
                lineChild[i].active = true;
            })
            dotChild[i].on(cc.Node.EventType.MOUSE_LEAVE, () => {
                lineChild[i].active = false;
            })
        }
    }


    private registedHub() {
        MiniGameSignalRClient.getInstance().receive('jackpotGod', (data) => {
            Tween.numberTo(this.lblJackpotValue, data.JackpotFund, 0.3);
        })

        MiniGameSignalRClient.getInstance().receive('resultISpin', (data) => {
            cc.log("resultISpin", data);
            this.onSpinResult(data);
        })
    }

    private activePopupSelectLine() {
        if (this.popupSelectLine) {
            this.popupSelectLine.show();
        } else {
            this.popupSelectLine = cc.instantiate(this.popupSelectLinePrefab).getComponent(PopupSelectLine);
            this.popupSelectLine.node.parent = this.popupContainer;
            this.popupSelectLine.show();
            this.popupSelectLine.setOnSelectedChanged(selected => {
                this.currentSelectedLine = selected;
                this.lblTotalLine.string = selected.length.toString();
            })
            this.popupSelectLine.activeAllLines();
        }
    }

    private activePopupHonor() {
        if (this.popupHonor) {
            this.popupHonor.show();
        } else {
            this.popupHonor = cc.instantiate(this.popupHonorPrefab).getComponent(PopupHonors);
            this.popupHonor.node.parent = this.popupContainer;
            this.popupHonor.show();
        }
    }

    private activePopupGuide() {
        if (this.popupGuide) {
            this.popupGuide.show();
        } else {
            this.popupGuide = cc.instantiate(this.popupGuidePrefab).getComponent(PopupGuide);
            this.popupGuide.node.parent = this.popupContainer;
            this.popupGuide.show();
        }
    }

    private activePopupHistory() {
        if (this.popupHistory) {
            this.popupHistory.show();
        } else {
            this.popupHistory = cc.instantiate(this.popupHistoryPrefab).getComponent(PopupHistory);
            this.popupHistory.node.parent = this.popupContainer;
            this.popupHistory.show();
        }
    }

    show() {
        if (this.node.active && !this.isAuto) {
            super.reOrder();
            return;
        }
        // this.isAuto = false;
        // this.roomID = 1;
        // this.betType = 1;
        this.stopAllLines();
        super.show();
        // reset position
        this.node.setPosition(cc.v3(0, 0, 0));
    }

    dismiss() {
        if (this.isAuto) {
            this.node.setPosition(cc.v3(-10000, 0, 0));
        }
        else {
            super.dismiss();
            this.invokeHideSlot();
        }

        for (let child of this.popupContainer.children) {
            child.active = false;
        }
        App.instance.hideGameMini("PhucSinh");
    }

    private onClickAutoSpin() {
        this.isAuto = !this.isAuto;
        if (!this.isAuto) return;
        this.onClickSpin();
    }

    private onClickSpin() {
        if (this.isSpinning) return;
        this.isSpinning = true;
        this.stopAllLines();
        MiniGameSignalRClient.getInstance().send('ISpin',
            [Configs.Login.CurrencyID, this.betType, this.roomID, this.currentSelectedLine.join(",")],
            (data) => {
                if (data < 0) {
                    App.instance.alertDialog.showMsg(App.instance.getTextLang('me' + data));
                    this.isSpinning = false;
                    if (this.isAuto) {
                        this.isAuto = false;
                    }
                }
            }
        )
    }

    private invokeGetJackpot() {
        MiniGameSignalRClient.getInstance().send('GetGodJackpot',
            [Configs.Login.CurrencyID, this.betType, this.roomID],
            (data) => {
            }
        )
    }

    private invokeHideSlot() {
        MiniGameSignalRClient.getInstance().send('HideGodSlot', [],
            (data) => {
                cc.log("HideGodSlot", data);
            }
        )
    }

    private tableInit() {
        for (let i = 0; i < this.reels.childrenCount; i++) {
            let column = this.reels.children[i];
            for (let j = 0; j < 50; j++) {
                let row = cc.instantiate(this.symbolTemplate);
                row.parent = column;
                row.getComponentInChildren(cc.Sprite).spriteFrame = this.symbol[Utils.randomRangeInt(0, 6)];
            }
        }
        this.symbolTemplate.removeFromParent();
        this.symbolTemplate = null;
    }

    private onSpinResult(res: any) {
        // if (res.ResponseStatus < 0) return;
        this.latestSpinResponse = res;
        this.lblSession.string = '#' + res.SpinID;
        this.updateBalance();
        BroadcastReceiver.send(BroadcastReceiver.USER_UPDATE_COIN);

        let matrix = res.SlotData.split(",");
        // let timeScale = this.isTurbo ? 0.8 : 1.4;

        // for (let i = 0; i < this.reels.childrenCount; i++) {
        //     let column = this.reels.children[i];

        //     let step1Pos = this.itemHeight * 0.3;
        //     let step2Pos = -this.itemHeight * column.childrenCount + this.itemHeight * 3 - this.itemHeight * 0.2;
        //     let step3Pos = -this.itemHeight * column.childrenCount + this.itemHeight * 3;

        //     column.runAction(cc.sequence(
        //         cc.delayTime(0.2 * i * timeScale),
        //         cc.moveTo(0.2 * timeScale, cc.v2(column.getPosition().x, step1Pos)).easing(cc.easeQuadraticActionOut()),
        //         cc.moveTo((3 + 0.3 * i) * timeScale, cc.v2(column.getPosition().x, step2Pos)),
        //         cc.moveTo(0.2 * timeScale, cc.v2(column.getPosition().x, step3Pos)).easing(cc.easeQuadraticActionIn()),
        //         cc.callFunc(() => {
        //             column.setPosition(cc.v2(column.getPosition().x, 0));
        //             if (i === this.reels.childrenCount - 1) {
        //                 this.receiveRewards();
        //             }
        //         })
        //     ));

        //     column.runAction(cc.sequence(
        //         cc.delayTime((0.8 + 0.2 * i) * timeScale),
        //         cc.callFunc(() => {
        //             let children = column.children;
        //             if (children.length >= 3) {
        //                 let id1 = parseInt(matrix[i]) - 1;
        //                 let id2 = parseInt(matrix[3 + i]) - 1;
        //                 let id3 = parseInt(matrix[6 + i]) - 1;

        //                 children[2].getComponentInChildren(cc.Sprite).spriteFrame = this.symbol[id1];
        //                 children[1].getComponentInChildren(cc.Sprite).spriteFrame = this.symbol[id2];
        //                 children[0].getComponentInChildren(cc.Sprite).spriteFrame = this.symbol[id3];

        //                 let itemBlur1 = children[children.length - 1].getComponentInChildren(cc.Sprite);
        //                 let itemBlur2 = children[children.length - 2].getComponentInChildren(cc.Sprite);
        //                 let itemBlur3 = children[children.length - 3].getComponentInChildren(cc.Sprite);

        //                 itemBlur1.spriteFrame = this.symbol[id1];
        //                 itemBlur2.spriteFrame = this.symbol[id2];
        //                 itemBlur3.spriteFrame = this.symbol[id3];
        //             }
        //         })
        //     ));
        // }
    }

    private async receiveRewards() {
        const hasReward = this.latestSpinResponse.TotalPrizeValue > 0;

        // Nếu có dòng thắng, xử lý tách dòng
        let rewardLines: cc.Node[] = [];
        if (hasReward) {
            rewardLines = (this.latestSpinResponse.PrizeData as string)
                .split(";")
                .map(segment => parseInt(segment.split(",")[0]))
                .map(num => this.lines.children[num - 1]);

            // Bước 1: Hiện tất cả các dòng thắng ngay lập tức
            rewardLines.forEach(node => node.active = true);
        }

        await this.showWin();

        this.isSpinning = false;
        this.resetAllEffect();
        this.lines.children.forEach(node => node.active = false);
        if (this.isAuto) {
            if (hasReward) {
                cc.systemEvent.emit('ShowMoneyAuto', { money: this.latestSpinResponse.TotalPrizeValue, game: "phucsinh" });
            }
            this.onClickSpin();
        } else {
            // Bước 3: Nếu có dòng thắng, chạy hiệu ứng từng dòng
            if (hasReward) {
                rewardLines.forEach((node, i) => {
                    cc.tween(node)
                        .delay(i)
                        .call(() => node.active = true)
                        .delay(0.3)
                        .call(() => node.active = false)
                        .start();
                });
            }
        }
    }

    private resetAllEffect() {
        this.effectJackpot.active = false;
        this.effectNormalWin.active = false;
        this.effectBigWin.active = false;
        this.effectX2Win.active = false;

        this.effectJackpot.stopAllActions();
        this.effectNormalWin.stopAllActions();
        this.effectBigWin.stopAllActions();
        this.effectX2Win.stopAllActions();
    }


    private stopAllLines() {
        this.unscheduleAllCallbacks();
        this.lines.children.forEach(line => {
            line.opacity = 255;
            line.active = false;
            line.cleanup()
        });
    }

    /*
    * If currency is 1: update GoldCoin
    * If currency is 2: update Coin
    */
    private updateBalance() {
        if (this.betType === 1) {
            Configs.Login.GoldCoin = this.latestSpinResponse.Balance;
        } else {
            Configs.Login.Coin = this.latestSpinResponse.Balance;
        }
    }

    private async showWin() {
        const payLinePrize = this.latestSpinResponse.TotalPrizeValue;
        const betValue = this.getBetValue();

        this.lblWinCash.string = '0';

        this.resetAllEffect();
        // this.effectNormalWin.active = payLinePrize > 0;

        if (this.latestSpinResponse.IsJackpot > 0) {
            this.effectJackpot.active = true;
            let jpAnim = this.effectJackpot.getComponentInChildren(cc.Animation).play('jackpot');
            return new Promise<void>(resolve => {
                this.effectJackpot.runAction(cc.sequence(
                    cc.spawn(cc.fadeIn(0.3), cc.scaleTo(0.3, 1).easing(cc.easeBackOut())),
                    cc.delayTime(4),
                    cc.fadeOut(0.3),
                    cc.callFunc(() => {
                        this.isAuto = false;
                        jpAnim.stop();
                        resolve();
                    })
                ));
            });
        }
        else if (payLinePrize >= 70 * betValue) {
            this.effectBigWin.active = true;
            let bigwinAnim = this.effectBigWin.getComponentInChildren(cc.Animation).play('bigwin');
            let lblWin = this.effectBigWin.getComponentInChildren(cc.Label);
            lblWin.string = '';
            Tween.numberTo(lblWin, payLinePrize, 3);
            return new Promise<void>(resolve => {
                this.effectBigWin.runAction(cc.sequence(
                    cc.spawn(cc.fadeIn(0.3), cc.scaleTo(0.3, 1).easing(cc.easeBackOut())),
                    cc.delayTime(5),
                    cc.fadeOut(0.3),
                    cc.callFunc(() => {
                        bigwinAnim.stop();
                        resolve();
                    })
                ));
            });
        }
        else if (false && this.latestSpinResponse.IsX2) {
            this.effectX2Win.active = true;
            // Tween.numberTo(this.lblWinCash, payLinePrize, 0.5);
            return new Promise<void>(resolve => {
                this.effectX2Win.runAction(cc.sequence(
                    cc.spawn(cc.fadeIn(0.3), cc.scaleTo(0.3, 1).easing(cc.easeBackOut())),
                    cc.delayTime(5),
                    cc.fadeOut(0.3),
                    cc.callFunc(() => {
                        // this.effectNormalWin.active = false;
                        resolve();
                    })
                ));
            });
        }
        else {
            this.effectNormalWin.active = true;
            return new Promise<void>(resolve => {
                Tween.numberTo(this.lblWinCash, payLinePrize, 0.5);
                setTimeout(() => {
                    this.effectNormalWin.active = false;
                    resolve();
                }, 1500);
            });
        }
    }

    getBetValue() {
        if (this.betType === 1) {
            if (this.roomID === 1) return 100;
            if (this.roomID === 2) return 1000;
            if (this.roomID === 3) return 10000;
        }
        else if (this.betType === 2) {
            if (this.roomID === 1) return 1000;
            if (this.roomID === 2) return 10000;
            if (this.roomID === 3) return 100000;
        }
        return 0;
    }

    private updateButtonUI() {
        this.setButtonInteractable(this.betButtons[0], !this.isSpinning && !this.isAuto);
        this.setButtonInteractable(this.betButtons[1], !this.isSpinning && !this.isAuto);
        this.setButtonInteractable(this.betButtons[2], !this.isSpinning && !this.isAuto);
        this.setButtonInteractable(this.btnSelectLine, !this.isSpinning && !this.isAuto);
        this.setButtonInteractable(this.btnTurbo, !this.isSpinning && !this.isAuto);
        this.setButtonInteractable(this.btnSpin, !this.isSpinning && !this.isAuto);
        this.setButtonInteractable(this.currencyButton, !this.isSpinning && !this.isAuto);
        if (!this.isAuto) {
            this.setButtonInteractable(this.btnAutoSpin, !this.isSpinning);
        }
    }

    private setBetValue(btn: cc.Button, betValue: number) {
        btn.node.getChildByName("Background").getComponentInChildren(cc.Label).string = Utils.formatMoney(betValue, true)
        btn.node.getChildByName("checkmark").getComponentInChildren(cc.Label).string = Utils.formatMoney(betValue, true);
    }

    private setButtonInteractable(button: cc.Button, active: boolean) {
        button.interactable = active;
        // button.node.opacity = active ? 255 : 200;
    }

    onDestroy() {
        MiniGameSignalRClient.getInstance().dontReceive();
    }
}
