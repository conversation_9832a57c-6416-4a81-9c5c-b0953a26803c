import Dialog from "../../Lobby/LobbyScript/Script/common/Dialog";
import Http from "../../Lobby/MoveScript/Http";
import Configs from "../../Lobby/MoveScript/Configs";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import Pagination from "../../Lobby/LobbyScript/Pagination";

const {ccclass, property} = cc._decorator;

@ccclass
export class PopupHonors extends Dialog {
    @property(cc.ToggleContainer)
    private toggleTitle: cc.ToggleContainer = null;

    @property(cc.Node)
    private tipzoNode: cc.Node = null;

    @property(cc.Node)
    private tipzoContainer: cc.Node = null;

    @property(cc.Node)
    private tipzoRowTemplate: cc.Node = null;

    @property(cc.Node)
    private xuNode: cc.Node = null;

    @property(cc.Node)
    private xuContainer: cc.Node = null;

    @property(cc.Node)
    private xuRowTemplate: cc.Node = null;

    @property(cc.SpriteFrame)
    sfRow1: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    sfRow2: cc.SpriteFrame = null;

    @property(Pagination)
    private tipzoPagination: Pagination = null;

    @property(Pagination)
    private xuPagination: Pagination = null;

    private _dataTipzoLoaded : any;
    private _dataXuLoaded : any;

    protected onLoad() {
        for (let i = 0; i < this.toggleTitle.node.children.length; i++) {
            this.toggleTitle.node.children[i].on("toggle", () => {
                this.onClickTab(i);
            })
        }

        this.tipzoPagination.initListener((page: number) => {
            this.showTipzo(page);
        });

        this.xuPagination.initListener((page: number) => {
            this.showXu(page);
        });
    }

    public show() {
        super.show();
        this.fetchTipzoHistory();
        this.fetchXuHistory();
    }

    public dismiss() {
        super.dismiss();
    }

    private onClickTab(i: number) {
        if (i === 0) {
            this.tipzoNode.active = true;
            this.xuNode.active = false;
        } else {
            this.xuNode.active = true;
            this.tipzoNode.active = false;
        }
    }

    private fetchTipzoHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopWinnerPhucSinh"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 1,
            "Page": 1,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataTipzoLoaded = json["d"];
                this.showTipzo(1);
            }
        });
    }

    private fetchXuHistory() {
        Http.get(Configs.App.DOMAIN_CONFIG["GetTopWinnerPhucSinh"], {
            "currencyID": Configs.Login.CurrencyID,
            "betType": 2,
            "Page": 1,
            "topCount": 100,
        }, (status, json) => {
            if (json["c"] >= 0) {
                this._dataXuLoaded = json["d"];
                this.showXu(1);
            }
        });
    }


    private showTipzo(page: number) {
        this.tipzoContainer.removeAllChildren();
        this.tipzoPagination.updatePagination(Math.ceil(this._dataTipzoLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataTipzoLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = cc.instantiate(this.tipzoRowTemplate);
            itemRow.active = true;
            this.tipzoContainer.addChild(itemRow);
            let dataParsed = this.dataParser(data);
            itemRow.children[0].getComponent(cc.Label).string = dataParsed.dateTime;
            itemRow.children[1].getComponent(cc.RichText).string = dataParsed.nickName;
            let betValue = 0;
            switch (parseInt(dataParsed.roomID)) {
                case 1:
                    betValue = 100;
                    break;
                case 2:
                    betValue = 1000;
                    break;
                case 3:
                    betValue = 10000;
                    break;
            }
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumber(betValue);
            itemRow.children[3].getComponent(cc.Label).string = dataParsed.win;
            itemRow.children[4].getComponent(cc.Label).string = dataParsed.winType;

            itemRow.getComponent(cc.Sprite).spriteFrame = ithRow % 2 === 0 ? this.sfRow1 : this.sfRow2;
            ithRow++;
        }
    }

    private showXu(page: number) {
        this.xuContainer.removeAllChildren();
        this.xuPagination.updatePagination(Math.ceil(this._dataXuLoaded.length / 10), page);

        let ithRow = 0;
        let loaded = this._dataXuLoaded.slice((page - 1) * 10, page * 10);
        for (let data of loaded) {
            let itemRow = cc.instantiate(this.xuRowTemplate);
            itemRow.active = true;
            this.xuContainer.addChild(itemRow);
            let dataParsed = this.dataParser(data);
            itemRow.children[0].getComponent(cc.Label).string = dataParsed.dateTime;
            itemRow.children[1].getComponent(cc.RichText).string = dataParsed.nickName;
            let betValue = 0;
            switch (parseInt(dataParsed.roomID)) {
                case 1:
                    betValue = 1000;
                    break;
                case 2:
                    betValue = 10000;
                    break;
                case 3:
                    betValue = 100000;
                    break;
            }
            itemRow.children[2].getComponent(cc.Label).string = Utils.formatNumber(betValue);
            itemRow.children[3].getComponent(cc.Label).string = dataParsed.win;
            itemRow.children[4].getComponent(cc.Label).string = dataParsed.winType;

            itemRow.getComponent(cc.Sprite).spriteFrame = ithRow % 2 === 0 ? this.sfRow1 : this.sfRow2;
            ithRow++;
        }
    }

    private dataParser(data: any): RowData{
        return {
            dateTime: Utils.formatDatetime(data.CreatedTime,"dd/MM/yyyy HH:mm:ss"),
            nickName: data.Nickname.replace(/\[(.*?)\]/, '<color=#41d802>[$1]</color>'),
            roomID: data.RoomID,
            win: Utils.formatNumber(data.PrizeValue),
            winType: App.instance.getTextLang("sl33") + (data.IsX2 ? " X2" : ""),
        }
    }

}

interface RowData {
    dateTime: string;
    nickName: string;
    roomID: string;
    win: string;
    winType: string;
}
