[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "PopupGuide", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}], "_active": true, "_components": [{"__id__": 149}, {"__id__": 150}, {"__id__": 151}], "_prefab": {"__id__": 152}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "4d1vrTqChLqYfpruHq2Z0W", "sync": false}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 7}, {"__id__": 12}, {"__id__": 18}, {"__id__": 57}], "_active": true, "_components": [{"__id__": 147}], "_prefab": {"__id__": 148}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1361, "height": 829}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "TITLE", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 228.57, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 344.3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "HƯỚNG DẪN", "_N$string": "HƯỚNG DẪN", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 1, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "id": "txt_guide", "isUpperCase": true, "useCustomFont": false, "maxWidth": 0, "useCustomWrapText": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 45, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "d2jKtQ9NhOoaYITw3AZ7HT", "sync": false}, {"__type__": "cc.Node", "_name": "BtnClose", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [670.5, 394.5, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c7d1088c-392b-4ff5-afd5-ad8b1f9fb790"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.95, "clickEvents": [{"__id__": 15}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 12}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "ce8e524HiJKqrOSEb3jkefd", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 10, "_top": 20, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "28QdZDxAhEr7MwU/NTbSAi", "sync": false}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 19}, {"__id__": 26}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 666.5, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-333.25, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_active": true, "_components": [{"__id__": 23}, {"__id__": 53}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [333.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "a3fC/PLyZHpoBPdYcOrWGd", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_scrollView": {"__id__": 24}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 21}, "_N$direction": 1, "_id": ""}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": false, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 25}, "content": {"__id__": 25}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 23}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 29}, {"__id__": 33}, {"__id__": 49}], "_active": true, "_components": [], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 666.5, "height": 1000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 332, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 25}], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 666.5, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "2dRXZgRupEm5dIdeYDXzpW", "sync": false}, {"__type__": "cc.Node", "_name": "guide1", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [], "_active": false, "_components": [{"__id__": 30}, {"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 602, "height": 806}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -423, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "235a6d5e-a65a-482a-af70-53682e003338"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 20, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "52tsTdpglJapL2QcjY7ucH", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON> thieu", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 34}, {"__id__": 37}, {"__id__": 40}, {"__id__": 43}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 47}], "_prefab": {"__id__": 48}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 602, "height": 157.79999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-301.27, -8.025, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 35}], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100.27, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON><PERSON><PERSON> thiệu:", "_N$string": "<PERSON><PERSON><PERSON><PERSON> thiệu:", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "86gegq2KJMF5ueGDfD2uz9", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 38}], "_prefab": {"__id__": 39}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 576.8, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON><PERSON>à game thu<PERSON><PERSON> thể loại slot đơn gi<PERSON>n d<PERSON> ch<PERSON>, dễ ", "_N$string": "<PERSON><PERSON><PERSON>à game thu<PERSON><PERSON> thể loại slot đơn gi<PERSON>n d<PERSON> ch<PERSON>, dễ ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "78E+XvMcpJIa+VipQ1SMKC", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 41}], "_prefab": {"__id__": 42}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 601.73, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 40}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "trúng<PERSON> <PERSON><PERSON><PERSON> 6 item đ<PERSON><PERSON><PERSON> sắp xếp thành 3 hàng, 3 cột tạo thành ", "_N$string": "trúng<PERSON> <PERSON><PERSON><PERSON> 6 item đ<PERSON><PERSON><PERSON> sắp xếp thành 3 hàng, 3 cột tạo thành ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "c5vthNOdpIOappybdow7fo", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 33}, "_children": [], "_active": true, "_components": [{"__id__": 44}], "_prefab": {"__id__": 45}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 506.16, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 43}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "r<PERSON><PERSON> <PERSON><PERSON><PERSON> tổ hợp trả thưởng theo 20 dòng cho trước.", "_N$string": "r<PERSON><PERSON> <PERSON><PERSON><PERSON> tổ hợp trả thưởng theo 20 dòng cho trước.", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "0ayckNBBdCR5kcJC9SLpSM", "sync": false}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "<color=yellow><PERSON><PERSON><PERSON><PERSON> thiệu:</color>\n<PERSON><PERSON>c <PERSON> là game thu<PERSON><PERSON> thể loại slot đơn gi<PERSON><PERSON> d<PERSON>, d<PERSON> trúng. Với 6 item đư<PERSON><PERSON> sắp xếp thành 3 hàng, 3 cột tạo thành rất nhiều tổ hợp trả thưởng theo 20 dòng cho trước.\n", "_N$horizontalAlign": 0, "_N$fontSize": 22, "_N$font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_N$cacheMode": 0, "_N$maxWidth": 602, "_N$lineHeight": 30, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": ""}, {"__type__": "3443eZBO+1FYbKZZLvKeyRF", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "id": "ps14", "isUpperCase": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "d6gj/Z4tBM+p+ShGiU693+", "sync": false}, {"__type__": "cc.Node", "_name": "guide1_p", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 599, "height": 681}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -523.903, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 49}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5287b994-3d5c-4f86-b996-086334d6330f"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "a6Ce84uahDHqcfgQ2DrZzk", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "2eE5XRr3ZO5orAeCM26ECB", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "763heKevNGPaqCuLLupIrl", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 14, "_right": 0.5, "_top": 140, "_bottom": 25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": false, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "72f6mAoW1IlLTU4+E4rHDR", "sync": false}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 58}, {"__id__": 65}], "_active": true, "_components": [{"__id__": 63}, {"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 666.5, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [333.25, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 59}], "_active": true, "_components": [{"__id__": 62}, {"__id__": 143}], "_prefab": {"__id__": 144}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [333.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "061NUBBD5K6Ln42WjYmeE+", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "_scrollView": {"__id__": 63}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 60}, "_N$direction": 1, "_id": ""}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": false, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 64}, "content": {"__id__": 64}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 62}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 65}, "_children": [{"__id__": 68}, {"__id__": 75}, {"__id__": 78}, {"__id__": 112}, {"__id__": 116}, {"__id__": 119}, {"__id__": 138}], "_active": true, "_components": [], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 666.5, "height": 1200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 332, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 64}], "_active": true, "_components": [{"__id__": 66}], "_prefab": {"__id__": 67}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 666.5, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 65}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "a4LsTKTm9DhJleykBm1Mkw", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON> thieu", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [{"__id__": 69}], "_active": true, "_components": [{"__id__": 72}, {"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 602, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-279.565, -11.855, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 68}, "_children": [], "_active": true, "_components": [{"__id__": 70}], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 460.37, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.8, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Wild có thể thay thế cho các biểu tượng còn lại", "_N$string": "Wild có thể thay thế cho các biểu tượng còn lại", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "3a4q1cpnhOfYZfzOG14S8a", "sync": false}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "Wild có thể thay thế cho các biểu tượng còn lại", "_N$horizontalAlign": 0, "_N$fontSize": 22, "_N$font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_N$cacheMode": 0, "_N$maxWidth": 602, "_N$lineHeight": 30, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": ""}, {"__type__": "3443eZBO+1FYbKZZLvKeyRF", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "id": "ps15", "isUpperCase": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "290yX1DyhO+peC6MHF9nF5", "sync": false}, {"__type__": "cc.Node", "_name": "guide2_1", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 628, "height": 178}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -143.389, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cc05cfd3-c19f-4b23-b61b-f92612d6c1fc"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "49ICPn9vdMIKOhGv0HQjuG", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON> thieu copy", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [{"__id__": 79}, {"__id__": 82}, {"__id__": 85}, {"__id__": 88}, {"__id__": 91}, {"__id__": 94}, {"__id__": 97}, {"__id__": 100}, {"__id__": 103}, {"__id__": 106}], "_active": true, "_components": [{"__id__": 109}, {"__id__": 110}], "_prefab": {"__id__": 111}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 602, "height": 217.79999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-284.231, -229.272, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 80}], "_prefab": {"__id__": 81}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 337.51, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> chơi và thể lệ trả thưởng", "_N$string": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> chơi và thể lệ trả thưởng", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "e7b/PLbqxC0LnngMzht1Oj", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 83}], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 197.76, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "B1: <PERSON><PERSON><PERSON> ", "_N$string": "B1: <PERSON><PERSON><PERSON> ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "362xZFs5BOOb4oFWE5L94N", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 86}], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 183.22, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [197.76, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "100, 1.000, 10.000", "_N$string": "100, 1.000, 10.000", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "c5SWFOpKxMILoJGP+hLLg9", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 89}], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 592.13, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "B2: <PERSON><PERSON><PERSON> số dòng cược. <PERSON>ò<PERSON> cược là các tổ hợp các biểu tư", "_N$string": "B2: <PERSON><PERSON><PERSON> số dòng cược. <PERSON>ò<PERSON> cược là các tổ hợp các biểu tư", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "be1f8o1dJI8IhVYTjLgHwf", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 455, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON>ng nằm theo vị trí đã đư<PERSON>c thiết kế. <PERSON><PERSON> tất cả ", "_N$string": "<PERSON>ng nằm theo vị trí đã đư<PERSON>c thiết kế. <PERSON><PERSON> tất cả ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "07igEF8iVHEJAdHKV2cgCv", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 135.28, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [455, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 94}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "20 dòng dư<PERSON>c", "_N$string": "20 dòng dư<PERSON>c", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "4eBhrL/UJFk4O6nZgjz+t+", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 98}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 11.62, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [590.28, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 97}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": ". ", "_N$string": ". ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "6c9WoKFJJLYJ/pGV+4kdId", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 101}], "_prefab": {"__id__": 102}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 305.52, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -157.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON> thể chọn 1 hoặc nhiều dòng", "_N$string": "<PERSON><PERSON> thể chọn 1 hoặc nhiều dòng", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "b4x0u8PZFJ0LDjuT0dTp4p", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 104}], "_prefab": {"__id__": 105}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 368.59, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -187.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 103}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Tổ<PERSON> tiền cượ<PERSON> = <PERSON><PERSON><PERSON> cược x số dòng", "_N$string": "Tổ<PERSON> tiền cượ<PERSON> = <PERSON><PERSON><PERSON> cược x số dòng", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "82tpel25NAu7VXR0wQAWSj", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 107}], "_prefab": {"__id__": 108}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 566.96, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -217.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "B3: <PERSON><PERSON><PERSON><PERSON> Quay để chơi. <PERSON><PERSON> thể chọn chế độ quay tự động.", "_N$string": "B3: <PERSON><PERSON><PERSON><PERSON> Quay để chơi. <PERSON><PERSON> thể chọn chế độ quay tự động.", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "fdkq+tMARINYQAteHQiOjB", "sync": false}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "<PERSON><PERSON><PERSON> bước chơi và thể lệ trả thưởng\nB1: <PERSON><PERSON><PERSON> mứ<PERSON> cượ<PERSON> <color=yellow>100, 1.000, 10.000</color>\nB2: <PERSON><PERSON><PERSON> số dòng cược. Dòng cược là các tổ hợp các biểu tượng nằm theo vị trí đã được thiết kế. <PERSON><PERSON> tất cả <color=yellow>20 dòng dược</color>. <PERSON><PERSON> thể chọn 1 hoặc nhiều dòng\n<color=yellow>Tổng tiền cược = Mức cược x số dòng</color>\nB3: Nhấn Quay để chơi. <PERSON><PERSON> thể chọn chế độ quay tự động.", "_N$horizontalAlign": 0, "_N$fontSize": 22, "_N$font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_N$cacheMode": 0, "_N$maxWidth": 602, "_N$lineHeight": 30, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": ""}, {"__type__": "3443eZBO+1FYbKZZLvKeyRF", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "id": "ps16", "isUpperCase": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "98ULi5XUNB7Z8LirfW+k7R", "sync": false}, {"__type__": "cc.Node", "_name": "New Label", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 113}, {"__id__": 114}], "_prefab": {"__id__": 115}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 125.74, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [13.674, -513.921, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "CHỌN DÒNG", "_N$string": "CHỌN DÒNG", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "id": "ps17", "isUpperCase": true, "useCustomFont": true, "maxWidth": 0, "useCustomWrapText": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "48AHkYJJFG+KNJGR12t7mO", "sync": false}, {"__type__": "cc.Node", "_name": "guide2_2", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": true, "_components": [{"__id__": 117}], "_prefab": {"__id__": 118}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 617, "height": 322}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.481, -684.269, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 116}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5a76d016-cf8e-4a52-8d80-fe90dd425a9c"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "f1TO23H8pGvI2fCWSjvEgx", "sync": false}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON><PERSON> thieu copy", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [{"__id__": 120}, {"__id__": 123}, {"__id__": 126}, {"__id__": 129}, {"__id__": 132}], "_active": true, "_components": [{"__id__": 135}, {"__id__": 136}], "_prefab": {"__id__": 137}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 602, "height": 157.79999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-284.231, -857.661, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 475.18, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "* <PERSON><PERSON> thống trả thưởng lên đến 98% số M-Win đặt", "_N$string": "* <PERSON><PERSON> thống trả thưởng lên đến 98% số M-Win đặt", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "9exr2kYCNHGLzb25eCpbp+", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568.48, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "* <PERSON><PERSON><PERSON> chơi <PERSON> độc lập hoàn toàn với kết quả chơi ", "_N$string": "* <PERSON><PERSON><PERSON> chơi <PERSON> độc lập hoàn toàn với kết quả chơi ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "bc/KpzMlpLhbZSx5YYOUZ9", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 127}], "_prefab": {"__id__": 128}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 133.75, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "game hiện tại", "_N$string": "game hiện tại", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "femJ2+DQ5AJr2VoP/zWfT6", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 130}], "_prefab": {"__id__": 131}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 571.82, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "* Tại mỗi thời điểm, mỗi mức cược sẽ chỉ có duy nhất một ", "_N$string": "* Tại mỗi thời điểm, mỗi mức cược sẽ chỉ có duy nhất một ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "d3l34ZFQBKtqHGFxwHoV/k", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 119}, "_children": [], "_active": true, "_components": [{"__id__": 133}], "_prefab": {"__id__": 134}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 248, "g": 248, "b": 248, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 453.99, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -157.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Jackpot và giá trị Jackpot sẽ liên tục tăng cao", "_N$string": "Jackpot và giá trị Jackpot sẽ liên tục tăng cao", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "a3dY7tOeZGhbjcUXTGY/HQ", "sync": false}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "* <PERSON><PERSON> thống trả thưởng lên đến 98% số M-Win đặt\n* <PERSON><PERSON><PERSON> chơi Phục Sinh độc lập hoàn toàn với kết quả chơi game hiện tại\n* Tại mỗi thời điểm, mỗi mức cược sẽ chỉ có duy nhất một Jackpot và giá trị Jackpot sẽ liên tục tăng cao", "_N$horizontalAlign": 0, "_N$fontSize": 22, "_N$font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_N$cacheMode": 0, "_N$maxWidth": 602, "_N$lineHeight": 30, "_N$imageAtlas": null, "_N$handleTouchEvent": true, "_id": ""}, {"__type__": "3443eZBO+1FYbKZZLvKeyRF", "_name": "", "_objFlags": 0, "node": {"__id__": 119}, "_enabled": true, "id": "ps20", "isUpperCase": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "f9c7AWIztJOpXgPvw+UxLs", "sync": false}, {"__type__": "cc.Node", "_name": "guide2", "_objFlags": 0, "_parent": {"__id__": 64}, "_children": [], "_active": false, "_components": [{"__id__": 139}, {"__id__": 140}], "_prefab": {"__id__": 141}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 629, "height": 942}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -491, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "99f39f8f-fd57-431d-9ee0-188d88ce54f7"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 138}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 20, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "90hAJetTJMsoj9duiQ5a2V", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "20CVXUQhpNwaRKCum0t5Nc", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "b10tj+XXJEk6s8kAZEmEFY", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0.5, "_right": 14, "_top": 140, "_bottom": 25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": false, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "481L4dondAKrnLrHigxuot", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a0254979-c999-441b-bd81-2f6092806003"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "96SghbGeJANLMn48LN1fIj", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_id": ""}, {"__type__": "ce8e524HiJKqrOSEb3jkefd", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": {"__id__": 2}, "container": {"__id__": 6}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "94330746-b4de-4e4d-b9cc-2892ac623d2f"}, "fileId": "", "sync": false}]