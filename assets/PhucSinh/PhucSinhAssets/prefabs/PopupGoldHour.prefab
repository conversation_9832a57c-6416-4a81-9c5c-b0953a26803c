[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "PopupGoldHour", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}], "_active": true, "_components": [{"__id__": 97}, {"__id__": 98}, {"__id__": 99}], "_prefab": {"__id__": 100}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "Bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "2cAauSkqNCeZJGOgNaWG0q", "sync": false}, {"__type__": "cc.Node", "_name": "Container", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 7}, {"__id__": 12}, {"__id__": 18}], "_active": true, "_components": [{"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1361, "height": 829}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "TITLE", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 211.22, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 344.3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Lung Linh X2", "_N$string": "Lung Linh X2", "_fontSize": 36, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "47fcfrB4HZCEqoTqVimESy9", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "id": "ev28", "isUpperCase": true, "useCustomFont": true, "maxWidth": 1000, "useCustomWrapText": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 1, "_left": 0, "_right": 0, "_top": 45, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "18zf+olbNDGYYaI7lcUBA6", "sync": false}, {"__type__": "cc.Node", "_name": "BtnClose", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 162, "height": 106}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [670.5, 394.5, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "c7d1088c-392b-4ff5-afd5-ad8b1f9fb790"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 0.95, "clickEvents": [{"__id__": 15}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 12}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "ce8e524HiJKqrOSEb3jkefd", "handler": "dismiss", "customEventData": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 33, "_left": 0, "_right": 10, "_top": 20, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "b5DYk8NYNC+roM3lYbgt1B", "sync": false}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [{"__id__": 19}, {"__id__": 26}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 93}], "_prefab": {"__id__": 94}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1333, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 20}], "_active": true, "_components": [{"__id__": 23}, {"__id__": 91}], "_prefab": {"__id__": 92}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [666.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "7cFypf6ZdDA4y+Hk/wPeV4", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_scrollView": {"__id__": 24}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 21}, "_N$direction": 1, "_id": ""}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": false, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 25}, "content": {"__id__": 25}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 23}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 29}], "_active": true, "_components": [], "_prefab": {"__id__": 90}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1333, "height": 1038}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 332, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [{"__id__": 25}], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1333, "height": 664}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "b2etH6N1tNiLHopBsS6djI", "sync": false}, {"__type__": "cc.Node", "_name": "guide", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [{"__id__": 30}, {"__id__": 33}, {"__id__": 36}, {"__id__": 39}, {"__id__": 42}, {"__id__": 45}, {"__id__": 48}, {"__id__": 51}, {"__id__": 54}, {"__id__": 57}, {"__id__": 60}, {"__id__": 63}, {"__id__": 66}, {"__id__": 69}, {"__id__": 72}, {"__id__": 75}, {"__id__": 78}, {"__id__": 81}, {"__id__": 84}], "_active": true, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": {"__id__": 89}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1177, "height": 247.79999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-606, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 84.77, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -37.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Sự kiện: ", "_N$string": "Sự kiện: ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "fbQjDOu71OYJNrTpG2dBdd", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 35}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 338.93, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [84.76999999999998, -37.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 33}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Chơi game <PERSON><PERSON><PERSON>, lung linh X2", "_N$string": "Chơi game <PERSON><PERSON><PERSON>, lung linh X2", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "c3epHneFFJQp3kGDNeK6dF", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 37}], "_prefab": {"__id__": 38}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 747.88, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [423.7, -37.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " của minigame Phục <PERSON> \\n\\nVào 12h30 thứ ba và thứ bảy hằng tuần, để tri", "_N$string": " của minigame Phục <PERSON> \\n\\nVào 12h30 thứ ba và thứ bảy hằng tuần, để tri", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "cbPewaLlJNk6LH2X67j6bE", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 917.77, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " ân ngư<PERSON><PERSON> chơ<PERSON>, minigame <PERSON><PERSON>c Sinh sẽ xuất hiện 22 hũ phòng 100 và 2 hũ phòng 1.000 được ", "_N$string": " ân ngư<PERSON><PERSON> chơ<PERSON>, minigame <PERSON><PERSON>c Sinh sẽ xuất hiện 22 hũ phòng 100 và 2 hũ phòng 1.000 được ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "0fjWUB/QVAtIxS2K4z2r+f", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230.65, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [917.77, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " X2(nhân đôi) giá trị hũ ", "_N$string": " X2(nhân đôi) giá trị hũ ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "96jPinuF5Ol7vQsnYBDzJF", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 46}], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 23.67, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1148.42, -67.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " ở ", "_N$string": " ở ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "f2fFiy8Y5PvLPpm5VEhW07", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 49}], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 489.31, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "thời điểm nổ (khô<PERSON> tính trả thưởng payline). \\n\\n", "_N$string": "thời điểm nổ (khô<PERSON> tính trả thưởng payline). \\n\\n", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "40gujogAtJ46/eUCEdi0xJ", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 52}], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 61.52, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [489.31, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " <PERSON><PERSON> dụ:", "_N$string": " <PERSON><PERSON> dụ:", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "8dzlkoCfpIBK3+V8oO0NWb", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 346.49, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [550.83, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON>i thời điểm ăn hũ X2, gi<PERSON> trị hũ là ", "_N$string": "<PERSON><PERSON>i thời điểm ăn hũ X2, gi<PERSON> trị hũ là ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "a7jy+MptFEeK+Ti2+XbjY/", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 58}], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 110.77, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [897.32, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " 6.000.000 ", "_N$string": " 6.000.000 ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "5d7N6ob6JFfLKqIrw4B2Jx", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 61}], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 119.12, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1008.09, -97.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": ", trả thưởng ", "_N$string": ", trả thưởng ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "7aDG/Q58ZOJI4Ai1CjgUjC", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 99.55, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "payline là ", "_N$string": "payline là ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "91zJZb5+dKFawBGKKfhGaz", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 92.13, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [99.55000000000001, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " 100.000 ", "_N$string": " 100.000 ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "df6UU4S41GUrQxZtAiOf4o", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 70}], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 397.26, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [191.68, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "thì giải thưởng người chơi nhận đư<PERSON>c là: ", "_N$string": "thì giải thưởng người chơi nhận đư<PERSON>c là: ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "402TWnZ2xP7aCNATrfLlt6", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 235, "b": 4, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 385.57, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [588.94, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " 6.000.000 x 2 + 100.000 = 12.100.000 ", "_N$string": " 6.000.000 x 2 + 100.000 = 12.100.000 ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "33c7NdH7NLjYE+qlDXapn9", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 148.37, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [974.51, -127.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": " \\n\\nChú ý: \\n- ", "_N$string": " \\n\\nChú ý: \\n- ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "34nTMsIctCOYIEwE+Jcr2Z", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1172.83, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -157.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Phòng 100: hũ được X2 tiếp theo sẽ cách 3 hũ bình thường đối với hũ X2 trước đó (vd: 1, 5, 9...).\\n- Phòng 1.000: hũ thứ", "_N$string": "Phòng 100: hũ được X2 tiếp theo sẽ cách 3 hũ bình thường đối với hũ X2 trước đó (vd: 1, 5, 9...).\\n- Phòng 1.000: hũ thứ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "29otuxpR9Ox5D2DnmvUf4K", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1167.79, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -187.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "ba và thứ bảy sẽ được X2 tính từ thời điểm khung giờ vàng.\\n- Giờ vàng X2 thứ ba và thứ bảy hằng tuần sẽ bắt đầu vào ", "_N$string": "ba và thứ bảy sẽ được X2 tính từ thời điểm khung giờ vàng.\\n- Giờ vàng X2 thứ ba và thứ bảy hằng tuần sẽ bắt đầu vào ", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "f811E0t2lJoK7eCegfemuR", "sync": false}, {"__type__": "cc.PrivateNode", "_name": "RICHTEXT_CHILD", "_objFlags": 1024, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840.55, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -217.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "showInEditor": false, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "lúc 12h30 và kết thúc khi trao thưởng đủ 22 hũ X2 phòng 100 và 2 X2 hũ phòng 1.000", "_N$string": "lúc 12h30 và kết thúc khi trao thưởng đủ 22 hũ X2 phòng 100 và 2 X2 hũ phòng 1.000", "_fontSize": 22, "_lineHeight": 30, "_enableWrapText": true, "_N$file": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 1, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "76X2ANsfJMTrRnIAYwkYxg", "sync": false}, {"__type__": "cc.RichText", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_fontFamily": "<PERSON><PERSON>", "_isSystemFontUsed": false, "_N$string": "Sự kiện: <color=yellow>Chơi game <PERSON><PERSON><PERSON>, lung linh X2</color> của minigame Phục Sinh \\n\\nVào 12h30 thứ ba và thứ bảy hằng tuần, để tri ân người chơi, minigame Phục Sinh sẽ xuất hiện 22 hũ phòng 100 và 2 hũ phòng 1.000 được <color=yellow> X2(nhân đôi) giá trị hũ </color> ở thời điểm nổ (không tính trả thưởng payline). \\n\\n<color=yellow> Ví dụ:</color>Tại thời điểm ăn hũ X2, giá trị hũ là <color=yellow> 6.000.000 </color>, trả thưởng payline là <color=yellow> 100.000 </color>thì giải thưởng người chơi nhận được là: <color=yellow> 6.000.000 x 2 + 100.000 = 12.100.000 </color> \\n\\nChú ý: \\n- Phòng 100: hũ đư<PERSON>c X2 tiếp theo sẽ cách 3 hũ bình thường đối với hũ X2 trước đó (vd: 1, 5, 9...).\\n- Phòng 1.000: hũ thứ ba và thứ bảy sẽ được X2 tính từ thời điểm khung giờ vàng.\\n- Giờ vàng X2 thứ ba và thứ bảy hằng tuần sẽ bắt đầu vào lúc 12h30 và kết thúc khi trao thưởng đủ 22 hũ X2 phòng 100 và 2 X2 hũ phòng 1.000\n", "_N$horizontalAlign": 0, "_N$fontSize": 22, "_N$font": {"__uuid__": "41468be0-6623-4ec0-882c-bf491c193690"}, "_N$cacheMode": 1, "_N$maxWidth": 1177, "_N$lineHeight": 30, "_N$imageAtlas": null, "_N$handleTouchEvent": false, "_id": ""}, {"__type__": "3443eZBO+1FYbKZZLvKeyRF", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "id": "ev27", "isUpperCase": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "a7b3cdJ19NBJJN3W9os0oP", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "16tVtIFxxIfK74Q3uc5jAC", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "bbkUbZQENA6a33jLFKr2AF", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 14, "_right": 14, "_top": 140, "_bottom": 25, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "f9SydQFRxFTbiPGGNWXs99", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a0254979-c999-441b-bd81-2f6092806003"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "a0CrTGwZRGgLFbrTzSrckH", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_id": ""}, {"__type__": "ce8e524HiJKqrOSEb3jkefd", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bg": {"__id__": 2}, "container": {"__id__": 6}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "dce40760-68f1-48c8-b317-5b76741acbcc"}, "fileId": "", "sync": false}]