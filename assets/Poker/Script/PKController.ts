// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import BundleControl from "../../Loading/src/BundleControl";
import ChatInGame from "../../Lobby/ChatInGame/ChatInGame";
import CardList from "../../Lobby/Common/CardList";
import CardOnTable from "../../Lobby/Common/CardOnTable";
import Card from "../../Lobby/Common/MCard";
import BuyChipPoker from "../../Lobby/LobbyScript/BuyChipPoker";
import App from "../../Lobby/LobbyScript/Script/common/App";
import LanguageMananger from "../../Lobby/LobbyScript/Script/common/Language.LanguageManager";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import CardGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/CardGameSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import PokerBetBar from "../PokerScript/PokerBetBar";
import PlayviewPK from "./PlayviewPK";

const {ccclass, property} = cc._decorator;

@ccclass
export default class PKController extends cc.Component {

    // @property(cc.SpriteFrame)
    // cardFrameSP: cc.SpriteFrame[] = [];
        @property(BuyChipPoker)
        buyChip:BuyChipPoker;

    @property(cc.Node)
    deskPoient: cc.Node = null;
    @property(cc.SpriteFrame)
    frameCardBack: cc.SpriteFrame = null;
    @property(cc.Node)
    cardFrames: cc.Node;
    @property(cc.Node)
    cardClone: cc.Node = null;

    @property(cc.Node)
    potClone: cc.Node = null;

    @property(cc.Node)
    potParent: cc.Node = null;

    @property(PokerBetBar)
    pokerBetBar: PokerBetBar = null;

    @property(cc.Node)
    cardCloneTable: cc.Node = null;

    public static instance:PKController = null ;

    @property(cc.Node)
    btnLatBai: cc.Node = null;
    @property(cc.Node)
    cardOnTableParent: cc.Node

    


    @property(PlayviewPK)
    listPlayerView: PlayviewPK[] = [];
    playerViewMe: PlayviewPK;
   

    listPlayer: any[] = [];
    maxPlayer:number = 6;
    playerMe: any;


    @property(cc.Node)
    public arrCardSprite: cc.Node = null;


    @property(cc.Label)
    lblThongBao: cc.Label = null;
    @property(cc.Label)
    public lblClone: cc.Label = null;

    @property(cc.Sprite)
    dealer: cc.Sprite;


    @property(cc.Label)
    lblMoneyCall: cc.Label = null;

    @property(cc.Node)
    myButtons: cc.Node;


    @property(cc.Node)
    myAutoButtons: cc.Node;


    @property(cc.AudioSource)
    sound: cc.AudioSource;


    @property(cc.Node)
    namePoker: cc.Node;

     // var bundle = cc.assetManager.getBundle("TienLen");
        // bundle.loadDir("res/win", cc.Texture2D, (err, textures) => {
        //     if (err) {
        //         console.error("Lỗi khi tải ảnh:", err);
        //         return;
        //     }
    
        //     // Chuyển texture thành SpriteFrame
        //     let spriteFrames: cc.SpriteFrame[] = textures.map((texture: cc.Texture2D) => {
        //         return new cc.SpriteFrame(texture);
        //     });
    
        //     // Gọi hàm phát animation
        //     this.playAnimation(spriteFrames);
        // });
    

    playSound(nameSound,isloop){
     
        var chidd = this.sound.node.getChildByName(nameSound);
        if(chidd){
            this.sound.clip =   chidd.getComponent(cc.AudioSource).clip;
            this.sound.loop = isloop;
            this.sound.play();
        }
 
    }
    INACTIVITY_TIMEOUT = 60;
    protected onLoad(): void {
        PKController.instance = this;
        this.schedule(this.checkInactivity, 1);
    }

    start () {
        var thiz = this;
        this.loadDir();
        this.pokerBetBar.node.active = false;
        this.pokerBetBar.callBackbetBar = (money,mydata)=>{
            thiz.playSound("button_Raise",false);

            CardGameSignalRClient.getInstance().send('Bet', [money,mydata.Action], (data) => {
                cc.log(data);
                     });
        }
        this.myButtons.children.forEach(element => {
            element.active = false;
        });

        cc.game.on(cc.game.EVENT_HIDE, this.onGamePause, this);

        cc.game.on(cc.game.EVENT_SHOW, this.onGameResume, this);
        
        
        this.lblThongBao.node.parent.active = false;

        this.listPlayerView.forEach(element => {
           // element.cardList.init(this.deskPoient,this.cardClone,this.frameCardBack,this.cardFrames);
        });


        this.playerViewMe = this.listPlayerView[0];

       
        CardGameSignalRClient.getInstance().receive('betOfAccountCF', (data: any) => {
      
     });
   
     setTimeout(() => {
        if(App.instance.DataPass.length>0)
            {
                this.onHandleJoin(App.instance.DataPass[0],App.instance.DataPass[1]);
            }    
     }, 100);
         
        // CardGameSignalRClient.getInstance().receiveArray('joinGame', (data1: any,data2: any) => {
        //      this.onHandleJoin(data1,data2);
        // });
        CardGameSignalRClient.getInstance().receive('playerJoin', (data: any) => {
            this.playerJoin(data);
        });
      CardGameSignalRClient.getInstance().receiveArray('playerLeave', (data: any,data2: any) => {
                 this.playerLeave(data,data2);
             });
        
        CardGameSignalRClient.getInstance().receiveArray('notifyStartActions', (data1: any,data2: any) => {
            this.notifyStartActions(data1,data2);
        });
        CardGameSignalRClient.getInstance().receiveArray('notifyChangePhrase',  (data1: any,data2: any)=> {
            this.notifyChangePhrase(data1,data2);
        });
        CardGameSignalRClient.getInstance().receive('playerCheckAuto', (data: any) => {
            this.playerCheckAuto(data);
        });
        CardGameSignalRClient.getInstance().receiveArray('playerFlipCards', (data1: any,data2: any) => {
            this.playerFlipCards(data1,data2);
        });

        

        //hieu ung tien bay len
        // end game
        //3 nguoi
        CardGameSignalRClient.getInstance().receiveArray('updateConnectionStatus',  (data1: any,data2: any)=> {
            this.updateConnectionStatus(data1,data2);
        });
        CardGameSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
                   if (accountId == `${Configs.Login.UserId}:${Configs.Login.PortalID}`) {
                      
                       thiz.playerViewMe.showChatMsg(content);
                   } else {
                       var playerView = thiz.getPlayviewWithID(accountId);
                       if (playerView == null) return;;
                       playerView.showChatMsg(content);
                   }
               });
        
        CardGameSignalRClient.getInstance().receiveArray('refund',  (data1: any,data2: any,data3:any,data4:any)=> {
            this.refun(data1,data2,data3,data4);
        });

        CardGameSignalRClient.getInstance().receiveArray('updateAccount',  (data1: any,data2: any,data3:any)=> {
            this.updateAccount(data1,data2,data3);
        });

        
        CardGameSignalRClient.getInstance().receiveArray('message',  (data1: any,data2: any)=> {
            if(data1.code){
                App.instance.showToast(App.instance.getTextLang("me"+data1.code));
            }
        });
        

        CardGameSignalRClient.getInstance().receiveArray('notifyFinishActions',  (data1: any,data2: any,data3:any,data4:any)=> {
            this.notifyFinishActions(data1,data2,data3,data4);
        });
      
        this.intervalId = setInterval(this.PingPong, 30000);

         var bundle = cc.assetManager.getBundle("Poker");
        bundle.loadDir("res/normal", cc.Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }
    
            // Chuyển texture thành SpriteFrame
            thiz.spriteFramesNo = textures.map((texture: cc.Texture2D) => {
                return new cc.SpriteFrame(texture);
            });
    
            // Gọi hàm phát animation
            this.playAnimationnormal();
        });

        bundle.loadDir("res/chiabai", cc.Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }
    
            // Chuyển texture thành SpriteFrame
            thiz.spriteFramesDe = textures.map((texture: cc.Texture2D) => {
                return new cc.SpriteFrame(texture);
            });
    
            // Gọi hàm phát animation
            // this.playAnimationDealer(spriteFrames);
        });
       

    }
    spriteFramesDe: cc.SpriteFrame[] = [];
    spriteFramesNo: cc.SpriteFrame[]= [];

    updateAccount( account,  chips,  buyin){
       
        var playerViewTmpe = this.getPlayviewWithID( account.AccountID);
        if (playerViewTmpe == null) return;
        playerViewTmpe.updateChipGold(chips);
    }

    refun(accountId,  refundValue,   chips,  pot){
        var playerViewTmpe = this.getPlayviewWithID(accountId);
        if (playerViewTmpe == null) return;
        playerViewTmpe.updateChipRefund(chips,refundValue);
        //update lại pot

        //move chip to bet to player

        if(this.potParent.childrenCount>0){
            this.potParent.children[this.potParent.childrenCount-1].children[0].getComponent(cc.Label).string = Utils.formatNumber(pot);
        }
    }

    ShowAutoC(AutoList){
        cc.log("AutoList" +JSON.stringify(AutoList));
        this.myButtons.active = false;
        this.myAutoButtons.active = true;
        AutoList.forEach(element => {
            
            this.myAutoButtons.children[element.Action].active = element.Valid;
            if(element.Action==2){
                this.myAutoButtons.children[element.Action].children[2].getComponent(cc.Label).string = Utils.formatNumber(element.Value);
            }
        });
    }

    ShowAutoN(AutoList){
        cc.log("AutoList" +JSON.stringify(AutoList));
        this.myButtons.active = false;
        this.myAutoButtons.active = true;
        AutoList.forEach(element => {
            
            this.myAutoButtons.children[element.action].active = element.valid;
            if(element.action==2){
                this.myAutoButtons.children[element.action].children[2].getComponent(cc.Label).string = Utils.formatMoney(element.value);
            }
        });
    }
    playerFlipCards( account,  cards  ){
        var playerViewTmpe = this.getPlayviewWithID(account);
        if (playerViewTmpe == null) return;
        cc.log("playerFlipCards" );
        playerViewTmpe.latBai( cards.map(card => card.OrdinalValue),true);

    }

   @property(cc.Button)
    btnChipBuy: cc.Button = null;
    notifyChangePhrase( roomInfo,  timeout  ){
        this.btnChipBuy.interactable = roomInfo.GameLoop.Phrase>1?false:true;
    // Waiting = 1, //chờ
    // PreFlop = 2
    // Flop = 3
    // Turn = 4
    // River = 5
    // Showdown = 6
    // AfterShowdown = 7
         this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + (roomInfo.CurrentGameLoopId==-1?"":roomInfo.CurrentGameLoopId.toString()); 
    // this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + roomInfo.CurrentGameLoopId; 
    cc.log("notifyChangePhrase");
    cc.log(JSON.stringify(roomInfo));

            //reset check mask auto
            this.myAutoButtons.children.forEach(element => {
                element.children[1].active = false;
            });
            // 
            var thiz = this;
            this.lblThongBao.node.parent.active = false;
            var lsPlaer = this.convertObjectToArray(roomInfo.Players);
    
            //update pot
            thiz.potParent.removeAllChildren();
            var distanceX = 156 + 5;  // Khoảng cách giữa các node (156 là chiều rộng của mỗi node, 5 là khoảng cách giữa các node)
            var totalWidth = distanceX * (roomInfo.GameLoop.PotAllInSplit.length - 1) + 156;  // Tổng chiều rộng của tất cả các node
            
            for (let index = 0; index < roomInfo.GameLoop.PotAllInSplit.length; index++) {
                var posOrigin = cc.v3(
                    index * distanceX - (totalWidth / 2) + 156 / 2,  // Căn giữa các node
                    0, 
                    0
                );
                var potTemp = cc.instantiate(thiz.potClone);
                potTemp.active = true;
                potTemp.setPosition(posOrigin);
                potTemp.children[0].getComponent(cc.Label).string = Utils.formatMoney(roomInfo.GameLoop.PotAllInSplit[index]);
                potTemp.parent =  thiz.potParent;
            }


            if (roomInfo.GameLoop.Phrase < 7)
                {
                    for (let index = 0; index < lsPlaer.length; index++) {
                        const element = lsPlaer[index];
                        
                        if(element.AccountID == this.playerMe.AccountID){
                            this.ShowAutoC(element.AutoList);
                        }
                     }
               
                }
          
            if(roomInfo.GameLoop.Phrase>2 && roomInfo.GameLoop.Phrase<6){
                this.playSound("collect_chips",false);
            }

            //update chips bet 

            setTimeout(() => {
                   for (let index = 0; index < lsPlaer.length; index++) {
                const element = lsPlaer[index];
                
                    var playerViewTmpe = this.getPlayviewWithID(element.AccountID);
                     if (playerViewTmpe == null) continue;
                     cc.log("reset chip==" + element.BetInRound);
                     playerViewTmpe.resetAction();
                     playerViewTmpe.updateChipGold(element.Chips);
                     if(roomInfo.GameLoop.Phrase>2){ //gom chip
                         playerViewTmpe.moveChipToPot(element.BetInRound,this.potParent); 
                     }else  if(roomInfo.GameLoop.Phrase==2){ // chip big small
                        playerViewTmpe.moveChipToBet(element.BetInRound,false,element.BetInRound); 
                     }
                          // neu het tien show popup mua chip
            if(roomInfo.GameLoop.Phrase == 1 ){
                if( thiz.playerMe.AccountID == element.AccountID && element.Chips==0){

                    this.showPopupMuaChip();
                }
              
            }
            if(roomInfo.GameLoop.Phrase == 2 ){
                playerViewTmpe.activeAvatar(1);
              
            }

         
             }
            }, 600);
         
    
           if(roomInfo.GameLoop.Phrase == 1 ) //cho 
            {
                this.myButtons.children.forEach(element => {
                    element.active = false;
                });
                this.myAutoButtons.active = false;
                this.btnLatBai.active = false;
               
        
                     this.lblThongBao.node.parent.active = true;
                           this.lblThongBao.string = App.instance.getTextLang("me6");
                           this.clearAllCardTable();
                         
            this.listPlayerView.forEach(element => {
                element.resetNewGame();
            });
            // 

           }else if(roomInfo.GameLoop.Phrase == 2){ //chia bai cho use 
           
            for (let index = 0; index < lsPlaer.length; index++) {
                const element = lsPlaer[index];
                // Cards
                if(element.AccountID == this.playerMe.AccountID){
                    this.dealCardsFake(element);
                }else{
                    var playerViewTmpe = this.getPlayviewWithID(element.AccountID);
                     if (playerViewTmpe == null) continue;
                     this.dealCardForOther(playerViewTmpe,element);
                     playerViewTmpe.cardList.active = true;
                }
            }
           }else if(roomInfo.GameLoop.Phrase == 3 || roomInfo.GameLoop.Phrase == 4 || roomInfo.GameLoop.Phrase == 5){ // chia 3 la bai
    
           var  phase3 = roomInfo.GameLoop.CommunityCards.slice(0, 3);
           if(roomInfo.GameLoop.Phrase == 4 || roomInfo.GameLoop.Phrase == 5){
            var  phase3 =  roomInfo.GameLoop.CommunityCards.slice(-1);
           }
           cc.log("roomInfo.GameLoop.Phrase"+roomInfo.GameLoop.Phrase);
        
            var cards =  phase3.map(card => card.OrdinalValue); 
            setTimeout(() => {
                  thiz.dealCardsTable(cards,true);
            }, 600);
            
           }
           else if(roomInfo.GameLoop.Phrase == 6){     

            if(this.cardOnTableParent.childrenCount<5){
                var newCard = roomInfo.GameLoop.CommunityCards.length-this.cardOnTableParent.childrenCount;
                if(newCard>0){
                    var  phase3 =  roomInfo.GameLoop.CommunityCards.slice(-newCard);
                    if(phase3.length>0){
                        var cards  = phase3.map(card => card.OrdinalValue); 
                        this.dealCardsTable(cards,true);
                    }
                 
                }
             
            }
          
           
            // lat 5 con

            this.myButtons.children.forEach(element => {
                element.active = false;
            });
            this.pokerBetBar.node.active = false;


            setTimeout(() => {
                thiz.showEndResulft(lsPlaer);
            }, 1000);

           }
           else if(roomInfo.GameLoop.Phrase == 7){
                //show button lat bai

                cc.log("=================== SHOWDOWN ========")
           }
    }


    dealCardForOther(playerViewTmpe:PlayviewPK,Player){
        
        var cards =  Player.Cards.map(card => card.OrdinalValue); 
        var thiz = this;
        var cardClone =  this.cardClone;
        let worldPos = thiz.deskPoient.convertToWorldSpaceAR(cc.Vec2.ZERO);
        var posDealer = this.node.convertToNodeSpaceAR(worldPos);
        const newOrder: number[] = [8, 9];

        var lsCardFake = [];
        for (var i = 0; i < cards.length; i++) {

            (()=>{
                var inew  = i;
                
               
                if (!thiz.isbackground) {
                    cc.log("dealCardsFake");
                    var cardNode = cc.instantiate(cardClone);
                
                cardNode.parent =  thiz.node;
                cardNode.setPosition(posDealer);
                cardNode.setSiblingIndex(newOrder[inew]);
                var cardTemp = playerViewTmpe.cardList.children[inew];
                cardNode.rotation = cardTemp.rotation;
                lsCardFake.push(cardNode);
                var posOrigin =  thiz.node.convertToNodeSpaceAR(cardTemp.convertToWorldSpaceAR(cc.Vec2.ZERO)); 
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
    
                    cardNode.setScale(cc.v3(-1, 1, 1));
                    let t1 = cc.tween(cardNode).delay(0.02*inew).call(() => {
                        cc.log("vaa" + inew);
                        cardNode.active = true;
                        this.playSound("chiabai",false);
                    });
                    let t2 = cc.tween(cardNode)
                        .to(0.25, { position: cc.v3(posOrigin.x,posOrigin.y,0) });
                    let t3 = cc.tween(cardNode).to(0.1, {
                        // Bind position
                        scaleX: 0,
                    }
                    ).call(() => {
                        setTimeout(() => {
                            cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
                        }, 0); 
                    });
    
                    let t4 = cc.tween(cardNode).delay(0.1).to(0.1, {
                        // Bind position
                        scaleX: 1,
                    }
                    ).call(()=>{
                     
                        if(inew == cards.length-1){
                           lsCardFake.forEach(element => {
                                element.destroy();
                            });
                            lsCardFake = [];
                            
                        }

                    });
    
                    cc.tween(cardNode).sequence(t1, t2, t3, t4).start();
    
    
                }
                else {
                    
                    // cardNode.active = true;
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                   
                    // cardNode.setPosition(posOrigin);
                    cc.log("finihs delcar " + inew + "=---" + (cards.length-1));
                    if(inew == cards.length-1){
                      playerViewTmpe.cardList.active = true;
                    }
                }
            })();
           


        }
    }


    showPopupMuaChip(){
        var thiz = this;
        setTimeout(() => {
            cc.log("Mua chip di"); 
            thiz.buyChip.showWithData(thiz.dataRoom.MinBet,thiz.dataRoom.CurrencyId,false);
        }, 1000);
    }


    actShowBuyChip(){
        this.buyChip.showWithData(this.dataRoom.MinBet,this.dataRoom.CurrencyId,false);
    }



    moveChipPotToPlayer(playerView,chip){
        var thiz = this;
        if(this.potParent.childrenCount>0){
            this.playSound("chips_moving_to_winner",false);
       
            var nodePot = this.potParent.children[this.potParent.childrenCount-1].children[0];
            var posOrigin = playerView.node.convertToNodeSpaceAR(nodePot.convertToWorldSpaceAR(cc.Vec2.ZERO));
            var potTemp = cc.instantiate(thiz.potClone);
 
            potTemp.active = true;
            potTemp.children[0].getComponent(cc.Label).string = Utils.formatNumber(chip);
            potTemp.parent =  playerView.node;
            cc.log("moveChipToPot"+ chip);
            thiz.potParent.children.forEach(element => {
                element.active = false;
            });
         
    
            potTemp.setPosition(posOrigin);
             
                cc.tween(potTemp)
                .to(1, { position: cc.v3(0,0,0)  })
                .call(() => {
                   
                    potTemp.destroy(); 
                })
                .start();
        }

    }


    checkInactivity() {
        App.instance.inactivityTimer++;
        var thiz = this;
        if (App.instance.inactivityTimer == this.INACTIVITY_TIMEOUT&& !thiz.isRegLeave) {
      
            App.instance.ShowAlertDialog(App.instance.getTextLang("ca247"));
            this.scheduleOnce(() => {
                if(App.instance.inactivityTimer >= thiz.INACTIVITY_TIMEOUT ){
                    App.instance.alertDialog.dismiss();
                     CardGameSignalRClient.getInstance().send('LeaveGame', [], (data) => {
                                  cc.log(data);
                                  if(data && !this.isGotolobby){
                                      App.instance.showToast(App.instance.getTextLang("me8"));
                                  }
                                         });
                }
                
              }, 3);
          
        }
    }

    showEndResulft(lsPlaer){
            var thiz = this;
 // show resuft win lose
        var maxName = -1;
        var playerHight = null;
        this.listPlayerView.forEach(element => {
            element.startCountDown(0);
        });
        for (let index = 0; index < lsPlaer.length; index++) {

      (()=>{
                 const element = lsPlaer[index];
            if(element.BestHandInfo.Name > maxName){
                maxName = element.BestHandInfo.Name;
                playerHight = element;
            }
            // Cards
            if(element.Status == 1){
              var playerViewTmpe = thiz.getPlayviewWithID(element.AccountID);
            if (playerViewTmpe != null){
  var cards =  element.Cards.map(card => card.OrdinalValue); 

            playerViewTmpe.latBai(cards, !thiz.isbackground);
            if(element.Result.Refund && element.Result.Refund >0 ){
                playerViewTmpe.createEffectMoney(element.Result.Refund);
            }else if(element.Result.Revenue && element.Result.Revenue>0){
                playerViewTmpe.createEffectMoney(element.Result.Revenue );
            }
   
            playerViewTmpe.updateChipGold(element.Chips);
                       
            
            if(element.Result.Refund>0){
                cc.log("move chip to player" + element.AccountID);
                  cc.log("move chip to player" + element.Result.Refund);
                thiz.playSound("thang",false);
                playerViewTmpe.showEffect(8);
                thiz.moveChipPotToPlayer(playerViewTmpe,element.Result.Refund);
            }else if (element.Result.Revenue<0){ // not draw
                playerViewTmpe.showEffect(9);
            } 
            }
          
            }
            
                })();

           
        }

        if(maxName>-1){
            this.namePoker.children[maxName].active = true;
            var playerViewTmpe = this.getPlayviewWithID(playerHight.AccountID);
            if (playerViewTmpe == null) return;

            //higtpar
            this.cardOnTableParent.children.forEach(element => {
                element.children[0].active = true;
                for (const CardValue of playerHight.BestHandInfo.CardValues) {
                    var isStop = false;
                    for (const item of CardValue) {
                        if (item.toString() ===   element.name ) {
                            isStop  = true;
                            element.children[0].active = false;
                            break;
                        }
                        console.log(item);
                    }
                    if(isStop){
                        break;
                    }
                }
            });
            
            // hight player
            playerViewTmpe.cardList.children.forEach(element => {
       
                for (const CardValue of playerHight.BestHandInfo.CardValues) {
                    var isStop = false;
                    for (const item of CardValue) {
                        if (item.toString() ===   element.name ) {
                            isStop  = true;
                            element.children[0].active = false;
                            break;
                        }
                        console.log(item);
                    }
                    if(isStop){
                        break;
                    }
                }
                
              
            });
            
        }
     
      
    }

    isbackground = false;


    onGamePause() {
        this.isbackground = true;
        // Xử lý khi game bị đưa xuống nền
    }
    
    onGameResume() {
        this.isbackground = false;
        // Xử lý khi game quay lại từ nền
    }
    onFinishDealCard(cards){


        for (let index = 0; index < 2; index++) {
            this.playerViewMe.cardList.children[index].getComponent(cc.Sprite).spriteFrame = this.arrCardSprite.children[cards[index]].getComponent(cc.Sprite).spriteFrame;
            
        }
        this.playerViewMe.cardList.active = true;
        
        this.lsCardFake.forEach(element => {
            element.destroy();
        });
        this.lsCardFake = [];
    }
    lsCardFake = [];
    dealCardsFake(Player) {
        cc.log("dealCards");

        var playerViewTmpe = this.getPlayviewWithID(Player.AccountID);
        if (playerViewTmpe == null) return;
        
        var cards =  Player.Cards.map(card => card.OrdinalValue); 
        var thiz = this;
        var cardClone =  this.cardClone;
        let worldPos = thiz.deskPoient.convertToWorldSpaceAR(cc.Vec2.ZERO);
        var posDealer = this.node.convertToNodeSpaceAR(worldPos);
        const newOrder: number[] = [8, 9];


        for (var i = 0; i < cards.length; i++) {

            (()=>{
                var inew  = i;
                
               
                if (!thiz.isbackground) {
                    cc.log("dealCardsFake");
                    var cardNode = cc.instantiate(cardClone);
                
                cardNode.parent =  thiz.node;
                cardNode.setPosition(posDealer);
                cardNode.setSiblingIndex(newOrder[inew]);
                var cardTemp = playerViewTmpe.cardList.children[inew];
                cardNode.rotation = cardTemp.rotation;
                thiz.lsCardFake.push(cardNode);
                var posOrigin =  thiz.node.convertToNodeSpaceAR(cardTemp.convertToWorldSpaceAR(cc.Vec2.ZERO)); 
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
    
                    cardNode.setScale(cc.v3(-1, 1, 1));
                    let t1 = cc.tween(cardNode).delay(0.02*inew).call(() => {
                        cc.log("vaa" + inew);
                        cardNode.active = true;
                        this.playSound("chiabai",false);
                    });
                    let t2 = cc.tween(cardNode)
                        .to(0.25, { position: cc.v3(posOrigin.x,posOrigin.y,0) });
                    let t3 = cc.tween(cardNode).to(0.1, {
                        // Bind position
                        scaleX: 0,
                    }
                    ).call(() => {
                        setTimeout(() => {
                            cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                        }, 0); 
                    });
    
                    let t4 = cc.tween(cardNode).delay(0.1).to(0.1, {
                        // Bind position
                        scaleX: 1,
                    }
                    ).call(()=>{
                     
                        if(inew == cards.length-1){
                             thiz.onFinishDealCard(cards);
                        }

                    });
    
                    cc.tween(cardNode).sequence(t1, t2, t3, t4).start();
    
    
                }
                else {
                    
                    // cardNode.active = true;
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                   
                    // cardNode.setPosition(posOrigin);
                    cc.log("finihs delcar " + inew + "=---" + (cards.length-1));
                    if(inew == cards.length-1){
                        cc.log("finihs delcar");
                         thiz.onFinishDealCard(cards);
                    }
                }
            })();
           


        }
    }



    dealCardsTable(cards , isAninamtion) {
        cc.log("dealCards" + cards);

         
        var thiz = this;
        var cardClone =  this.cardCloneTable;
        let worldPos = thiz.deskPoient.convertToWorldSpaceAR(cc.Vec2.ZERO);
        var posDealer = this.cardOnTableParent.convertToNodeSpaceAR(worldPos);
  


        for (var i = 0; i < cards.length; i++) {

            (()=>{
                var inew  = i;
                var cardNode = cc.instantiate(cardClone);
                var posOrigin =   new cc.Vec3((60+thiz.cardOnTableParent.childrenCount*140), 0, 0);
                cardNode.parent =  thiz.cardOnTableParent;
                cardNode.setPosition(posDealer);
                cardNode.name = cards[inew].toString();
       
             
                if (!thiz.isbackground && isAninamtion ) {
                

                // cc.log("posOrigin"+posOrigin);
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
    
                    cardNode.setScale(cc.v3(-1, 1, 1));
                    let t1 = cc.tween(cardNode).delay(0.02*inew).call(() => {
                        cc.log("vl" + inew);
                        cardNode.active = true;
                    });
                    let t2 = cc.tween(cardNode)
                        .to(0.25, { position: cc.v3(posOrigin.x,posOrigin.y,0) });
                    let t3 = cc.tween(cardNode).to(0.1, {
                        // Bind position
                        scaleX: 0,
                    }
                    ).call(() => {
                        setTimeout(() => {
                            cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                        }, 0); 
                    });
    
                    let t4 = cc.tween(cardNode).delay(0.1).to(0.1, {
                        // Bind position
                        scaleX: 1,
                    }
                    ).call(()=>{
                     
                        

                    });
    
                    cc.tween(cardNode).sequence(t1, t2, t3, t4).start();
    
    
                }
                else {
                    
                     cardNode.active = true;
                     cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                   
                     cardNode.setPosition(posOrigin);
                   
                }
            })();
           


        }
    }

    convertObjectToArray(Players){

        var lsPlaer = [];
        const playerIDs = Object.keys(Players);
    
        for (let i = 0; i < playerIDs.length; i++) {
            const playerID = playerIDs[i];
            lsPlaer.push(Players[playerID]);
        }
        return lsPlaer;
    }


    notifyFinishActions( accountId,  state,  pot,  player){
        // cc.log("notifyFinishActions" + JSON.stringify(state));
        // cc.log(JSON.stringify(player));
        var playerView = this.getPlayviewWithID(accountId);
        if (playerView == null) return;
        // const index = this.listPlayer.findIndex(player => player.AccountID === accountId);
  
        // if (index !== -1) {
        //     // Cập nhật thông tin player nếu tìm thấy
        //     this.listPlayer[index] = { ...this.listPlayer[index], ...player };
        // } 

           // Thao tác người chơi:
        // Chờ: -1,
        // Check: 1,
        // Bet: 2,
        // Call: 3,
        // Raise: 4,
        // Fold: 5,
        // All In: 6,
        // Flip: 7

       this.ShowAutoN(player.autoList);

        switch (state.action) {
            case 2:
             case 3:
                case 4: 
                case 7:  
                this.playSound("chipchamnhau",false);
                break;
                case 5:
                    playerView.activeAvatar(2);
                    playerView.startCountDown(0);
                    this.playSound("upbo",false);
                    this.myButtons.children.forEach(element => {
                        element.active = false;
                    });
                    this.pokerBetBar.node.active = false;
                    break;
        
            default:
                break;
        }
        playerView.updateAction(state,player);
    }

    playAnimationDealer() {
        var thiz = this;
        let frameIndex = 0;
        if(thiz.spriteFramesDe.length<=0) return;
        const finishDealer = () => {
            console.log("Animation completed!");
            thiz.playAnimationnormal();
        };
        this.dealer.scheduleOnce(() => {
            this.dealer.spriteFrame = thiz.spriteFramesDe[frameIndex];
            frameIndex = (frameIndex + 1) % thiz.spriteFramesDe.length; 
        
            // Gọi callback khi hoàn thành
            finishDealer();
        }, 1/30);

    }
    

    playAnimationnormal() {
        var thiz = this;
        let index = 0;
        let forward = true; // Biến kiểm soát hướng
        const frameRate =1/30; // 100ms mỗi frame
        if(thiz.spriteFramesNo.length<=0) return;
        // Hủy mọi schedule trước khi đặt mới
        this.dealer.unscheduleAllCallbacks();
        
        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesNo[index];
        
            // Nếu đến cuối mảng, đảo chiều
            if (index === thiz.spriteFramesNo.length - 1) {
                forward = false;
            } 
            // Nếu đến đầu mảng, đổi hướng đi tiếp
            else if (index === 0) {
                forward = true;
            }
        
            // Cập nhật index theo hướng
            index += forward ? 1 : -1;
        }, frameRate, cc.macro.REPEAT_FOREVER);
        // 
        
    }


    private intervalId: number = 0;


    PingPong() {
        CardGameSignalRClient.getInstance().send('PingPong', [], (data) => {
            cc.log(data);
                 });
        // Thực hiện các hành động khác trong hàm này
    }
    onDestroy() {
        // Dừng interval khi component bị hủy
        if (this.intervalId !== 0) {
            clearInterval(this.intervalId);
        }
        CardGameSignalRClient.getInstance().dontReceive();
        this.unschedule(this.checkInactivity);
    }

    

   
    updateConnectionStatus(plaerID,status){


        if(this.playerMe.AccountID == plaerID){
        this.playerMe.isRegExit = false;
        if( status == 2)
                    this.playerMe.isRegExit = true;
                }

        var playerView = this.getPlayviewWithID(plaerID);
        if (playerView == null) return;
        
        playerView.updateStatus(status);
    }
   
   

  
    
 chatIngame: ChatInGame = null;
    actChat(){
        //this.playSound("click",false);
        App.instance.inactivityTimer = 0;
                var thiz = this;
                if(this.chatIngame == null){
                    let cb = (prefab) => {
                        this.chatIngame = cc.instantiate(prefab).getComponent("ChatInGame");
                        this.node.addChild(this.chatIngame.node);
                        this.chatIngame.show(Configs.GameId88.Poker);
                       
                      };
                      BundleControl.loadPrefabPopup("PrefabPopup/ChatInGame", cb);
                }else{
                    this.chatIngame.show(Configs.GameId88.Poker);
                }
               
    }

    loadDir(){
      
            const bundle = cc.assetManager.getBundle("Poker");
            if (!bundle) {
                cc.error("Không tìm thấy bundle 'Poker'");
                return;
            }
            var lang  = LanguageMananger.instance.languageCode;
            if(lang == "id"){
                lang = "en";
            }
            if(lang == "th"){
                lang = "km";
            }

            var dirs = ["cu-lu-","sanh-","thung-","thung-pha-sanh-","tu-quy-en"];
            dirs.forEach(element => {
                bundle.loadDir( "res/text/" + lang +"/"+(element+lang), cc.Texture2D, (err, textures) => {
                    if (err) {
                        cc.error("Lỗi khi tải ảnh:", err);
                        return;
                    }
        
                    cc.log("tai thanh cong" + "res/text/" + lang +"/"+element+lang);
                });
            });
           
       
    }

    startGame(data,isAni){
        

        this.clearAllCardTable();

        var mycardTable =  data.GameLoop.CommunityCards.map(card => card.OrdinalValue); 
      
        this.dealCardsTable(mycardTable,false);
       
       
        this.lblThongBao.node.parent.active = false;
        this.listPlayerView.forEach(element => {
            element.resetNewGame();
        });
        //this.playSound("s_chiabai",false);
        
        const playerIDs = Object.keys(data.Players);
        var thiz = this;

        for (let i = 0; i < playerIDs.length; i++) {

            
            var inew = i;
            const playerID = playerIDs[inew];
            const player = data.Players[playerID];
           
           
            if(player.AccountID!=thiz.playerMe.AccountID){
                //chia card ao
                var playerView = thiz.getPlayviewWithID(player.AccountID);
                if (playerView != null) {
                //     var mycard =  player.Cards.map(card => card.OrdinalValue); 
                //      playerView.cardList.active = true;
                if(player.Status == 1){
                    playerView.cardList.active = true;
                    playerView.activeAvatar(player.Status);
                }
               
                }   
             
            }else{
                var mycard =  player.Cards.map(card => card.OrdinalValue); 
                cc.log("thiz.playerMe"+ thiz.playerMe.AccountID);
                cc.log("thiz.mycard"+ mycard);
                thiz.playerViewMe.activeAvatar(player.Status);
                if(mycard.length == 2){
                    for (let index = 0; index < 2; index++) {
                        if(mycard[index]>-1){
                            thiz.playerViewMe.cardList.children[index].getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[mycard[index]].getComponent(cc.Sprite).spriteFrame;
                        }
                      
                    }
                    thiz.playerViewMe.cardList.active = true;
                }
               
            }
           
        }
       if(data.GameLoop.Phrase == 1){
        this.lblThongBao.node.parent.active = true;
        this.lblThongBao.string = App.instance.getTextLang("me6");
       }
      
        
    }

    
    arrayAction = [];


    actAutoPlay(event,action){
        //
        this.myAutoButtons.children[parseInt(action)].children[1].active = ! this.myAutoButtons.children[parseInt(action)].children[1].active;
        if(this.myAutoButtons.children[parseInt(action)].children[1].active){
            CardGameSignalRClient.getInstance().send('RegisterAuto', [parseInt(action)], (data) => {
                cc.log(data);
                     });
        }else{
            CardGameSignalRClient.getInstance().send('RegisterAuto', [0], (data) => {
                cc.log(data);
                     });
        }
       
    }

   
    notifyStartActions(timeout,data){
        // Thao tác người chơi:
        // Chờ: -1,
        // Check: 1,
        // Bet: 2,
        // Call: 3,
        // Raise: 4,
        // Fold: 5,
        // All In: 6,
        // Flip: 7
        cc.log("notifyStartActions");
        cc.log(JSON.stringify(data));

        this.playSound("Turn",false);
        var thiz = this;
        var idplaer =  data.Account;

//        
        thiz.pokerBetBar.node.active = false;
        this.myButtons.children.forEach(element => {
            element.active = false;
        });

   

        if(idplaer == this.playerMe.AccountID){
        this.arrayAction = data.Actions;
        this.myAutoButtons.active = false;
        this.myButtons.active = true;
        this.arrayAction.forEach(element => {
            thiz.myButtons.children[element.Action].active = true;
            if(element.Action == 3){
                this.lblMoneyCall.string = Utils.formatNumber(element.Min);
            }
            if(element.Action == 7){
                thiz.btnLatBai.active = true;
            }
         
        });
            
        }
        
        this.listPlayerView.forEach(element => {
            element.startCountDown(0);
        });
    var playerView = this.getPlayviewWithID(idplaer );
    if (playerView == null) return;
   
    playerView.startCountDown(timeout );
    }
    
    playerLeave(idLeave,data){

        //this.playSound("leave_room",false);
        
        if(idLeave == this.playerMe.AccountID){
            this.dataRoom = null;
             this.isGotolobby = true;
              if(data && data.code == 11007){
                var stringaler = App.instance.getTextLang("me11007");
                App.instance.ShowAlertDialog(Utils.formatString(stringaler, Utils.formatNumber(data.prms[0]),data.prms[0]==1? App.instance.getTextLang("hi25"):"Tipzo"));
            }
            this.node.removeFromParent();
                       App.instance.gotoLobby();
            return;
        }else{
            this.playSound("leave_room",false);
        }
        for (var i = 0; i < this.listPlayer.length; i++) {
            if (this.listPlayer[i]['AccountID'] == idLeave) {
                this.listPlayer.splice(i, 1);
                break;
            }
        }
        for (var i = 0; i < this.listPlayerView.length; i++) {
            if (this.listPlayerView[i].player != null && this.listPlayerView[i].player.AccountID == idLeave) {
                this.listPlayerView[i].updateInfor(null);
                break;
            }
        }
        // this.updateDataRoom(data);

    }
    actExitLobby(){
    
    }
 isRegLeave = false;
    isGotolobby = false;
    actLeaveGame(){
        //this.playSound("click",false);
         if( this.playerMe.isRegExit){
                              CardGameSignalRClient.getInstance().send('UnregisterLeaveRoom', [], (data) => {
                                  cc.log(data);
                                    this.isRegLeave = false;
                                  if(data && !this.isGotolobby){
                                      App.instance.showToast(App.instance.getTextLang("me9"));
                                  }
                                         }); 
                  
                          }else{
                              CardGameSignalRClient.getInstance().send('LeaveGame', [], (data) => {
                                  cc.log(data);
                                    this.isRegLeave = true;
                                  if(data && !this.isGotolobby){
                                      App.instance.showToast(App.instance.getTextLang("me8"));
                                  }
                                         });
                          }
                 

       
    }

    playerJoin(data){
        cc.log("playerJoin"+ data);
        this.playSound("join_room",false);
      this.listPlayer.push(data);
      this.fillPlayerToSlot();
    }

    // responsvive==================
    dataRoom;
    @property(cc.Label)
    lblTable: cc.Label = null;
    @property(cc.Label)
    lblTable2: cc.Label = null;
    @property(cc.Label)
    lblTable3: cc.Label = null;
    @property(cc.Node)
    vipTable: cc.Node = null;
    onHandleJoin(data,extra){

             this.lblTable.string = ( data.MoneyType == 1 ? App.instance.getTextLang("tb112") : App.instance.getTextLang("tb113"))+ ": " + data.Name;
              this.lblTable2.string = App.instance.getTextLang("iap38")+ ": " + Utils.formatNumber(data.MinBet) + (data.MoneyType == 1?" Tipzo" :" Coin") ;
            //   this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + data.CurrentGameLoopId; 
                this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + (data.CurrentGameLoopId==-1?"":data.CurrentGameLoopId.toString()); 
              this.vipTable.active = data.MoneyType != 1 ?true:false;
              this.vipTable.parent.children[1].color =new cc.Color().fromHEX(data.MoneyType != 1 ?'#513b21':"#003533"); 
        this.listPlayer = [];
        this.listPlayerView.forEach(element => {
            element.resetAll();
        });
    
        var dataRoom = data;
        const playerIDs = Object.keys(dataRoom.Players);
        //this.playSound("join_room",false);
        // Duyệt danh sách bằng vòng lặp for bình thường
        for (let i = 0; i < playerIDs.length; i++) {
            const playerID = playerIDs[i];
            const player = dataRoom.Players[playerID];
            var isExits = false;
            for (var j = 0; j < this.listPlayer.length; j++) {
                if(playerID == this.listPlayer[j]['AccountID']){
                    isExits = true;
                    break;
                }
               
            }
            if(!isExits){
                this.listPlayer.push(player);
            }
            // console.log(`Player ID: ${playerID}`);
            // console.log(`Nickname: ${player.Account.NickName}`);
            // console.log(`Gold: ${player.Account.GoldBalance}`);
            // console.log(`Status: ${player.Status}`);
            // console.log("-----------------------");
        }
        this.updateDataRoom(dataRoom);
        this.fillPlayerToSlot();
        this.startGame(dataRoom,false);
     

        // if(extra && extra.state.accountId){
            // this.notifyStartActions(extra.state.accountId, extra.time,extra.state.allowedActions) ;
        // }
    }
    updateDataRoom(dataRoom){
        this.dataRoom = dataRoom;
        var thiz = this;
        this.listPlayer.forEach(element => {
            element["isOwner"] = false;
            if(dataRoom.OwnerId == element.AccountID){
                element["isOwner"] = true;
            }
            const firstPart = element.AccountID.substring(0, element.AccountID.indexOf(":"))
            if (firstPart == Configs.Login.UserId) {
                thiz.playerMe = element;

            }
        });
        
    }

    clearAllCardTable() {
       this.cardOnTableParent.removeAllChildren();
       this.namePoker.children.forEach(element => {
        element.active = false;
       });
    }
   
   
 
   

    getPlayviewWithID(userId) {
        for (let index = 0; index < this.listPlayerView.length; index++) {
            const element = this.listPlayerView[index];
            if (element.player != null && element.player.AccountID == userId) {
                return element;
            }
        }
        return null;
    }
   
    fillPlayerToSlot() {
        var thiz = this;
       
        this.listPlayer.forEach(element => {
            var slot = this.getSlotNew(element['Position']);
            cc.log("slot new" + slot);
            thiz.listPlayerView[slot].updateInfor(element);
        });

        
    }
    actTheo(){
               App.instance.inactivityTimer = 0;
        var money = 0;
        this.playSound("button_call",false);
        for (let index = 0; index < this.arrayAction.length; index++) {
            const element = this.arrayAction[index];
           if(element.Action == 3){
            money = element.Min;
            cc.log(element.Min);
            cc.log(element.Max);
           }
        }
        cc.log(money);
        
        CardGameSignalRClient.getInstance().send('Bet', [money,3], (data) => {
            cc.log(data);
                 });
    }

    actXaLang(){
               App.instance.inactivityTimer = 0;
        this.playSound("button_all_in",false);
        CardGameSignalRClient.getInstance().send('Bet', [0,6], (data) => {
            cc.log(data);
                 });
    }

    actLatBai(){
               App.instance.inactivityTimer = 0;
        this.playSound("button_all_in",false);
        var thiz = this;
        CardGameSignalRClient.getInstance().send('FlipCards', [], (data) => {
            cc.log(data);
            thiz.btnLatBai.active = false;
                 });
    }

    actBoBai(){
              App.instance.inactivityTimer = 0;
        CardGameSignalRClient.getInstance().send('Bet', [0,5], (data) => {
            cc.log(data);
                 });
    }
    actCheck(){
               App.instance.inactivityTimer = 0;
        this.playSound("button_check",false);
        
        CardGameSignalRClient.getInstance().send('Bet', [0,1], (data) => {
            cc.log(data);
                 });
    }

    actBet(){ //show action bet
       App.instance.inactivityTimer = 0;

    for (let index = 0; index < this.arrayAction.length; index++) {
                const element = this.arrayAction[index];
            if(element.Action == 2 || element.Action == 4){
                this.pokerBetBar.show(element);
                break;
            }
            }
        
        }

   


    // Chờ: -1,
    //     Check: 1,
    //     Bet: 2,
    //     Call: 3,
    //     Raise: 4,
    //     Fold: 5,
    //     All In: 6,

    actXacNhan(){
       App.instance.inactivityTimer = 0;
    }



   playerCheckAuto(action){
        this.myAutoButtons.children.forEach(element => {
            element.children[1].active = false;
        });

         this.myAutoButtons.children[action].children[1].active = true;
    }   

    getSlotNew(slot) {
        var slotNew = slot - this.playerMe.Position;
        if(slotNew<0){
            slotNew =this.maxPlayer + slotNew;
        }
        return slotNew;
    }
    getIDCard(card) {
        
        return card.idCard;
    }

   
    // update (dt) {}
}
