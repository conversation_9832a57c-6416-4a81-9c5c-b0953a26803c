// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import BundleControl from "../../Loading/src/BundleControl";
import ChatInGame from "../../Lobby/ChatInGame/ChatInGame";
import App from "../../Lobby/LobbyScript/Script/common/App";
import Utils from "../../Lobby/LobbyScript/Script/common/Utils";
import CardGameSignalRClient from "../../Lobby/LobbyScript/Script/networks/CardGameSignalRClient";
import Configs from "../../Lobby/MoveScript/Configs";
import { ListCardMauBinh } from "./ListCardMauBinh";
import MauBinhPlayView from "./MauBinhPlayView";

const {ccclass, property} = cc._decorator;

@ccclass
export default class MauBinhController extends cc.Component {


    // LIFE-CYCLE CALLBACKS:

    // onLoad () {}
    @property(ListCardMauBinh)
    lsCardMB:ListCardMauBinh = null;

    public static instance:MauBinhController = null ;
    // start () {
    //     setTimeout(() => {
    //         this.lsCardMB.fillData([1,2,3,4,5,6,7,8,9,10,11,12,13]);
    //     }, 1000);
    // }
    @property(cc.Node)
    deskPoient: cc.Node = null;


    @property(cc.Node)
    nodeXepBai: cc.Node = null;

    @property(cc.Node)
    cardClone: cc.Node = null;

  
    @property(MauBinhPlayView)
    listPlayerView: MauBinhPlayView[] = [];
    playerViewMe: MauBinhPlayView;
    cardCombo = [];

    listPlayer: any[] = [];
    maxPlayer:number = 4;
    playerMe: any;

    @property(cc.SpriteFrame)
    public frameCardBack: cc.SpriteFrame = null;



    @property(cc.Label)
    lblThongBao: cc.Label = null;
    @property(cc.Label)
  public  lblClone: cc.Label = null;

    @property(cc.Sprite)
    dealer: cc.Sprite;


    @property(cc.Label)
    lblGiay: cc.Label = null;


    @property(cc.Label)
    lblTable: cc.Label = null;
    @property(cc.Label)
    lblTable2: cc.Label = null;
    @property(cc.Label)
    lblTable3: cc.Label = null;
    @property(cc.Node)
    vipTable: cc.Node = null;


    @property(cc.Node)
    public arrCardSprite: cc.Node = null;

    @property(cc.AudioSource)
    sound: cc.AudioSource;

     // var bundle = cc.assetManager.getBundle("TienLen");
        // bundle.loadDir("res/win", cc.Texture2D, (err, textures) => {
        //     if (err) {
        //         console.error("Lỗi khi tải ảnh:", err);
        //         return;
        //     }
    
        //     // Chuyển texture thành SpriteFrame
        //     let spriteFrames: cc.SpriteFrame[] = textures.map((texture: cc.Texture2D) => {
        //         return new cc.SpriteFrame(texture);
        //     });
    
        //     // Gọi hàm phát animation
        //     this.playAnimation(spriteFrames);
        // });
    

    playSound(nameSound,isloop){

        // var chidd = this.sound.node.getChildByName(nameSound);
        // if(chidd){
        //     this.sound.clip =   chidd.getComponent(cc.AudioSource).clip;
        //     this.sound.loop = isloop;
        //     this.sound.play();
        // }
 
    }
    INACTIVITY_TIMEOUT = 60;
    
    protected onLoad(): void {
        MauBinhController.instance = this;
        this.schedule(this.checkInactivity, 1);
    }


    checkInactivity() {
            App.instance.inactivityTimer++;
            var thiz = this;
            if (App.instance.inactivityTimer == this.INACTIVITY_TIMEOUT  && !thiz.isRegLeave) {
                
                App.instance.ShowAlertDialog(App.instance.getTextLang("ca247"));
                this.scheduleOnce(() => {
                    if(App.instance.inactivityTimer >= thiz.INACTIVITY_TIMEOUT ){
                        App.instance.alertDialog.dismiss();
                     CardGameSignalRClient.getInstance().send('LeaveGame', [], (data) => {
                                  cc.log(data);
                                  if(data && !this.isGotolobby){
                                      App.instance.showToast(App.instance.getTextLang("me8"));
                                  }
                                         });
                    }
                    
                  }, 3);
              
            }
        }

    start () {
        cc.game.on(cc.game.EVENT_HIDE, this.onGamePause, this);

        cc.game.on(cc.game.EVENT_SHOW, this.onGameResume, this);
        var thiz = this;
        this.lblThongBao.node.parent.active = false;
        this.nodeXepBai.active = false;
        
        this.btnXepLai.active = false;
      

        this.listPlayerView.forEach(element => {
            element.resetMB();
        });
       

        this.playerViewMe = this.listPlayerView[0];
        this.lsCardMB.sendCheck = function () {
            App.instance.inactivityTimer = 0;
            thiz.actCheckChi();
      
          };
       
        // setTimeout(() => {
        //     this.cardListMe.dealCards([1,2,3,4,5,6,7,8,9,10,11,12,13],true);
        //     this.cardListMe.onFinishDealCard = ()=>{
        // };
        // }, 100);
         CardGameSignalRClient.getInstance().receiveArray('updateAccount', (data: any,data2: any) => {
                     this.updateAccount(data,data2);
                 });
        
        CardGameSignalRClient.getInstance().receive('betOfAccountCF', (data: any) => {
      
     });
   
     setTimeout(() => {
        if(App.instance.DataPass.length>0)
            {
                this.onHandleJoin(App.instance.DataPass[0],App.instance.DataPass[1]);
            }    
     }, 10);
          CardGameSignalRClient.getInstance().receiveArray('recieveMessage', (accountId: string, _nickname: string, content: string) => {
                     if (accountId == `${Configs.Login.UserId}:${Configs.Login.PortalID}`) {
                        
                         thiz.playerViewMe.showChatMsg(content);
                     } else {
                         var playerView = thiz.getPlayviewWithID(accountId);
                         if (playerView == null) return;;
                         playerView.showChatMsg(content);
                     }
                 });
       
        CardGameSignalRClient.getInstance().receive('playerJoin', (data: any) => {
            this.playerJoin(data);
        });
       CardGameSignalRClient.getInstance().receiveArray('playerLeave', (data: any,data2: any) => {
                  this.playerLeave(data,data2);
              });

            CardGameSignalRClient.getInstance().receiveArray('message',  (data1: any,data2: any)=> {
                    if(data1.code){
                        App.instance.showToast(App.instance.getTextLang("me"+data1.code));
                    }
                });
        
        CardGameSignalRClient.getInstance().receiveArray('startActionTimer', (data1: any,data2: any,data3:any) => {
            this.startActionTimer(data1,data2,data3);
        });
        CardGameSignalRClient.getInstance().receive('startGame', (data: any) => {
            this.startGame(data,true);
        });
        CardGameSignalRClient.getInstance().receiveArray('checkSortChi',  (data1: any,data2: any,data3:any,data4:any,data5:any)=> {
            this.checkSortChi(data1,data2,data3,data4,data5);
        });
       

        CardGameSignalRClient.getInstance().receiveArray('ketThuc',  (data1: any,data2: any)=> {
            this.xepXong(data1,data2);
        });

        //hieu ung tien bay len
        // end game
        //3 nguoi
        CardGameSignalRClient.getInstance().receiveArray('updateConnectionStatus',  (data1: any,data2: any)=> {
            this.updateConnectionStatus(data1,data2);
        });
        CardGameSignalRClient.getInstance().receiveArray('showResult',  (data1: any,data2: any)=> {
            this.showResult(data1,data2);
        });
        CardGameSignalRClient.getInstance().receiveArray('haBai',  (data1: any)=> {
            this.haBai(data1);
        });
         CardGameSignalRClient.getInstance().receiveArray('updateGameSession',  (data1: any,data2: any)=> {
             this.updateSessin(data1,data2);
         });
        this.intervalId = setInterval(this.PingPong, 30000);

         var bundle = cc.assetManager.getBundle("MauBinh");
        bundle.loadDir("res/normal", cc.Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }
    
            // Chuyển texture thành SpriteFrame
            thiz.spriteFramesNo = textures.map((texture: cc.Texture2D) => {
                return new cc.SpriteFrame(texture);
            });
    
            // Gọi hàm phát animation
            this.playAnimationnormal();
        });

        bundle.loadDir("res/chiabai", cc.Texture2D, (err, textures) => {
            if (err) {
                console.error("Lỗi khi tải ảnh:", err);
                return;
            }
    
            // Chuyển texture thành SpriteFrame
            thiz.spriteFramesDe = textures.map((texture: cc.Texture2D) => {
                return new cc.SpriteFrame(texture);
            });
    
            // Gọi hàm phát animation
            // this.playAnimationDealer(spriteFrames);
        });
       
    }
    spriteFramesDe: cc.SpriteFrame[] = [];
    spriteFramesNo: cc.SpriteFrame[]= [];

       updateAccount(idLeave,data){
        var playerView = this.getPlayviewWithID(idLeave.AccountID);
                if (playerView != null) {
                    playerView.updateGold(this.MoneyType == 1? idLeave.GoldBalance:idLeave.CoinBalance);
                }
    }

    updateSessin(playerID,roomInfor){
        this.onHandleJoin(roomInfor,null);
    }

    playAnimationDealer() {
        var thiz = this;
        let frameIndex = 0;
        if(thiz.spriteFramesDe.length<=0) return;
        const finishDealer = () => {
            console.log("Animation completed!");
            thiz.playAnimationnormal();
        };
        this.dealer.scheduleOnce(() => {
            this.dealer.spriteFrame = thiz.spriteFramesDe[frameIndex];
            frameIndex = (frameIndex + 1) % thiz.spriteFramesDe.length; 
        
            // Gọi callback khi hoàn thành
            finishDealer();
        }, 1/30);

    }
    checkSortChi(accId,names,ids,binhlung,cards){
        cc.log(ids);
        this.lsCardMB.updateMyMB(names,ids,binhlung,cards);
    }

    playAnimationnormal() {
        var thiz = this;
        let index = 0;
        let forward = true; // Biến kiểm soát hướng
        const frameRate =1/30; // 100ms mỗi frame
        if(thiz.spriteFramesNo.length<=0) return;
        // Hủy mọi schedule trước khi đặt mới
        this.dealer.unscheduleAllCallbacks();
        
        this.dealer.schedule(() => {
            this.dealer.spriteFrame = thiz.spriteFramesNo[index];
        
            // Nếu đến cuối mảng, đảo chiều
            if (index === thiz.spriteFramesNo.length - 1) {
                forward = false;
            } 
            // Nếu đến đầu mảng, đổi hướng đi tiếp
            else if (index === 0) {
                forward = true;
            }
        
            // Cập nhật index theo hướng
            index += forward ? 1 : -1;
        }, frameRate, cc.macro.REPEAT_FOREVER);
        // 
        // let index = 0;
        // const frameRate = 0.3; // 100ms mỗi frame
        // this.dealer.unscheduleAllCallbacks(); 
        // // Lặp lại animation
        // this.dealer.schedule(() => {
        //     this.dealer.spriteFrame = thiz.spriteFramesNo[index];
        //     index = (index + 1) % thiz.spriteFramesNo.length; // Vòng lặp animation
        // },frameRate, cc.macro.REPEAT_FOREVER);
    }
    private intervalId: number = 0;
    PingPong() {
        CardGameSignalRClient.getInstance().send('PingPong', [], (data) => {
            cc.log(data);
                 });
        // Thực hiện các hành động khác trong hàm này
    }

    onDestroy() {
        // Dừng interval khi component bị hủy
        if (this.intervalId !== 0) {
            clearInterval(this.intervalId);
        }
        this.unschedule(this.checkInactivity);
        CardGameSignalRClient.getInstance().dontReceive();
    }

   

    
    updateConnectionStatus(plaerID,status){


        if(this.playerMe.AccountID == plaerID){
        this.playerMe.isRegExit = false;
        if( status == 2)
                    this.playerMe.isRegExit = true;
                }

        var playerView = this.getPlayviewWithID(plaerID);
        if (playerView == null) return;
        
        playerView.updateStatus(status);
    }

    haBai(data){
        this.nodeXepBai.active = false;
        
           var lsPlaer = this.convertObjectToArray(data.Players);

           for (let index = 0; index < lsPlaer.length; index++) {
                const element = lsPlaer[index];
               if(element.AccountID == this.playerMe.AccountID && element.Status == 1){
                 this.playerViewMe.cardList.active = true;
                      break;
               }
             
            }
    }
    xepXong(accountId,roomInfor){
        var playview =  this.getPlayviewWithID(accountId);
        if( playview != null){
            playview.showXepXong();
        }
    }
    boLuot(accountId,Roo){
        // var playerView = this.getPlayviewWithID(data1);
        // if (playerView == null) return;
        // playerView.showEffectBoluot();

        // {"type":1,"target":"boLuot","arguments":[100085]}
    }

    convertObjectToArray(Players){

        var lsPlaer = [];
        const playerIDs = Object.keys(Players);
    
        for (let i = 0; i < playerIDs.length; i++) {
            const playerID = playerIDs[i];
            lsPlaer.push(Players[playerID]);
        }
        return lsPlaer;
    }

  groupMauBinhResults(data: any[]): any[] {
    const groupedMap: { [key: string]: any } = {};

    data.forEach(item => {
        const key = item.AccountId + '_' + item.Index;
        if (groupedMap[key]) {
            groupedMap[key].Money += item.Money;
        } else {
            groupedMap[key] = { ...item }; // clone lại object
        }
    });

    return Object.values(groupedMap);
}

    soTungChi(indexChi,Players,MauBinhResultList,timez){

        this.lblThongBao.node.parent.active = true;
        this.lblThongBao.string = App.instance.getTextLang("ca28") + " " + (indexChi+1);

        for (let index = 0; index < Players.length; index++) {
            var temp = Players[index];
            var playview =  this.getPlayviewWithID(temp.AccountID);
            if(temp.Status == 1 && playview != null){
                //lat chi
                var mycard =  temp.BaiRac.map(card => card.OrdinalValue); 
                playview.latBai(indexChi,mycard);
                playview.startCountDownKetQua(timez);

                var groupedResult = this.groupMauBinhResults(MauBinhResultList);
                var dataChi =  groupedResult.find(item => item.AccountId === temp.AccountID && item.Index === (indexChi+1)); //ngu nguoi
                // hien tien va name chi

                playview.showNameChiAndMoney(dataChi);
            }
            
        }
    }

   
    showResult(data,timerObj){
        
        var thiz = this;
        this.nodeXepBai.active = false;
        // this.playerViewMe.cardList.active = true;
        this.btnXepLai.active = false;
       var lsPlaer = this.convertObjectToArray(data.Players);
        var thiz = this;

        // this.listPlayerView.forEach(element => {
        
        // });

     
            for (let index = 0; index < lsPlaer.length; index++) {
                const element = lsPlaer[index];
                var playerView = this.getPlayviewWithID(element.AccountID);
                if(playerView && element.Status==1){
                     playerView.hidenXepXong();
                }
            }


        // if(!thiz.isbackground){
        if(data.GameLoop.MauBinhToiTrang > 1 || data.GameLoop.BinhLung  >= data.CountActivePlayer - 1){ // toi trang ko can sep // ket thuc luon
            // xu li toi trang
            cc.log("show ket qua luon");  

             var maubinhMax = 0 ;
             data.GameLoop.SessionResult.MauBinhResultList.forEach(element => {
                if(element.ResultFamily>maubinhMax){
                    maubinhMax = element.ResultFamily;
                }
             });


            // lat ca bai show thang thua
            for (let index = 0; index < lsPlaer.length; index++) {
                const element = lsPlaer[index];
                var playerView = this.getPlayviewWithID(element.AccountID);
                if(playerView && element.Status==1){
                    var MauBinhResultList = data.GameLoop.SessionResult.MauBinhResultList;
                    
                 var groupedResult = this.groupMauBinhResults(MauBinhResultList);
                    var dataChi =  groupedResult.find(item => item.AccountId === element.AccountID );
                    if(dataChi !== undefined){

                        //show effect toi trang ra day
                        playerView.showNameChiAndMoneyToiTrang(dataChi,maubinhMax);
                    }else{
                        maubinhMax = element.MauBinhToiTrang;
                       dataChi= {"ResultFamily":element.MauBinhToiTrang,"Money":element.Money};
                       playerView.showNameChiAndMoneyToiTrang(dataChi,maubinhMax); 
                    }
                    var mycard =  element.BaiRac.map(card => card.OrdinalValue); 
                    playerView.latBaiAll(mycard);
                }
            }
            this.lblThongBao.node.parent.active = true;
            this.lblThongBao.string = App.instance.getTextLang("me18");

        }else{
            var timeSoChi = 5;
            var timeSap = 0;
            if(data.GameLoop.SapBaChi ){
                timeSoChi = 4;
                timeSap = 4;
                // ko có sap 3 chi // so bai binh thuong
            }
            // loai tru binh lung
            const binhLungPlayers = lsPlaer.filter(player => player.BinhLung);

            for (let index = 0; index < binhLungPlayers.length; index++) {
                const element = binhLungPlayers[index];
                var playerView = this.getPlayviewWithID(element.AccountID);
                if(playerView){
                    playerView.showNameChiLung(); //lat bai binh lung
                          var mycardLung =  element.BaiRac.map(card => card.OrdinalValue); 
                    playerView.latBaiAll(mycardLung);
                }
            }

            // tim nhung thang sap va bi bat sap case sap cheo
            const normalPlayers = lsPlaer.filter(player => !player.BinhLung);
            var MauBinhResultList = data.GameLoop.SessionResult.MauBinhResultList;
            this.node.stopAllActions();
                this.node.runAction(
                    cc.sequence(
                        cc.callFunc(() => {
                            cc.log("so chi 1");
                            thiz.soTungChi(0,normalPlayers,MauBinhResultList,timeSoChi);
                        }),
                        cc.delayTime(timeSoChi),
                        cc.callFunc(() => {
                            cc.log("so chi 2");
                            thiz.soTungChi(1,normalPlayers,MauBinhResultList,timeSoChi);
                        }),
                        cc.delayTime(timeSoChi),
                        cc.callFunc(() => {
                            cc.log("so chi 3");
                            thiz.soTungChi(2,normalPlayers,MauBinhResultList,timeSoChi);
                        }),
                        cc.delayTime(timeSap),
                        cc.callFunc(() => {
                            cc.log("sap ba chi");
                           if( data.GameLoop.SapBaChi){
                            this.sapBaChi(lsPlaer,MauBinhResultList,timeSoChi);
                           }
                           
                            ///
                            // thiz.soTungChi(2,normalPlayers,MauBinhResultList);
                        }),
                        cc.delayTime(timeSoChi),
                        cc.callFunc(() => {
                            cc.log("tong ket====");
                            this.tongket(lsPlaer);
                            ///
                            // thiz.soTungChi(2,normalPlayers,MauBinhResultList);
                        }),
                    
                    )
                );
          
          
        }
    // }
    // else{ // chi update tien

    // }

    }

   

    sapBaChi(Players,MauBinhResultList,timez){
        
        this.lblThongBao.node.parent.active = true;
        this.lblThongBao.string = App.instance.getTextLang("ca29");
        for (let index = 0; index < Players.length; index++) {
            var temp = Players[index];
            var playview =  this.getPlayviewWithID(temp.AccountID);
            if(temp.Status == 1 && playview != null){
              
                var dataChi =  MauBinhResultList.find(item => item.AccountId === temp.AccountID && item.Index === 4);
                // hien tien va name chi
                if(dataChi){
                    playview.startCountDownKetQua(timez);
                    playview.activeCard();
                    playview.showNameChiAndMoney(dataChi);
                }
               
            }
            
        }
    }

    tongket(lsPlaer){
        this.lblThongBao.node.parent.active = true;
        this.lblThongBao.string = App.instance.getTextLang("me18");
        for (let index = 0; index < lsPlaer.length; index++) {
            const element = lsPlaer[index];
            var playerView = this.getPlayviewWithID(element.AccountID);
            if(playerView){
                
                playerView.activeCard();
                playerView.showTongKet(element);
            }
            
        }
    }

   chatIngame: ChatInGame = null;
       actChat(){
               App.instance.inactivityTimer = 0;
           //this.playSound("click",false);
           App.instance.inactivityTimer = 0;
                   var thiz = this;
                   if(this.chatIngame == null){
                       let cb = (prefab) => {
                           this.chatIngame = cc.instantiate(prefab).getComponent("ChatInGame");
                           this.node.addChild(this.chatIngame.node);
                           this.chatIngame.show(Configs.GameId88.MauBinh);
                          
                         };
                         BundleControl.loadPrefabPopup("PrefabPopup/ChatInGame", cb);
                   }else{
                       this.chatIngame.show(Configs.GameId88.MauBinh);
                   }
                  
       }
   

    startGame(data,isAni){


       // data.GameSessionStep

        if(data.CurrentGameLoopId!=-1){
            this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + data.CurrentGameLoopId; 
        }
               
        this.node.stopAllActions();
        this.lblThongBao.node.parent.active = false;
        this.listPlayerView.forEach(element => {
            element.resetResulft();
        });
        this.playSound("s_chiabai",false);
        
        const playerIDs = Object.keys(data.Players);
        var thiz = this;
        if(data.GameLoop.Elapsed){
            // if(idplaer == this.playerMe.AccountID){
                this.lblGiay.unscheduleAllCallbacks(); 
                cc.log(" startCountDown" + data.GameLoop.Elapsed);
                this.startCountdown(data.GameLoop.Elapsed);
            // }
        }
        for (let i = 0; i < playerIDs.length; i++) {

            
            var inew = i;
            const playerID = playerIDs[inew];
            const player = data.Players[playerID];
            var playerView2 = thiz.getPlayviewWithID(player.AccountID);
            if (playerView2 != null  ) {
                playerView2.activeAvatar(player.Status);
            }   
           if(player.Status != 1){
            continue;
           }
            if(player.AccountID!=thiz.playerMe.AccountID){
                //chia card ao
                var playerView = thiz.getPlayviewWithID(player.AccountID);
                if (playerView != null  ) {
                   if(player.Status == 1){
                    if(data.GameSessionStep == 3){
                        //show 
                        var mycard =  player.BaiRac.map(card => card.OrdinalValue); 
                        playerView.latBaiAllNotEffect(mycard);
                 }else{
                     if(isAni){

                         thiz.dealCardForOther(playerView);
                     }else{
                         playerView.showCardlist();
                     }
                 }
                   }
                   

                    playerView.activeAvatar(player.Status);
                    //show Bo Bai bao
                  //  var mycard =  player.BaiRac.map(card => card.OrdinalValue); 
                  //  playerView.updateRemainCard(mycard.length);
                }   
            }else{
                var mycard =  player.BaiRac;
                cc.log("thiz.playerMe"+ thiz.playerMe.AccountID);
                cc.log("thiz.mycard"+ mycard);
             
                thiz.playerViewMe.activeAvatar(player.Status);
                (function(mycard) {
                    setTimeout(() => {
                        thiz.playAnimationDealer();
                        //chia bai
                        if(data.GameSessionStep == 1){
                         
                            if(isAni){
                                thiz.dealCards(mycard);
                                // 
                            }else{
                                thiz.showXepBai(mycard);
                            }
                        }else if(data.GameSessionStep == 3){
                            // cho chia bai
                            var mycardz =  player.BaiRac.map(card => card.OrdinalValue); 
                            thiz.playerViewMe.latBaiAllNotEffect(mycardz);
                        }
                        
                    }, 100);
                })(mycard);
               
            }
           
        }
       

        
    }
    isbackground = false;

     newOrderz: number[] = [8, 9, 10, 11, 12, 3, 4, 5, 6, 7, 0, 1, 2];
    dealCardForOther(playerViewTmpe){
            
     
        var thiz = this;
        var cardClone =  this.cardClone;
        let worldPos = thiz.deskPoient.convertToWorldSpaceAR(cc.Vec2.ZERO);
        var posDealer = this.node.convertToNodeSpaceAR(worldPos);
     
       var lsCardFake = [];
   
        for (var i = 0; i < 13; i++) {

            (()=>{
                var inew  = i;
                
               
                if (!thiz.isbackground) {
                    cc.log("dealCardsFake");
                    var cardNode = cc.instantiate(cardClone);
                    lsCardFake.push(cardNode);
                cardNode.parent =  thiz.node;
                cardNode.setPosition(posDealer);
                  cardNode.setSiblingIndex(this.newOrderz[inew]+100);
                cardNode.rotation = playerViewTmpe.getCardByIndex(inew).rotation;
             
                var posOrigin =  thiz.node.convertToNodeSpaceAR(playerViewTmpe.getCardByIndex(inew).convertToWorldSpaceAR(cc.Vec2.ZERO));  
                    // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
    
                    // cardNode.setScale(cc.v3(-1, 1, 1));
                    let t1 = cc.tween(cardNode).delay(0.02*inew).call(() => {
                        cc.log("vaa" + inew);
                        cardNode.active = true;
                        this.playSound("chiabai",false);
                    });
                    let t2 = cc.tween(cardNode)
                        .to(0.25, { position: cc.v3(posOrigin.x,posOrigin.y,0) });
                    let t3 = cc.tween(cardNode).to(0.1, {
                        // Bind position
                        scaleX: 0,
                    }
                    ).call(() => {
                        setTimeout(() => {
                            cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
                        }, 0); 
                    });
    
                    let t4 = cc.tween(cardNode).delay(0.1).to(0.1, {
                        // Bind position
                        // scaleX: 1,
                    }
                    ).call(()=>{
                     
                        if(inew == 12){
                            lsCardFake.forEach(element => {
                                element.destroy();
                            });
                            playerViewTmpe.showCardlist();
                        }

                    });
    
                    cc.tween(cardNode).sequence(t1, t2, t3, t4).start();
    
    
                }
                
            })();
           


        }
    }
   
    showXepBai(BaiRac){

        this.nodeXepBai.active = true;
       
        this.lsCardMB.fillData(BaiRac);
        this.actCheckChi();
        this.playerViewMe.showCardlistMe(BaiRac);
        this.playerViewMe.cardList.active = false;
      
    }

    onGamePause() {
        this.isbackground = true;
        // Xử lý khi game bị đưa xuống nền
    }
    
    onGameResume() {
        this.isbackground = false;
        // Xử lý khi game quay lại từ nền
    }
    onFinishDealCard(BaiRac){
        this.showXepBai(BaiRac);
        // thiz.playerViewMe.cardList.active = true;
        this.lsCardFake.forEach(element => {
            element.destroy();
        });
        this.lsCardFake = [];
    }
    lsCardFake = [];
      dealCards(BaiRac) {
            cc.log("dealCards");
            var cards =  BaiRac.map(card => card.OrdinalValue); 
            var thiz = this;
            var cardClone =  this.cardClone;
            let worldPos = thiz.deskPoient.convertToWorldSpaceAR(cc.Vec2.ZERO);
            var posDealer = this.node.convertToNodeSpaceAR(worldPos);
            const newOrder: number[] = [8, 9, 10, 11, 12, 3, 4, 5, 6, 7, 0, 1, 2];

    
            for (var i = 0; i < cards.length; i++) {
    
                (()=>{
                    var inew  = i;
                    
                   
                    if (!thiz.isbackground) {
                        var cardNode = cc.instantiate(cardClone);
                    
                    cardNode.parent =  thiz.node;
                    cardNode.setPosition(posDealer);
                    cardNode.setSiblingIndex(newOrder[inew]+100);
                    cardNode.rotation = thiz.playerViewMe.getCardByIndex(inew).rotation;
                    thiz.lsCardFake.push(cardNode);
                    var posOrigin =  thiz.node.convertToNodeSpaceAR(thiz.playerViewMe.getCardByIndex(inew).convertToWorldSpaceAR(cc.Vec2.ZERO)); 
                        // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.frameCardBack;
        
                        cardNode.setScale(cc.v3(-1, 1, 1));
                        let t1 = cc.tween(cardNode).delay(0.02*inew).call(() => {
                            // cc.log("vl" + posOrigin);
                            cardNode.active = true;
                        });
                        let t2 = cc.tween(cardNode)
                            .to(0.25, { position: posOrigin });
                        let t3 = cc.tween(cardNode).to(0.1, {
                            // Bind position
                            scaleX: 0,
                        }
                        ).call(() => {
                            // setTimeout(() => {
                                cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                            // }, 0); 
                        });
        
                        let t4 = cc.tween(cardNode).delay(0.1).to(0.1, {
                            // Bind position
                            scaleX: 1,
                        }
                        ).call(()=>{
                         
                            if(inew == cards.length-1){
                                cc.log("finihs delcar " + inew + "=---" + (cards.length-1));
                                thiz.onFinishDealCard(BaiRac);
                            }

                        });
        
                        cc.tween(cardNode).sequence(t1, t2, t3, t4).start();
        
        
                    }
                    else {
                        
                        // cardNode.active = true;
                        // cardNode.getComponent(cc.Sprite).spriteFrame = thiz.arrCardSprite.children[cards[inew]].getComponent(cc.Sprite).spriteFrame;
                       
                        // cardNode.setPosition(posOrigin);
                        cc.log("finihs delcar " + inew + "=---" + (cards.length-1));
                        if(inew == cards.length-1){
                            cc.log("finihs delcar");
                            thiz.onFinishDealCard(BaiRac);
                        }
                    }
                })();
               
    
    
            }
        }

    createEffectMoney(playview, element) {
        var txtMoney = cc.instantiate(this.lblClone.node);
        txtMoney.parent = playview.node;
        txtMoney.active = true;
        txtMoney.position = cc.v3(0, 0, 0);
        if (element > 0) {
            txtMoney.getComponent(cc.Label).string = "+" + Utils.formatMoney(Math.abs(element));
              cc.tween(txtMoney).to(1.5, { position: cc.v3(0, 100, 0) }).delay(1.5).call(() => {
            txtMoney.removeFromParent();
        }).start();
        } else  if (element <0) {
            // txtMoney.color = cc.color(255, 0, 0, 255);
            txtMoney.getComponent(cc.Label).string = "-" + Utils.formatMoney(Math.abs(element));
              cc.tween(txtMoney).to(1.5, { position: cc.v3(0, 100, 0) }).delay(1.5).call(() => {
            txtMoney.removeFromParent();
        }).start();
        }
      
    }
  @property(cc.Node)
  btnXepLai:cc.Node = null;
    startActionTimer(idplaer,time,allowAcction){

        console.log("Timemer======" + idplaer);
        console.log("Timemer======" + time);
        var isStart = false;
       for (let index = 0; index < allowAcction.length; index++) {
               const element = allowAcction[index];
               if(idplaer == this.playerMe.AccountID){
                  
                   if(element == 1){
                  
                   }
                   if(element == 2 ){
                    this.btnXepLai.active = false;
                   }
                   
                   if(element == 3){
                    this.btnXepLai.active = false;
                    
                      
                   }
       //             Chặt bài: 3
       // Thắng-Thua: 4
       
               }
                   if(element == -1){ //startgame
                       // this.btnBo.active = true;
                       this.lblThongBao.node.parent.active = true;
                       this.lblThongBao.string = App.instance.getTextLang("me7");
                       this.listPlayerView.forEach(element => {
                        element.resetResulft();
                    });
                       
                      
                   }else if(element == 1000) {
                    isStart = true;
                       this.playSound("start",false);
                       this.lblThongBao.node.parent.active = true;
                       this.lblThongBao.string = App.instance.getTextLang("me26");
            
                       this.listPlayerView.forEach(element => {
                           element.resetResulft();
                       });
                       
                   }
               }
   
  
   
    var playerView = this.getPlayviewWithID(idplaer );
    if (playerView == null) return;
    this.listPlayerView.forEach(element => {
        element.startCountDown(0);
    });
    playerView.startCountDown(time );

  
   

    

    }

    actXepLai(){
               App.instance.inactivityTimer = 0;
        this.nodeXepBai.active = true;
        this.playerViewMe.cardList.active = false;
        this.btnXepLai.active = false;
    }

    startCountdown(duration) {
        let remainingTime = duration; // Thời gian đếm ngược (giây)
        cc.log("start dem giay");
        this.lblGiay.string =  remainingTime + " " +App.instance.getTextLang("ca146"); 
        this.lblGiay.unscheduleAllCallbacks();
        this.lblGiay.schedule(() => {
            remainingTime--;
            if (remainingTime > 0) {
               
                cc.log("start dem giay");
                this.lblGiay.string =  remainingTime + " " + App.instance.getTextLang("ca146"); 
                // if(remainingTime<3){
                //     this.lblGiay.node.color = cc.Color.RED; 
                // }
            } else {
                this.lblGiay.unscheduleAllCallbacks(); // Dừng đếm ngược khi hết thời gian
                // this.lblTime.string = "Time's up!";
            }
        }, 1);
    }

    
    protected update(dt: number): void {
        
    }
   
    playerLeave(idLeave,data){

        this.playSound("leave_room",false);
        if(idLeave == this.playerMe.AccountID){
            this.dataRoom = null;
                 this.isGotolobby = true;
                  if(data && data.code == 11007){
                var stringaler = App.instance.getTextLang("me11007");
                App.instance.ShowAlertDialog(Utils.formatString(stringaler, Utils.formatNumber(data.prms[0]),data.prms[0]==1? App.instance.getTextLang("hi25"):"Tipzo"));
              }
              this.node.removeFromParent();
                           App.instance.gotoLobby();
            return;
        }
        for (var i = 0; i < this.listPlayer.length; i++) {
            if (this.listPlayer[i]['AccountID'] == idLeave) {
                this.listPlayer.splice(i, 1);
                break;
            }
        }
        for (var i = 0; i < this.listPlayerView.length; i++) {
            if (this.listPlayerView[i].player != null && this.listPlayerView[i].player.AccountID == idLeave) {
                this.listPlayerView[i].updateInfor(null);
                break;
            }
        }
        // this.updateDataRoom(data);

    }
    actExitLobby(){
    
    }


    actCheckChi(){
App.instance.inactivityTimer = 0;
        CardGameSignalRClient.getInstance().send('CheckChi', [this.lsCardMB.getIdCards()], (data) => {
            cc.log(data,this.lsCardMB.getIdCards());
                   }); 
    }


    actDone(){
               App.instance.inactivityTimer = 0;
        CardGameSignalRClient.getInstance().send('KetThuc', [this.lsCardMB.getIdCards()], (data) => {
            cc.log(data);
            if(data){
                this.nodeXepBai.active = false;
                this.playerViewMe.cardList.active = true;
                this.btnXepLai.active = true;
                //update 
                this.playerViewMe.showCardlistMe2(this.lsCardMB.getIdCards());
                this.playerViewMe.showXepXong();
            }
                   }); 
    }
    isGotolobby = false;
    isRegLeave = false;
    actLeaveGame(){
        this.playSound("click",false);
         if( this.playerMe.isRegExit){
                              CardGameSignalRClient.getInstance().send('UnregisterLeaveRoom', [], (data) => {
                                  cc.log(data);
                                    this.isRegLeave = false;
                                  if(data && !this.isGotolobby){
                                      App.instance.showToast(App.instance.getTextLang("me9"));
                                  }
                                         }); 
                  
                          }else{
                              CardGameSignalRClient.getInstance().send('LeaveGame', [], (data) => {
                                this.isRegLeave = true;
                                  cc.log(data);
                                  if(data && !this.isGotolobby){
                                      App.instance.showToast(App.instance.getTextLang("me8"));
                                  }
                                         });
                          }
                 

       
    }

    playerJoin(data){
      //  {"type":1,"target":"playerJoin","arguments":[{"HandCards":[],"OrderInGame":1,"EmptyHand":
      // true,"AccountID":"100085:15","Account":{"AvatarID":0,"Avatar":"0","AccountID":"100085:
      // 15","NickName":"[X]tommyt1","OriginNickname":"tommyt1","GoldBalance":********,"CoinBalance"
      // :0,"MerchantID":0,"SourceID":1,"CurrencyID":1,"PortalID":15},"Status":0,"Position":1,"Regi
      // sterLeaveRoom":false,"ConnectionStatus":1}]}
      this.listPlayer.push(data);
      this.fillPlayerToSlot();
    }

    // responsvive==================
    dataRoom;
    MoneyType = 1;
    onHandleJoin(data,extra){
        // {"type":1,"target":"joinGame","arguments":[{"Rule":0,"GameLoop":{"TimerPaused":true,"Elapsed":0,"CurrTurnCards":[],
        // "GameState":{"DefaultAccount":null,"ClientStates":[]},"SessionResult":{"ResultList":[]}},
        // "Players":{"100053:15":{"HandCards":[],"OrderInGame":-1,"EmptyHand":true,"AccountID":"100053:15",
        // "Account":{"AvatarID":0,"Avatar":"0","AccountID":"100053:15","NickName":"
        // [X]tommy2","OriginNickname":"tommy2","GoldBalance":********,"CoinBalance":5700000,"M
        // erchantID":0,"SourceID":1,"CurrencyID":1,"PortalID":15},"Status":0,"Position":0,"Regi
        // sterLeaveRoom":false,"ConnectionStatus":1}},"IsPlaying":false,"OwnerId":"100053:15","Positions"
        // :["100053:15",null,null,null],"LeaveGameList":[],"CountActivePlayer":0,"MaxAllow":4,"Name":"1",
        // "GameId":7,"MaxPlayer":4,"MinBet":1000,"MaxBet":0,"RuleDescription":"","IsPrivate":false,"BetStep":0,
        // "MoneyType":1,"CurrentGameLoopId":0,"CurrencyId":1,"CountPlayers":1},{"totalTime":15,"time":0,"state":
        // {"accountId":null,"allowedActions":[]}}]}{"type":3,"invocationId":"1","result":1}
        cc.log("==============");
        cc.log(JSON.stringify(data));

         this.listPlayerView.forEach(element => {
            element.MoneyType = data.MoneyType;
        });
      this.lblTable.string = ( data.MoneyType == 1 ? App.instance.getTextLang("tb112") : App.instance.getTextLang("tb113"))+ ": " + data.Name;
      this.lblTable2.string = App.instance.getTextLang("iap38")+ ": " + Utils.formatMoney(data.MinBet) + (data.MoneyType == 1?" Tipzo" :" Coin") ;

  
      this.lblTable3.string =  App.instance.getTextLang("ca95") + " " + (data.CurrentGameLoopId==-1?"":data.CurrentGameLoopId.toString()); 
      this.vipTable.active = data.MoneyType != 1 ?true:false;
      this.MoneyType = data.MoneyType;
      this.vipTable.parent.children[1].color =new cc.Color().fromHEX(data.MoneyType != 1 ?'#513b21':"#003533"); 
      
        this.listPlayer = [];
        this.listPlayerView.forEach(element => {
            element.resetAll();
        });
        
        var dataRoom = data;
        const playerIDs = Object.keys(dataRoom.Players);
        this.playSound("join_room",false);
        // Duyệt danh sách bằng vòng lặp for bình thường
        for (let i = 0; i < playerIDs.length; i++) {
            const playerID = playerIDs[i];
            const player = dataRoom.Players[playerID];
            var isExits = false;
            for (var j = 0; j < this.listPlayer.length; j++) {
                if(playerID == this.listPlayer[j]['AccountID']){
                    isExits = true;
                    break;
                }
               
            }
            if(!isExits){
                this.listPlayer.push(player);
            }
            // console.log(`Player ID: ${playerID}`);
            // console.log(`Nickname: ${player.Account.NickName}`);
            // console.log(`Gold: ${player.Account.GoldBalance}`);
            // console.log(`Status: ${player.Status}`);
            // console.log("-----------------------");
        }
        this.updateDataRoom(dataRoom);
        this.fillPlayerToSlot();

        this.startGame(dataRoom,false);
        if(data.CountActivePlayer ==0){
            this.lblThongBao.node.parent.active = true;
            this.lblThongBao.string = App.instance.getTextLang("me7");
        }

        if(extra && extra.accountId){
            this.startActionTimer(extra.accountId, extra.time,extra.allowedActions) ;
        }
    }
    updateDataRoom(dataRoom){
        this.dataRoom = dataRoom;
        var thiz = this;
        this.listPlayer.forEach(element => {
            element["isOwner"] = false;
            if(dataRoom.OwnerId == element.AccountID){
                element["isOwner"] = true;
            }
            const firstPart = element.AccountID.substring(0, element.AccountID.indexOf(":"))
            if (firstPart == Configs.Login.UserId) {
                thiz.playerMe = element;

            }
        });
        
    }

    actDoiChi(){
        App.instance.inactivityTimer = 0;
      this.lsCardMB.swapchi();
        
    }
    
    getPlayviewWithID(userId) {
        for (let index = 0; index < this.listPlayerView.length; index++) {
            const element = this.listPlayerView[index];
            if (element.player != null && element.player.AccountID == userId) {
                return element;
            }
        }
        return null;
    }
   
    fillPlayerToSlot() {
        var thiz = this;
       
        this.listPlayer.forEach(element => {
            var slot = this.getSlotNew(element['Position']);

            thiz.listPlayerView[slot].updateInfor(element);
        });

        
    }
    

   

   
  

    getSlotNew(slot) {
        var slotNew = slot - this.playerMe.Position;
        if(slotNew<0){
            slotNew =this.maxPlayer + slotNew;
        }
        return slotNew;
    }
    getIDCard(card) {
        
        return card.idCard;
    }
    // update (dt) {}
}
